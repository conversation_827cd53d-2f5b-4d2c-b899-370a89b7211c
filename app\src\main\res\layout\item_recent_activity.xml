<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="@dimen/analytics_card_elevation_small"
    app:cardBackgroundColor="@color/surface">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- Activity Icon -->
        <TextView
            android:id="@+id/tvActivityIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:text="📧"
            android:textSize="20sp"
            android:gravity="center"
            android:background="@drawable/circle_background"
            android:layout_marginEnd="12dp" />

        <!-- Activity Details -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvActivityTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Email Detected"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/tvActivityDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Spam email detected from unknown sender"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:maxLines="2"
                android:ellipsize="end"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <!-- Time and Status -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="end">

            <TextView
                android:id="@+id/tvActivityTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2m ago"
                android:textSize="11sp"
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/tvActivityStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="BLOCKED"
                android:textSize="10sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:background="@drawable/status_badge_background"
                android:padding="4dp"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
