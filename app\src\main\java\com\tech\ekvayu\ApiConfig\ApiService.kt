package com.tech.ekvayu.ApiConfig

import com.tech.ekvayu.ActivityGraph.ActivityGraphResponse
import com.tech.ekvayu.Dispute.DisputeMailistResponse
import com.tech.ekvayu.Dispute.DisputeRaiseRequest
import com.tech.ekvayu.Dispute.DisputeRaiseResponse
import com.tech.ekvayu.Request.CommonRequest
import com.tech.ekvayu.Request.PendingMailRequest
import com.tech.ekvayu.Response.EmailResponse
import com.tech.ekvayu.Response.HashResponse
import com.tech.ekvayu.Response.PedingMailRes
import com.tech.ekvayu.Response.SpamMailResponse
import com.tech.ekvayu.Sms.AiResponse.CheckSmsAiRequest
import com.tech.ekvayu.Sms.AiResponse.CheckSmsAiResponse
import com.tech.ekvayu.Sms.HashId.GetHashRequest
import com.tech.ekvayu.Sms.HashId.GetHashResponse
import com.tech.ekvayu.Sms.CheckMessage.SmsCheckReponse
import com.tech.ekvayu.Sms.CheckMessage.SmsCheckRequest
import com.tech.ekvayu.Sms.SmsSpamList.GetSmsSpamRequest
import com.tech.ekvayu.Sms.SmsSpamList.GetSmsSpamResponse
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part

interface ApiService {

    /// Api implementation for Gamil

    @Multipart
    @POST("plugin/get-email-hash/")
    fun getHashKey(
        @Part("receiverEmail") email: RequestBody,
        @Part("senderEmail") senderEmail: RequestBody,
        @Part file: MultipartBody.Part,
        @Part("body") body: RequestBody,
        @Part("cc") cc: RequestBody,
        @Part("bcc") bcc: RequestBody,
        @Part("subject") subject: RequestBody,
    ): Call<HashResponse>

    @Multipart
    @POST("plugin/check-email/")
    fun uploadFile(
        @Part("receiverEmail") email: RequestBody,
        @Part("senderEmail") senderEmail: RequestBody,
        @Part file: MultipartBody.Part,
        @Part("body") body: RequestBody,
        @Part("cc") cc: RequestBody,
        @Part("bcc") bcc: RequestBody,
        @Part("subject") subject: RequestBody,
    ): Call<EmailResponse>


    @Headers("Content-Type: application/json")
    @POST("plugin/pending-status-check/")
    fun pendingMailStatusAi(
        @Body request: PendingMailRequest
    ): Call<PedingMailRes>


    @Headers("Content-Type: application/json")
    @POST("plugin/spam-email/")
    fun getSpamMail(
        @Body request: CommonRequest
    ): Call<SpamMailResponse>


    @Headers("Content-Type: application/json")
    @GET("resolved-dispute-data/")
    fun getDisputelist(
    ): Call<DisputeMailistResponse>
 
    @Headers("Content-Type: application/json")
    @POST("plugin/raise-dispute/")
    fun disputeRaise(
        @Body request: DisputeRaiseRequest
    ): Call<DisputeRaiseResponse>


    @Headers("Content-Type: application/json")
    @POST("plugin/graph-count/")
    fun getActivtyGraph(
        @Body request: CommonRequest
    ): Call<ActivityGraphResponse>

   /// Api Implementation for sms

    @Headers("Content-Type: application/json")
    @POST("plugin/get-msg-hash/")
    fun getSMSHashKey(
        @Body request: GetHashRequest
    ): Call<GetHashResponse>



    @Headers("Content-Type: application/json")
    @POST("plugin/check-sms-message/")
    fun checkSms(
        @Body request: SmsCheckRequest
    ): Call<SmsCheckReponse>

    @Headers("Content-Type: application/json")
    @POST("plugin/pending-status-check-msg/")
    fun getPendingStatuscheckMsg(
        @Body request: CheckSmsAiRequest
    ): Call<CheckSmsAiResponse>


    @Headers("Content-Type: application/json")
    @POST("plugin/spam-msg/")
    fun getSmsSpamlist(
        @Body request: GetSmsSpamRequest
    ): Call<GetSmsSpamResponse>


}