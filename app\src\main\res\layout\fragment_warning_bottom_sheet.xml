<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".BottomSheet.WarningBottomSheetFragment">


    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:cardCornerRadius="@dimen/_10sdp"
        android:layout_margin="@dimen/_15sdp"
        android:elevation="0dp"
        app:cardElevation="0dp"
        app:cardBackgroundColor="@color/light_grey"

        >


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">


            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivIcon"
                android:layout_width="@dimen/_35sdp"
                android:layout_height="@dimen/_35sdp"
                android:src="@drawable/icon_secure_phishing"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginVertical="@dimen/_15sdp"
                />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvMailAccess"
                app:layout_constraintTop_toBottomOf="@+id/ivIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Mail Access Required"
                android:textSize="@dimen/_13sdp"
                android:fontFamily="@font/roboto_semi_bold"
                android:textColor="@color/dark_grey"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginTop="@dimen/_10sdp"
                />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvSuggetion"
                app:layout_constraintTop_toBottomOf="@+id/tvMailAccess"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/to_extract_email_details_securely"
                android:textSize="@dimen/_9sdp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textColor="@color/black"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:layout_marginTop="@dimen/_10sdp"
                />


            <androidx.appcompat.widget.AppCompatButton
                app:layout_constraintBottom_toBottomOf="parent"
                android:id="@+id/btPermission"
                app:layout_constraintTop_toBottomOf="@+id/tvSuggetion"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:layout_margin="@dimen/_12sdp"
                android:elevation="@dimen/_10sdp"
                android:paddingStart="@dimen/_15sdp"
                android:paddingEnd="@dimen/_15sdp"
                android:textAllCaps="false"
                android:visibility="visible"
                android:background="@drawable/bg_button_app_color"
                android:fontFamily="@font/roboto_semi_medium"
                android:textSize="@dimen/_10sdp"
                android:textColor="@color/white"
                android:text="@string/permission"
                />




        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>


</ScrollView>
