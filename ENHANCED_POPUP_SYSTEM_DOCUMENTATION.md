# Enhanced Popup System Documentation

## Overview
The Ekvayu app now features a completely redesigned popup system that provides a modern, attractive, and user-friendly experience for displaying security alerts and email analysis results.

## Key Improvements

### 🎨 **Visual Design**
- **Modern Material Design**: Clean, contemporary interface following Material Design 3 principles
- **Adaptive Theming**: Automatically adapts to light/dark mode settings
- **Status-Based Colors**: Different color schemes for safe, unsafe, pending, warning, and error states
- **Smooth Animations**: Entrance and exit animations for better user experience
- **Improved Typography**: Better text hierarchy and readability

### 🚀 **Enhanced UX Features**
- **Multiple Action Buttons**: Primary and secondary actions (e.g., "Close" and "View Details")
- **Progress Indicators**: Visual feedback for ongoing analysis
- **Auto-Dismiss**: Configurable automatic dismissal for non-critical alerts
- **Status Indicators**: Clear visual representation of email security status
- **Responsive Layout**: Optimized for different screen sizes

### 🔧 **Technical Improvements**
- **Centralized Management**: Single `PopupManager` class handles all popup logic
- **Type Safety**: Enum-based popup types prevent errors
- **Memory Management**: Proper cleanup and leak prevention
- **Error Handling**: Robust error handling with fallbacks
- **Accessibility**: Better accessibility support for screen readers

## Usage Examples

### Basic Usage

```kotlin
// Show a safe email popup
PopupManager.showSafeEmailPopup(
    context = context,
    message = "Email content verified as safe. No threats detected.",
    onClose = { 
        Log.d("Popup", "Safe email popup closed") 
    }
)

// Show an unsafe email popup with details option
PopupManager.showUnsafeEmailPopup(
    context = context,
    reasons = "Suspicious links detected in email content.",
    onClose = { 
        Log.d("Popup", "Unsafe email popup closed") 
    },
    onViewDetails = { 
        // Navigate to detailed threat analysis
        showThreatDetailsScreen()
    }
)

// Show pending analysis popup
PopupManager.showPendingAnalysisPopup(
    context = context,
    message = "Analyzing email content for potential threats..."
)
```

### Advanced Configuration

```kotlin
// Custom popup with full configuration
PopupManager.showPopup(
    context = context,
    config = PopupManager.PopupConfig(
        type = PopupManager.PopupType.WARNING,
        title = "Suspicious Activity",
        subtitle = "Potential phishing attempt detected",
        message = "This email contains elements commonly found in phishing attacks. Please verify the sender before taking any action.",
        primaryButtonText = "Block Email",
        secondaryButtonText = "Mark as Safe",
        showProgress = false,
        autoDismissDelay = 0L, // No auto dismiss
        onPrimaryAction = {
            blockEmail()
        },
        onSecondaryAction = {
            markEmailAsSafe()
        },
        onDismiss = {
            logUserAction("popup_dismissed")
        }
    )
)
```

## Popup Types

### 1. **SAFE** 🟢
- **Use Case**: Email verified as safe
- **Color Scheme**: Green tones
- **Animation**: Success checkmark
- **Auto-Dismiss**: 3 seconds (configurable)

### 2. **UNSAFE** 🔴
- **Use Case**: Threats detected in email
- **Color Scheme**: Red tones
- **Animation**: Warning/danger indicator
- **Actions**: Close, View Details

### 3. **PENDING** 🟡
- **Use Case**: Analysis in progress
- **Color Scheme**: Orange tones
- **Animation**: Loading/waiting indicator
- **Features**: Progress bar, cancel option

### 4. **WARNING** 🟠
- **Use Case**: Suspicious but not confirmed threat
- **Color Scheme**: Yellow/orange tones
- **Animation**: Caution indicator
- **Actions**: Multiple user choices

### 5. **ERROR** ❌
- **Use Case**: System errors or failures
- **Color Scheme**: Red tones
- **Animation**: Error indicator
- **Actions**: Retry, dismiss

## Integration with Accessibility Services

### Gmail Service Integration
```kotlin
private fun setResponsePopup(unsafeReasons: String?, mailStatus: String, email: String, hashId: String) {
    when (mailStatus.lowercase()) {
        "safe" -> PopupManager.showSafeEmailPopup(applicationContext, unsafeReasons ?: "Safe")
        "unsafe" -> PopupManager.showUnsafeEmailPopup(applicationContext, unsafeReasons ?: "Unsafe")
        "pending" -> PopupManager.showPendingAnalysisPopup(applicationContext)
    }
}
```

### SMS Service Integration
```kotlin
private fun showSmsAlert(analysisResult: SmsAnalysisResult) {
    when (analysisResult.riskLevel) {
        RiskLevel.HIGH -> PopupManager.showUnsafeEmailPopup(
            context = this,
            reasons = "Suspicious SMS content detected: ${analysisResult.reasons.joinToString(", ")}"
        )
        RiskLevel.MEDIUM -> PopupManager.showPopup(
            context = this,
            config = PopupManager.PopupConfig(
                type = PopupManager.PopupType.WARNING,
                title = "SMS Security Alert",
                subtitle = "Medium risk detected",
                message = analysisResult.reasons.joinToString("\n")
            )
        )
    }
}
```

## Customization Options

### Theme Integration
The popup system automatically adapts to the app's theme:
- **Light Mode**: Bright backgrounds with dark text
- **Dark Mode**: Dark backgrounds with light text
- **System Mode**: Follows device theme settings

### Color Customization
Add custom colors in `colors.xml`:
```xml
<!-- Custom popup colors -->
<color name="popup_success">#4CAF50</color>
<color name="popup_warning">#FF9800</color>
<color name="popup_error">#F44336</color>
```

### Animation Customization
Modify animation duration and style in `PopupManager`:
```kotlin
private fun animatePopupEntrance(view: View) {
    // Custom animation implementation
    view.animate()
        .alpha(1f)
        .scaleX(1f)
        .scaleY(1f)
        .setDuration(400) // Custom duration
        .setInterpolator(OvershootInterpolator()) // Custom interpolator
        .start()
}
```

## Best Practices

### 1. **Appropriate Popup Types**
- Use `SAFE` for confirmed safe content with auto-dismiss
- Use `UNSAFE` for confirmed threats requiring user action
- Use `PENDING` for ongoing analysis with progress indication
- Use `WARNING` for suspicious content requiring user decision

### 2. **Message Guidelines**
- Keep messages concise but informative
- Use clear, non-technical language
- Provide actionable information
- Include specific threat details when available

### 3. **Button Configuration**
- Primary button for main action (usually dismissal)
- Secondary button for additional options
- Use clear, action-oriented button text
- Limit to 2 buttons maximum for clarity

### 4. **Performance Considerations**
- Always dismiss existing popups before showing new ones
- Use auto-dismiss for non-critical notifications
- Implement proper error handling for overlay permissions
- Clean up resources properly to prevent memory leaks

## Migration from Old Popup System

### Before (Old System)
```kotlin
// Old popup implementation
val binding = LayoutValidationBinding.inflate(layoutInflater)
binding.lvWarning.setAnimation(R.raw.safe)
binding.tvMessage.text = message
windowManager.addView(binding.root, params)
```

### After (New System)
```kotlin
// New popup implementation
PopupManager.showSafeEmailPopup(
    context = context,
    message = message,
    onClose = { /* handle close */ }
)
```

## Troubleshooting

### Common Issues
1. **Popup not showing**: Check overlay permissions
2. **Animation issues**: Verify Lottie animation files exist
3. **Theme not applied**: Ensure theme colors are defined
4. **Memory leaks**: Always use application context for popups

### Debug Logging
Enable debug logging to troubleshoot issues:
```kotlin
Log.d("PopupManager", "Showing popup: ${config.type}")
```

---

**Document Version**: 1.0  
**Last Updated**: December 2024  
**Next Review**: March 2025
