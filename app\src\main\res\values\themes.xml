<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme (Light Mode) -->
    <style name="Base.Theme.Ekvayu" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary Colors -->
        <item name="colorPrimary">@color/app_color</item>
        <item name="colorPrimaryVariant">@color/app_color_dark</item>
        <item name="colorOnPrimary">@color/white</item>

        <!-- Secondary Colors -->
        <item name="colorSecondary">@color/app_color_light</item>
        <item name="colorSecondaryVariant">@color/app_color</item>
        <item name="colorOnSecondary">@color/white</item>

        <!-- Background Colors -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>

        <!-- Status Bar - Transparent for edge-to-edge -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>

        <!-- Navigation Bar -->
        <item name="android:navigationBarColor">@color/background</item>
        <item name="android:windowLightNavigationBar">true</item>

        <!-- Edge-to-edge display -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>

        <!-- Window Background -->
        <item name="android:windowBackground">@color/background</item>

        <!-- Text Colors -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>

        <!-- Other Colors -->
        <item name="colorError">@color/red</item>
        <item name="colorOnError">@color/white</item>
    </style>

    <style name="Theme.Ekvayu" parent="Base.Theme.Ekvayu" />

    <!-- Card Style -->
    <style name="CardStyle">
        <item name="android:background">@color/card_background</item>
        <item name="android:elevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>

    <!-- Button Styles -->
    <style name="ButtonPrimary" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/app_color</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="ButtonSecondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/app_color</item>
        <item name="android:textColor">@color/app_color</item>
    </style>

    <!-- Header Styles -->
    <style name="HeaderStyle">
        <item name="android:background">@color/surface</item>
        <item name="android:elevation">4dp</item>
        <item name="android:paddingTop">@dimen/_8sdp</item>
        <item name="android:paddingBottom">@dimen/_4sdp</item>
    </style>

    <style name="HeaderTitleStyle">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">14sp</item>
        <item name="fontFamily">@font/roboto_semi_bold</item>
    </style>

    <!-- Text Size Styles -->
    <style name="TextAppearance.App.Headline" parent="TextAppearance.Material3.HeadlineSmall">
        <item name="android:textSize">18sp</item>
        <item name="fontFamily">@font/roboto_semi_bold</item>
    </style>

    <style name="TextAppearance.App.Title" parent="TextAppearance.Material3.TitleMedium">
        <item name="android:textSize">16sp</item>
        <item name="fontFamily">@font/roboto_semi_medium</item>
    </style>

    <style name="TextAppearance.App.Body" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textSize">14sp</item>
        <item name="fontFamily">@font/roboto_semi_regular</item>
    </style>

    <style name="TextAppearance.App.Caption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textSize">12sp</item>
        <item name="fontFamily">@font/roboto_semi_regular</item>
    </style>

    <style name="TextAppearance.App.Label" parent="TextAppearance.Material3.LabelMedium">
        <item name="android:textSize">10sp</item>
        <item name="fontFamily">@font/roboto_semi_regular</item>
    </style>

    <!-- Bottom Navigation Text Style -->
    <style name="BottomNavigationTextStyle" parent="TextAppearance.Material3.LabelMedium">
        <item name="android:textSize">10sp</item>
        <item name="fontFamily">@font/roboto_semi_regular</item>
    </style>

    <style name="HeaderButtonStyle">
        <item name="cardBackgroundColor">@color/app_color</item>
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">2dp</item>
    </style>

    <style name="HeaderThemeButtonStyle">
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">2dp</item>
    </style>
</resources>