package com.tech.ekvayu.Response

import com.google.gson.annotations.SerializedName

data class PedingMailRes(
    @SerializedName("message" ) var message : String? = null,
    @SerializedName("status"  ) var status  : String? = null,
    @SerializedName("code"    ) var code    : Int?    = null,
    @SerializedName("data"    ) var data    : Data?   = Data()
)


data class Data (
    @SerializedName("eml_status"      ) var emlStatus      : String? = null,
    @SerializedName("hashId"          ) var hashId         : String? = null,
    @SerializedName("email"           ) var email          : String? = null,
    @SerializedName("unsafe_reasons"  ) var unsafeReasons  : String? = null,
    @SerializedName("dispute_counter" ) var disputeCounter : Int?    = null,
    @SerializedName("code"            ) var code           : Int?    = null,
    @SerializedName("message"         ) var message        : String? = null
)