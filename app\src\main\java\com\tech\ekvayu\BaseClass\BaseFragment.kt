package com.tech.ekvayu.BaseClass

import android.util.Log
import androidx.fragment.app.Fragment
import com.tech.ekvayu.Activities.DashboardActivity
import com.tech.ekvayu.BottomSheet.LoginBottomSheetFragment

/**
 * Base fragment class with common navigation functionality
 */
abstract class BaseFragment : Fragment() {
    
    protected val TAG: String = this::class.java.simpleName
    
    /**
     * Get the dashboard activity safely
     */
    protected fun getDashboardActivity(): DashboardActivity? {
        return activity as? DashboardActivity
    }
    
    /**
     * Navigate to another fragment with enhanced navigation
     */
    protected fun navigateToFragment(
        fragment: Fragment,
        tag: String? = null,
        clearStack: Boolean = false,
        useAnimation: Boolean = true
    ) {
        Log.d(TAG, "🧭 Navigating to fragment: ${fragment.javaClass.simpleName}")
        
        getDashboardActivity()?.showFragment(
            fragment = fragment,
            tag = tag,
            clearStack = clearStack,
            useAnimation = useAnimation
        )
    }
    
    /**
     * Navigate back to home
     */
    protected fun navigateToHome() {
        Log.d(TAG, "🏠 Navigating to home")
        getDashboardActivity()?.navigateToHome()
    }
    
    /**
     * Navigate back to previous fragment
     */
    protected fun navigateBack(): Boolean {
        Log.d(TAG, "⬅️ Navigating back")
        return getDashboardActivity()?.let { activity ->
            if (activity.canGoBack()) {
                activity.onBackPressedDispatcher.onBackPressed()
                true
            } else {
                false
            }
        } ?: false
    }
    
    /**
     * Navigate to specific fragment by tag
     */
    protected fun navigateToFragmentByTag(tag: String): Boolean {
        Log.d(TAG, "🔍 Navigating to fragment by tag: $tag")
        return getDashboardActivity()?.navigateToFragmentByTag(tag) ?: false
    }
    
    /**
     * Check if we can go back
     */
    protected fun canGoBack(): Boolean {
        return getDashboardActivity()?.canGoBack() ?: false
    }
    
    /**
     * Get current fragment stack info for debugging
     */
    protected fun getFragmentStackInfo(): String {
        return getDashboardActivity()?.getFragmentStackInfo() ?: "No activity available"
    }
    
    /**
     * Log fragment lifecycle events
     */
    override fun onResume() {
        super.onResume()
        Log.d(TAG, "📱 Fragment resumed: ${this.javaClass.simpleName}")
    }
    
    override fun onPause() {
        super.onPause()
        Log.d(TAG, "⏸️ Fragment paused: ${this.javaClass.simpleName}")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "🔚 Fragment destroyed: ${this.javaClass.simpleName}")
    }

   protected  fun callLoginBottomSheet(){
       val botttomSheet = LoginBottomSheetFragment()
       botttomSheet.setCancelable(true)
       botttomSheet.show(requireActivity().supportFragmentManager, botttomSheet.tag)
   }

}
