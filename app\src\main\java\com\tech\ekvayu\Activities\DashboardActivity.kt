package com.tech.ekvayu.Activities

import android.content.Context
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.FragmentNavigationManager
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.BaseClass.ThemeManager
import com.tech.ekvayu.Fragments.AnalyticsFragment
import com.tech.ekvayu.Fragments.HomeFragment
import com.tech.ekvayu.Fragments.ProfileFragment
import com.tech.ekvayu.Fragments.SecurityFragment
import com.tech.ekvayu.R
import com.tech.ekvayu.databinding.ActivityDashboardBinding
import java.util.UUID

class DashboardActivity : AppCompatActivity(){

    companion object {
        private const val TAG = "DashboardActivity"
    }

    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    private lateinit var binding: ActivityDashboardBinding
    private lateinit var fragmentNavigationManager: FragmentNavigationManager


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding=ActivityDashboardBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initializeFragmentManager()

        setupWindowInsets()

        setupClickListeners()

        setupBottomNavigation()

        setupBackPressHandling()

        if (savedInstanceState == null) {
            fragmentNavigationManager.initializeWithHome(HomeFragment())
            binding.bottomNavigation.selectedItemId = R.id.nav_home
        }
        handleIntentNavigation()


        sharedPrefManager.putString(AppConstant.deviceId,getDeviceId(applicationContext))

    }

    fun getDeviceId(context: Context): String {
        return Settings.Secure.getString(
            context.contentResolver,
            Settings.Secure.ANDROID_ID
        )
    }

    private fun initializeFragmentManager() {
        fragmentNavigationManager = FragmentNavigationManager(
            fragmentManager = supportFragmentManager,
            containerId = R.id.fragmentContainer
        )
        Log.d(TAG, "✅ Fragment navigation manager initialized")
    }

    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.main) { _, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            binding.header.root.updatePadding(top = systemBars.top)
            binding.fragmentContainer.updatePadding(bottom = systemBars.bottom)
            insets
        }
    }

    private fun setupClickListeners() {
        binding.header.cvBack.setOnClickListener { handleBackPress() }
        binding.header.cvTheme.setOnClickListener {
            toggleTheme()
        }
    }

    private fun setupBottomNavigation() {
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.nav_home -> {
                    navigateToBottomNavFragment(HomeFragment(), "HOME")
                    true
                }
                R.id.nav_security -> {
                    navigateToBottomNavFragment(SecurityFragment(), "SECURITY")
                    true
                }
                R.id.nav_analytics -> {
                    navigateToBottomNavFragment(AnalyticsFragment(), "ANALYTICS")
                    true
                }
                R.id.nav_profile -> {
                    navigateToBottomNavFragment(ProfileFragment(), "PROFILE")
                    true
                }
                else -> false
            }
        }
    }

    private fun setupBackPressHandling() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                handleBackPress()
            }
        })
    }

    private fun handleIntentNavigation() {
        // Check if we need to open SMS detection from notification
        val openSmsDetection = intent.getBooleanExtra("open_sms_detection", false)
        if (openSmsDetection) {
            // Navigate to SMS detection fragment
            navigateToSmsDetection()
        }
    }

    private fun handleBackPress() {
        Log.d(TAG, "🔙 Back press detected")

        // Try to navigate back using our fragment manager
        val canGoBack = fragmentNavigationManager.navigateBack()

        if (!canGoBack) {
            // If we can't go back, check if we're not on home tab
            if (binding.bottomNavigation.selectedItemId != R.id.nav_home) {
                // Navigate to home tab
                binding.bottomNavigation.selectedItemId = R.id.nav_home
                Log.d(TAG, "🏠 Navigated to home tab")
            } else {
                // If we're already on home, finish the activity
                Log.d(TAG, "🏠 At home fragment, finishing activity")
                finish()
            }
        }

        // Log current stack state for debugging
        Log.d(TAG, fragmentNavigationManager.getStackInfo())
    }

    private fun toggleTheme() {
        try {
            val currentTheme = ThemeManager.getCurrentTheme()
            val nextTheme = when (currentTheme) {
                ThemeManager.ThemeMode.LIGHT -> ThemeManager.ThemeMode.DARK
                ThemeManager.ThemeMode.DARK -> ThemeManager.ThemeMode.LIGHT
                ThemeManager.ThemeMode.SYSTEM -> ThemeManager.ThemeMode.DARK
            }

            ThemeManager.applyTheme(nextTheme)

            val themeName = ThemeManager.getThemeDisplayName(nextTheme)
            Toast.makeText(this, "Switched to $themeName", Toast.LENGTH_SHORT).show()

            Log.d(TAG, "🎨 Theme switched to: $themeName")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error toggling theme: ${e.message}", e)
            Toast.makeText(this, "Failed to change theme", Toast.LENGTH_SHORT).show()
        }
    }

    fun showFragment(
        fragment: Fragment,
        tag: String? = null,
        clearStack: Boolean = false,
        useAnimation: Boolean = true
    ) {
        Log.d(TAG, "📱 Showing fragment: ${fragment.javaClass.simpleName}")

        fragmentNavigationManager.navigateToFragment(
            fragment = fragment,
            tag = tag,
            addToBackStack = true,
            clearStack = clearStack,
            useAnimation = useAnimation
        )
    }

    private fun navigateToBottomNavFragment(fragment: Fragment, tag: String) {
        Log.d(TAG, "🧭 Bottom nav to: $tag")
        fragmentNavigationManager.navigateToFragment(
            fragment = fragment,
            tag = tag,
            addToBackStack = false,
            clearStack = true,
            useAnimation = false
        )
    }


    fun navigateToHome() {
        Log.d(TAG, "🏠 Navigating to home")
        fragmentNavigationManager.navigateToHome()
    }


    fun navigateToFragmentByTag(tag: String): Boolean {
        Log.d(TAG, "🔍 Navigating to fragment by tag: $tag")
        return fragmentNavigationManager.navigateToFragmentByTag(tag)
    }


    private fun navigateToSmsDetection() {
        try {
            val smsDetectionFragment = Class.forName("com.tech.ekvayu.Fragments.SmsDetectionFragment")
                .getDeclaredConstructor()
                .newInstance() as Fragment

            showFragment(smsDetectionFragment, "SMS_DETECTION", clearStack = true)
            Log.d(TAG, "📱 Navigated to SMS Detection from notification")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error navigating to SMS Detection: ${e.message}", e)
        }
    }

    /**
     * Get current fragment for external access
     */
    fun getCurrentFragment(): Fragment? {
        return fragmentNavigationManager.getCurrentFragment()
    }

    /**
     * Check if we can go back
     */
    fun canGoBack(): Boolean {
        return fragmentNavigationManager.canGoBack()
    }

    /**
     * Get fragment stack info for debugging
     */
    fun getFragmentStackInfo(): String {
        return fragmentNavigationManager.getStackInfo()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "🔚 DashboardActivity destroyed")
    }
}