package com.tech.ekvayu.EkService

import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.PixelFormat
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Toast
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.EmailDetectionHelper
import com.tech.ekvayu.BaseClass.PopupManager
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.R
import com.tech.ekvayu.Request.PendingMailRequest
import com.tech.ekvayu.Response.EmailResponse
import com.tech.ekvayu.Response.HashResponse
import com.tech.ekvayu.Response.PedingMailRes
import com.tech.ekvayu.databinding.LayoutProgressBinding
import com.tech.ekvayu.databinding.LayoutValidationBinding
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.IOException


class YahooAccessibilityService : AccessibilityService() {
    private var currentPopupView: View? = null
    companion object {
        private const val TAG = "YahooAccessibility"
        private const val YAHOO_PACKAGE = "com.yahoo.mobile.client.android.mail"
        private const val EXTRACTION_DELAY_MS = 500L
        var instance: YahooAccessibilityService? = null
    }

    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }

    private var lastProcessedTime = 0L
    private var lastWindowContent = ""



    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null) return

        // Process both click and window content change events for Yahoo Mail
        if (event.packageName == YAHOO_PACKAGE) {
            // Comprehensive event logging for debugging
            Log.d("getEventType", "onAccessibilityEvent: "+event.eventType)
            logAccessibilityEvent(event)
            when (event.eventType) {
                AccessibilityEvent.TYPE_VIEW_CLICKED -> {
                    Log.d(TAG, "🔍 Yahoo Mail VIEW_CLICKED detected!")

                    // Check if this is specifically a sender mail click
                    val isSenderClick = isClickOnSenderMail(event)
                    if (isSenderClick) {
                        Log.d(TAG, "🎯 SENDER MAIL CLICK CONFIRMED - Starting targeted extraction...")
                        processEmailExtraction("SENDER_CLICK_EVENT")
                    } else {
                        Log.d(TAG, "📧 General click detected - Starting email extraction...")
                        processEmailExtraction("GENERAL_CLICK_EVENT")
                    }
                }

                AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                    // Check if this is likely a click-triggered content change
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastProcessedTime > 1000) { // Avoid rapid duplicate processing

                        // Log the event details for debugging
                        Log.d(TAG, "📱 WINDOW_CONTENT_CHANGED event details:")
                        Log.d(TAG, "   Event text: ${event.text}")
                        Log.d(TAG, "   Event source: ${event.source?.text}")
                        Log.d(TAG, "   Event className: ${event.className}")

                        // Get current window content to detect if it's an email view
                        val rootNode = rootInActiveWindow
                        if (rootNode != null) {
                            // Check if this looks like a sender mail click
                            val isSenderClick = detectSenderMailClick(rootNode)
                            val isEmailView = isEmailDetailView(rootNode)

                            Log.d(TAG, "🔍 Analysis: isSenderClick=$isSenderClick, isEmailView=$isEmailView")

                            if (isSenderClick || isEmailView) {
                                val currentContent = getWindowContentSignature(rootNode)

                                // Only process if content has significantly changed
                                if (currentContent != lastWindowContent) {
                                    Log.d(TAG, "🔍 Yahoo Mail content changed - ${if (isSenderClick) "SENDER CLICK" else "EMAIL VIEW"} detected, starting extraction...")
                                    lastWindowContent = currentContent
                                    lastProcessedTime = currentTime
                                    processEmailExtraction("CONTENT_CHANGE_${if (isSenderClick) "SENDER" else "EMAIL"}")
                                }
                            }
                        }
                    }
                }

                AccessibilityEvent.TYPE_VIEW_FOCUSED -> {
                    // Additional trigger for when email elements get focus
                    if (isEmailRelatedFocus(event)) {
                        Log.d(TAG, "🔍 Yahoo Mail email focus detected, starting extraction...")
                        processEmailExtraction("FOCUS_EVENT")
                    }
                }
            }
        }
    }

    /**
     * Process email extraction with debouncing
     */
    private fun processEmailExtraction(trigger: String) {
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                val rootNode = rootInActiveWindow
                if (rootNode != null) {
                    Log.d(TAG, "📧 Processing email extraction triggered by: $trigger")

                    // If this is a sender-specific trigger, prioritize sender data extraction
                    if (trigger.contains("SENDER")) {
                        extractEmailDetailsWithSenderFocus(rootNode)
                    } else {
                        extractEmailDetails(rootNode)
                    }
                } else {
                    Log.w(TAG, "⚠️ Root node is null, cannot extract email details")
                }
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error in accessibility service: ${e.message}", e)
            }
        }, EXTRACTION_DELAY_MS)
    }

    /**
     * Extract email details with focus on sender information
     */
    private fun extractEmailDetailsWithSenderFocus(rootNode: AccessibilityNodeInfo) {
        Log.d(TAG, "🎯 Starting SENDER-FOCUSED email extraction from Yahoo Mail...")

        // First, aggressively look for sender information
        val senderInfo = findSenderInformation(rootNode)

        if (senderInfo != null) {
            Log.d(TAG, "✅ SENDER FOUND: Name='${senderInfo.first}', Email='${senderInfo.second}'")

            // Now extract the full email details
            extractEmailDetails(rootNode)
        } else {
            Log.w(TAG, "⚠️ No sender information found, falling back to general extraction")
            extractEmailDetails(rootNode)
        }
    }

    /**
     * Aggressively find sender information in the current view
     */
    private fun findSenderInformation(rootNode: AccessibilityNodeInfo): Pair<String?, String>? {
        val nodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, nodes)

        Log.d(TAG, "🔍 Searching for sender in ${nodes.size} nodes...")

        for (node in nodes) {
            val text = node.text?.toString()?.trim() ?: continue
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()

            Log.d(TAG, "🔍 Checking node: '$text' | ViewId: '$viewId' | ContentDesc: '$contentDesc'")

            // Try to extract sender info from this node
            val senderInfo = extractSenderInfo(text, viewId, contentDesc)
            if (senderInfo != null) {
                Log.d(TAG, "🎯 SENDER EXTRACTED: ${senderInfo.first} <${senderInfo.second}>")
                return senderInfo
            }
        }

        return null
    }

    /**
     * Check if current view is an email detail view
     */
    private fun isEmailDetailView(rootNode: AccessibilityNodeInfo): Boolean {
        val nodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, nodes)

        var hasEmailContent = false
        var hasSenderInfo = false
        var hasSubject = false

        for (node in nodes) {
            val text = node.text?.toString()?.trim() ?: continue
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()

            // Check for email indicators
            if (text.contains("@") && text.isValidEmail()) {
                hasEmailContent = true
            }

            // Check for sender indicators
            if (viewId?.contains("sender", true) == true ||
                viewId?.contains("from", true) == true ||
                contentDesc?.contains("sender", true) == true ||
                text.startsWith("From:", true)) {
                hasSenderInfo = true
            }

            // Check for subject indicators
            if (viewId?.contains("subject", true) == true ||
                contentDesc?.contains("subject", true) == true ||
                text.startsWith("Subject:", true) ||
                text.startsWith("Re:", true) ||
                text.startsWith("Fwd:", true)) {
                hasSubject = true
            }
        }

        // Consider it an email detail view if we have at least 2 of these indicators
        val indicators = listOf(hasEmailContent, hasSenderInfo, hasSubject).count { it }
        val isEmailView = indicators >= 2

        Log.d(TAG, "📧 Email detail view check: hasEmail=$hasEmailContent, hasSender=$hasSenderInfo, hasSubject=$hasSubject, isEmailView=$isEmailView")
        return isEmailView
    }

    /**
     * Get a signature of current window content for change detection
     */
    private fun getWindowContentSignature(rootNode: AccessibilityNodeInfo): String {
        val nodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, nodes)

        val contentBuilder = StringBuilder()
        for (node in nodes) {
            val text = node.text?.toString()?.trim()
            if (!text.isNullOrEmpty() && text.length > 5) {
                contentBuilder.append(text.take(20)) // Take first 20 chars of each significant text
            }
        }

        return contentBuilder.toString().hashCode().toString()
    }

    /**
     * Check if the click event is specifically on sender mail
     */
    private fun isClickOnSenderMail(event: AccessibilityEvent): Boolean {
        val source = event.source ?: return false
        val sourceText = source.text?.toString() ?: ""
        val sourceViewId = source.viewIdResourceName ?: ""
        val sourceContentDesc = source.contentDescription?.toString() ?: ""

        Log.d(TAG, "🔍 Checking if click is on sender mail:")
        Log.d(TAG, "   Source text: '$sourceText'")
        Log.d(TAG, "   Source ViewId: '$sourceViewId'")
        Log.d(TAG, "   Source ContentDesc: '$sourceContentDesc'")

        // Check for sender-specific indicators
        val hasSenderIndicators = sourceViewId.contains("sender", true) ||
                                 sourceViewId.contains("from", true) ||
                                 sourceContentDesc.contains("sender", true) ||
                                 sourceContentDesc.contains("from", true) ||
                                 sourceText.startsWith("From:", true) ||
                                 sourceText.startsWith("Message from", true)

        // Check for Yahoo's specific sender format "Name • <EMAIL>"
        val hasYahooSenderFormat = sourceText.contains("•") && sourceText.contains("@")

        // Check if text contains email address (likely sender)
        val hasEmailInText = sourceText.contains("@") && sourceText.isValidEmail()

        val isSenderClick = hasSenderIndicators || hasYahooSenderFormat ||
                           (hasEmailInText && sourceText.length < 100) // Short text with email is likely sender

        Log.d(TAG, "🎯 Sender click analysis:")
        Log.d(TAG, "   Has sender indicators: $hasSenderIndicators")
        Log.d(TAG, "   Has Yahoo format: $hasYahooSenderFormat")
        Log.d(TAG, "   Has email in text: $hasEmailInText")
        Log.d(TAG, "   Final result: $isSenderClick")

        return isSenderClick
    }

    /**
     * Check if focus event is related to email content
     */
    private fun isEmailRelatedFocus(event: AccessibilityEvent): Boolean {
        val text = event.text?.toString() ?: ""
        val contentDesc = event.contentDescription?.toString() ?: ""
        val source = event.source
        val viewId = source?.viewIdResourceName

        // Check if focused element contains email-related content
        val isEmailRelated = text.contains("@") ||
                            viewId?.contains("sender", true) == true ||
                            viewId?.contains("from", true) == true ||
                            viewId?.contains("subject", true) == true ||
                            contentDesc.contains("sender", true) ||
                            contentDesc.contains("from", true) ||
                            contentDesc.contains("email", true)

        Log.d(TAG, "🎯 Focus event check: text='${text.take(30)}', viewId='$viewId', isEmailRelated=$isEmailRelated")
        return isEmailRelated
    }

    /**
     * Dump all screen content for debugging
     */
    private fun dumpAllScreenContent(rootNode: AccessibilityNodeInfo) {
        val allNodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, allNodes)

        Log.d(TAG, "🔍 ===== DUMPING ALL SCREEN CONTENT =====")
        Log.d(TAG, "Total nodes found: ${allNodes.size}")

        allNodes.forEachIndexed { index, node ->
            val text = node.text?.toString()?.trim() ?: ""
            val viewId = node.viewIdResourceName ?: ""
            val contentDesc = node.contentDescription?.toString() ?: ""
            val className = node.className?.toString() ?: ""

            if (text.isNotEmpty() || viewId.isNotEmpty() || contentDesc.isNotEmpty()) {
                Log.d(TAG, """
                    📱 Node $index:
                       Text: '$text'
                       ViewId: '$viewId'
                       ContentDesc: '$contentDesc'
                       ClassName: '$className'
                       Clickable: ${node.isClickable}
                       Focusable: ${node.isFocusable}
                    ---
                """.trimIndent())
            }
        }
        Log.d(TAG, "🔍 ===== END SCREEN CONTENT DUMP =====")
    }

    /**
     * Simple aggressive extraction - get basic data first
     */
    private fun performSimpleExtraction(rootNode: AccessibilityNodeInfo): Map<String, String> {
        // First dump all content for debugging
        dumpAllScreenContent(rootNode)

        val extractedData = mutableMapOf<String, String>()
        val allNodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, allNodes)

        Log.d(TAG, "🔍 SIMPLE EXTRACTION: Analyzing ${allNodes.size} nodes for basic data...")

        for (node in allNodes) {
            val text = node.text?.toString()?.trim() ?: continue
            if (text.isEmpty()) continue

            Log.d(TAG, "📝 Analyzing text: '$text'")

            // Check for original Yahoo comma-separated format
            if (text.contains(",") && text.length > 20) {
                val parts = text.split(",").map { it.trim() }
                if (parts.size >= 3) {
                    Log.d(TAG, "🔍 Found comma-separated data with ${parts.size} parts")
                    parts.forEachIndexed { index, part ->
                        Log.d(TAG, "   Part $index: '$part'")
                        if (part.contains("@") && part.isValidEmail()) {
                            if (!extractedData.containsKey("fromEmail")) {
                                extractedData["fromEmail"] = part
                                Log.d(TAG, "📧 COMMA FORMAT SENDER: $part")
                            } else if (!extractedData.containsKey("toEmail")) {
                                extractedData["toEmail"] = part
                                Log.d(TAG, "📬 COMMA FORMAT RECIPIENT: $part")
                            }
                        } else if (index == 0 && part.isNotEmpty() && !part.contains("@")) {
                            extractedData["senderName"] = part
                            Log.d(TAG, "👤 COMMA FORMAT SENDER NAME: $part")
                        } else if (index == 1 && part.isNotEmpty() && !part.contains("@")) {
                            extractedData["subject"] = part
                            Log.d(TAG, "📝 COMMA FORMAT SUBJECT: $part")
                        } else if (index >= 5 && part.length > 20) {
                            extractedData["body"] = part
                            Log.d(TAG, "📄 COMMA FORMAT BODY: ${part.take(50)}...")
                        }
                    }
                    // If we found comma-separated data, we can return early
                    if (extractedData.isNotEmpty()) {
                        Log.d(TAG, "✅ Successfully extracted from comma format")
                        return extractedData
                    }
                }
            }

            // Look for emails in text
            if (text.contains("@")) {
                val emails = text.extractEmails()
                emails.forEach { email ->
                    Log.d(TAG, "📧 Found email: $email in text: '${text.take(50)}...'")

                    // Yahoo specific pattern: "Name • <EMAIL>"
                    if (text.contains("•")) {
                        val parts = text.split("•")
                        if (parts.size >= 2) {
                            val namePart = parts[0].trim()
                            val emailPart = parts[1].trim()
                            if (emailPart.contains(email) && namePart.isNotEmpty()) {
                                extractedData["fromEmail"] = email
                                extractedData["senderName"] = namePart
                                Log.d(TAG, "🎯 YAHOO FORMAT SENDER: $namePart <$email>")
                            }
                        }
                    }
                    // Standard sender patterns
                    else if (text.startsWith("From:", true) || text.contains("Message from", true)) {
                        extractedData["fromEmail"] = email
                        val name = text.replace(email, "").replace("From:", "")
                            .replace("Message from", "").trim()
                        if (name.isNotEmpty() && !name.contains("@")) {
                            extractedData["senderName"] = name
                        }
                        Log.d(TAG, "👤 STANDARD SENDER: $email, Name: $name")
                    }
                    // Recipient patterns
                    else if (text.startsWith("To:", true) || text.contains("Sent to", true)) {
                        extractedData["toEmail"] = email
                        Log.d(TAG, "📬 EXPLICIT RECIPIENT: $email")
                    }
                    // If no clear context, assume first email is sender, subsequent are recipients
                    else if (!extractedData.containsKey("fromEmail")) {
                        extractedData["fromEmail"] = email
                        Log.d(TAG, "📧 ASSUMED SENDER (first email): $email")
                    } else if (!extractedData.containsKey("toEmail")) {
                        extractedData["toEmail"] = email
                        Log.d(TAG, "📬 ASSUMED RECIPIENT: $email")
                    }
                }
            }

            // Look for subject
            if (text.startsWith("Subject:", true) || text.startsWith("Re:", true) ||
                text.startsWith("Fwd:", true)) {
                val subject = text.removePrefix("Subject:").removePrefix("Re:").removePrefix("Fwd:").trim()
                if (subject.isNotEmpty()) {
                    extractedData["subject"] = subject
                    Log.d(TAG, "📝 SUBJECT: $subject")
                }
            }

            // Look for substantial body content
            if (text.length > 50 && !text.contains("@") && !text.startsWith("Subject:") &&
                !text.startsWith("From:") && !text.startsWith("To:")) {
                extractedData["body"] = text
                Log.d(TAG, "📄 BODY: ${text.take(100)}...")
            }
        }

        return extractedData
    }

    /**
     * Process simple extraction data
     */
    private fun processSimpleExtractionData(data: Map<String, String>) {
        val senderName = data["senderName"] ?: "Unknown Sender"
        val fromEmail = data["fromEmail"] ?: "<EMAIL>"
        val toEmail = data["toEmail"] ?: "<EMAIL>"
        val subject = data["subject"] ?: "No Subject"
        val body = data["body"] ?: "No Body Content"

        Log.d(TAG, """
            📧 ===== SIMPLE YAHOO EMAIL EXTRACTION =====
            👤 Sender Name: $senderName
            📧 From Email: $fromEmail
            📬 To Email: $toEmail
            📝 Subject: $subject
            📄 Body: ${body.take(100)}...
            ==========================================
        """.trimIndent())

        // Create simplified email data
        val emailData = EmailData(
            senderName = senderName,
            fromEmail = fromEmail,
            toEmails = listOf(toEmail),
            ccEmails = emptyList(),
            bccEmails = emptyList(),
            subject = subject,
            date = "Unknown Date",
            body = body,
            attachments = "No Attachments"
        )

        processExtractedEmailData(emailData)
    }

    /**
     * Extract comprehensive email details from Yahoo Mail
     */
    private fun extractEmailDetails(rootNode: AccessibilityNodeInfo) {
        Log.d(TAG, "🔍 Starting comprehensive email extraction from Yahoo Mail...")

        // First try simple extraction
        val simpleData = performSimpleExtraction(rootNode)
        if (simpleData.containsKey("fromEmail") && simpleData.containsKey("toEmail")) {
            Log.d(TAG, "✅ Simple extraction found basic data, using it")
            processSimpleExtractionData(simpleData)
            return
        }

        Log.d(TAG, "📧 Simple extraction insufficient, proceeding with comprehensive extraction...")

        // Initialize email data variables
        var senderName: String? = null
        var fromEmail: String? = null
        var subject: String? = null
        var date: String? = null
        var attachments: String? = null

        val toEmails = mutableListOf<String>()
        val ccEmails = mutableListOf<String>()
        val bccEmails = mutableListOf<String>()
        val emailBodyBuilder = StringBuilder()
        val attachmentSet = mutableSetOf<String>()

        // Collect all nodes for processing
        val possibleNodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, possibleNodes)

        Log.d(TAG, "📊 Found ${possibleNodes.size} nodes to analyze")

        // Process each node for email data extraction
        for (node in possibleNodes) {
            val text = node.text?.toString()?.trim() ?: continue
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()

            if (text.isEmpty()) continue

            Log.d(TAG, "🔍 Processing: '$text' | ViewId: '$viewId' | ContentDesc: '$contentDesc'")

            // Extract sender information first
            if (fromEmail == null) {
                extractSenderInfo(text, viewId, contentDesc)?.let { (name, email) ->
                    senderName = name
                    fromEmail = email
                    Log.d(TAG, "👤 Found sender: Name='$name', Email='$email'")
                }
            }

            val currentFromEmail = fromEmail // Capture current state for filtering

            // Extract other email components
            extractSubject(text, viewId, contentDesc)?.let { extractedSubject ->
                if (subject == null) {
                    subject = extractedSubject
                    Log.d(TAG, "📧 Found subject: $subject")
                }
            }

            extractToEmails(text, viewId, contentDesc, currentFromEmail)?.let { emails ->
                emails.forEach { email ->
                    if (!toEmails.contains(email) &&
                        email != currentFromEmail &&
                        !email.equals(currentFromEmail, ignoreCase = true)) {
                        toEmails.add(email)
                        Log.d(TAG, "📬 Found TO email: $email")
                    }
                }
            }

            extractCcEmails(text, viewId, contentDesc)?.let { emails ->
                emails.forEach { email ->
                    if (!ccEmails.contains(email) &&
                        email != currentFromEmail &&
                        !email.equals(currentFromEmail, ignoreCase = true)) {
                        ccEmails.add(email)
                        Log.d(TAG, "📋 Found CC email: $email")
                    }
                }
            }

            extractBccEmails(text, viewId, contentDesc)?.let { emails ->
                emails.forEach { email ->
                    if (!bccEmails.contains(email) &&
                        email != currentFromEmail &&
                        !email.equals(currentFromEmail, ignoreCase = true)) {
                        bccEmails.add(email)
                        Log.d(TAG, "🔒 Found BCC email: $email")
                    }
                }
            }

            extractDate(text, viewId, contentDesc)?.let { extractedDate ->
                if (date == null) {
                    date = extractedDate
                    Log.d(TAG, "📅 Found date: $date")
                }
            }

            if (detectEmailBodyContent(text, viewId, contentDesc, node)) {
                emailBodyBuilder.appendLine(text)
                Log.d(TAG, "📄 Added to body: ${text.take(50)}...")
            }

            extractAttachments(text, viewId, contentDesc)?.let { attachment ->
                attachmentSet.add(attachment)
                Log.d(TAG, "📎 Found attachment: $attachment")
            }
        }

        // Finalize extracted data with fallbacks
        val finalData = finalizeEmailData(
            senderName, fromEmail, toEmails, ccEmails, bccEmails,
            subject, date, emailBodyBuilder, attachmentSet
        )

        // Process the extracted email data
        processExtractedEmailData(finalData)
    }

    /**
     * Detect if user clicked specifically on sender mail
     */
    private fun detectSenderMailClick(rootNode: AccessibilityNodeInfo): Boolean {
        val nodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, nodes)

        var senderClickDetected = false
        var senderEmailFound = ""

        for (node in nodes) {
            val text = node.text?.toString()?.trim() ?: continue
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()

            // Check for recently clicked or focused sender elements
            if (node.isClickable || node.isFocusable) {
                val isSenderContext = viewId?.contains("sender", true) == true ||
                                     viewId?.contains("from", true) == true ||
                                     contentDesc?.contains("sender", true) == true ||
                                     contentDesc?.contains("from", true) == true ||
                                     text.startsWith("From:", true) ||
                                     text.contains("•") // Yahoo specific sender format

                if (isSenderContext && text.contains("@")) {
                    val emails = text.extractEmails()
                    if (emails.isNotEmpty()) {
                        senderEmailFound = emails[0]
                        senderClickDetected = true
                        Log.d(TAG, "🎯 SENDER CLICK DETECTED: $senderEmailFound in context: $text")
                        break
                    }
                }
            }

            // Also check for Yahoo's specific sender format "Name • <EMAIL>"
            if (text.contains("•") && text.contains("@")) {
                val parts = text.split("•")
                if (parts.size >= 2) {
                    val emailPart = parts[1].trim()
                    if (emailPart.isValidEmail()) {
                        senderEmailFound = emailPart
                        senderClickDetected = true
                        Log.d(TAG, "🎯 YAHOO SENDER FORMAT DETECTED: $senderEmailFound")
                        break
                    }
                }
            }
        }

        if (senderClickDetected) {
            Log.d(TAG, "✅ Sender mail click confirmed for: $senderEmailFound")
        }

        return senderClickDetected
    }

    /**
     * Recursively find all email-related nodes
     */
    private fun findEmailNodes(node: AccessibilityNodeInfo, nodes: MutableList<AccessibilityNodeInfo>) {
        try {
            // Add current node if it has useful content
            val text = node.text?.toString()?.trim()
            val contentDesc = node.contentDescription?.toString()?.trim()
            val viewId = node.viewIdResourceName

            if (!text.isNullOrEmpty() || !contentDesc.isNullOrEmpty() || !viewId.isNullOrEmpty()) {
                nodes.add(node)
            }

            // Recursively process child nodes
            for (i in 0 until node.childCount) {
                val child = node.getChild(i)
                if (child != null) {
                    findEmailNodes(child, nodes)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error finding email nodes: ${e.message}")
        }
    }

    /**
     * Extract sender information (name and email)
     */
    private fun extractSenderInfo(text: String, viewId: String?, contentDesc: String?): Pair<String?, String>? {
        // Check for strong sender context indicators
        val isStrongSenderContext = viewId?.contains("sender", true) == true ||
                                   viewId?.contains("from", true) == true ||
                                   contentDesc?.contains("sender", true) == true ||
                                   contentDesc?.contains("from", true) == true ||
                                   text.startsWith("From:", true) ||
                                   text.startsWith("Message from", true)

        // PRIORITY 1: Yahoo specific format "Name • <EMAIL>"
        if (text.contains("•")) {
            val parts = text.split("•").map { it.trim() }
            if (parts.size >= 2) {
                var name = parts[0].takeIf { it.isNotEmpty() && !it.contains("@") }
                val emailPart = parts[1]
                val email = emailPart.extractEmail() ?: if (emailPart.isValidEmail()) emailPart else null

                if (email != null) {
                    name = name?.let { cleanSenderName(it) }
                    Log.d(TAG, "✅ PRIORITY 1 (•): Name='$name', Email='$email'")
                    return Pair(name, email)
                }
            }
        }

        // PRIORITY 2: Strong sender context
        if (isStrongSenderContext) {
            val cleanText = text.removePrefix("From:").removePrefix("Message from").trim()

            // Check for "Name <<EMAIL>>" in sender context
            val emailInBrackets = Regex("<([^>]+)>").find(cleanText)
            if (emailInBrackets != null) {
                val email = emailInBrackets.groupValues[1].trim()
                val name = cleanText.replace(emailInBrackets.value, "").trim().takeIf { it.isNotEmpty() }
                if (email.isValidEmail()) {
                    Log.d(TAG, "✅ PRIORITY 2A (context+<>): Name='$name', Email='$email'")
                    return Pair(name, email)
                }
            }

            // Direct email in sender context
            if (cleanText.isValidEmail()) {
                Log.d(TAG, "✅ PRIORITY 2B (context): Email='$cleanText'")
                return Pair(null, cleanText)
            }

            // Extract first email from sender context
            val extractedEmail = cleanText.extractEmail()
            if (extractedEmail != null) {
                val name = cleanText.replace(extractedEmail, "").trim().takeIf { it.isNotEmpty() }
                Log.d(TAG, "✅ PRIORITY 2C (context+extract): Name='$name', Email='$extractedEmail'")
                return Pair(name, extractedEmail)
            }
        }

        // PRIORITY 3: "Name <<EMAIL>>" without strong context
        val emailInBrackets = Regex("<([^>]+)>").find(text)
        if (emailInBrackets != null) {
            val email = emailInBrackets.groupValues[1].trim()
            val name = text.replace(emailInBrackets.value, "").trim().takeIf { it.isNotEmpty() }
            if (email.isValidEmail()) {
                Log.d(TAG, "✅ PRIORITY 3 (<>): Name='$name', Email='$email'")
                return Pair(name, email)
            }
        }

        return null
    }

    /**
     * Clean sender name by removing common prefixes and suffixes
     */
    private fun cleanSenderName(name: String): String {
        return name
            .removePrefix("From:")
            .removePrefix("Message from")
            .removePrefix("•")
            .removeSuffix("•")
            .replace(Regex("\\s+"), " ")
            .trim()
    }

    /**
     * Extract subject from text
     */
    private fun extractSubject(text: String, viewId: String?, contentDesc: String?): String? {
        // Check for subject indicators
        val isSubjectContext = viewId?.contains("subject", true) == true ||
                              contentDesc?.contains("subject", true) == true ||
                              text.startsWith("Subject:", true) ||
                              text.startsWith("Re:", true) ||
                              text.startsWith("Fwd:", true)

        if (isSubjectContext) {
            val cleanSubject = text
                .removePrefix("Subject:")
                .removePrefix("Re:")
                .removePrefix("Fwd:")
                .trim()

            if (cleanSubject.isNotEmpty() && !cleanSubject.contains("@")) {
                return cleanSubject
            }
        }

        return null
    }

    /**
     * Extract TO emails
     */
    private fun extractToEmails(text: String, viewId: String?, contentDesc: String?, senderEmail: String?): List<String>? {
        val emails = mutableListOf<String>()

        // Pattern 1: Explicit "To:" prefix
        if (text.startsWith("To:", true)) {
            val extractedEmails = text.extractEmails()
            extractedEmails.forEach { email ->
                if (!email.equals(senderEmail, ignoreCase = true)) {
                    emails.add(email)
                    Log.d(TAG, "✅ Found TO email from 'To:' prefix: $email")
                }
            }
        }

        // Pattern 2: ViewId indicates TO field (but NOT sender field)
        val isSenderContext = viewId?.contains("sender", true) == true ||
                             viewId?.contains("from", true) == true ||
                             contentDesc?.contains("sender", true) == true ||
                             contentDesc?.contains("from", true) == true

        val isToContext = (viewId?.contains("to", true) == true ||
                          viewId?.contains("recipient", true) == true ||
                          viewId?.contains("addressee", true) == true ||
                          contentDesc?.contains("to", true) == true ||
                          contentDesc?.contains("recipient", true) == true) &&
                          !isSenderContext

        if (isToContext && text.contains("@")) {
            val extractedEmails = text.extractEmails()
            extractedEmails.forEach { email ->
                if (!email.equals(senderEmail, ignoreCase = true)) {
                    emails.add(email)
                    Log.d(TAG, "✅ Found TO email from context: $email")
                }
            }
        }

        return emails.takeIf { it.isNotEmpty() }
    }

    /**
     * Extract CC emails
     */
    private fun extractCcEmails(text: String, viewId: String?, contentDesc: String?): List<String>? {
        val emails = mutableListOf<String>()

        // Pattern 1: Explicit "CC:" prefix
        if (text.startsWith("CC:", true) || text.startsWith("Cc:", true)) {
            emails.addAll(text.extractEmails())
            Log.d(TAG, "✅ Found CC emails from 'CC:' prefix: ${emails.joinToString(", ")}")
        }

        // Pattern 2: ViewId indicates CC field
        val isCcContext = viewId?.contains("cc", true) == true ||
                         contentDesc?.contains("cc", true) == true ||
                         contentDesc?.contains("carbon copy", true) == true

        if (isCcContext && text.contains("@")) {
            emails.addAll(text.extractEmails())
            Log.d(TAG, "✅ Found CC emails from context: ${emails.joinToString(", ")}")
        }

        return emails.takeIf { it.isNotEmpty() }
    }

    /**
     * Extract BCC emails
     */
    private fun extractBccEmails(text: String, viewId: String?, contentDesc: String?): List<String>? {
        val emails = mutableListOf<String>()

        // Pattern 1: Explicit "BCC:" prefix
        if (text.startsWith("BCC:", true) || text.startsWith("Bcc:", true)) {
            emails.addAll(text.extractEmails())
            Log.d(TAG, "✅ Found BCC emails from 'BCC:' prefix: ${emails.joinToString(", ")}")
        }

        // Pattern 2: ViewId indicates BCC field
        val isBccContext = viewId?.contains("bcc", true) == true ||
                          contentDesc?.contains("bcc", true) == true ||
                          contentDesc?.contains("blind carbon copy", true) == true

        if (isBccContext && text.contains("@")) {
            emails.addAll(text.extractEmails())
            Log.d(TAG, "✅ Found BCC emails from context: ${emails.joinToString(", ")}")
        }

        return emails.takeIf { it.isNotEmpty() }
    }

    /**
     * Extract date information
     */
    private fun extractDate(text: String, viewId: String?, contentDesc: String?): String? {
        // Check for date indicators
        val isDateContext = viewId?.contains("date", true) == true ||
                           viewId?.contains("time", true) == true ||
                           contentDesc?.contains("date", true) == true ||
                           contentDesc?.contains("time", true) == true ||
                           text.startsWith("Date:", true) ||
                           text.startsWith("Sent:", true)

        if (isDateContext) {
            val cleanDate = text
                .removePrefix("Date:")
                .removePrefix("Sent:")
                .trim()

            if (cleanDate.isNotEmpty() && !cleanDate.contains("@")) {
                return cleanDate
            }
        }

        // Pattern matching for common date formats
        val datePatterns = listOf(
            Regex("\\d{1,2}/\\d{1,2}/\\d{4}"),
            Regex("\\d{4}-\\d{2}-\\d{2}"),
            Regex("\\w{3}\\s+\\d{1,2},\\s+\\d{4}"),
            Regex("\\d{1,2}\\s+\\w{3}\\s+\\d{4}")
        )

        for (pattern in datePatterns) {
            val match = pattern.find(text)
            if (match != null) {
                return match.value
            }
        }

        return null
    }

    /**
     * Detect if text is part of email body content
     */
    private fun detectEmailBodyContent(text: String, viewId: String?, contentDesc: String?, node: AccessibilityNodeInfo): Boolean {
        // Skip if text is too short or contains email addresses (likely headers)
        if (text.length < 10 || text.contains("@")) return false

        // Check for body content indicators
        val isBodyContext = viewId?.contains("body", true) == true ||
                           viewId?.contains("content", true) == true ||
                           viewId?.contains("message", true) == true ||
                           contentDesc?.contains("body", true) == true ||
                           contentDesc?.contains("content", true) == true ||
                           contentDesc?.contains("message", true) == true

        if (isBodyContext) return true

        // Check if it's a substantial text block (likely body content)
        val isSubstantialText = text.length > 50 &&
                               text.split(" ").size > 5 &&
                               !text.startsWith("From:") &&
                               !text.startsWith("To:") &&
                               !text.startsWith("Subject:") &&
                               !text.startsWith("Date:")

        return isSubstantialText
    }

    /**
     * Extract attachment information
     */
    private fun extractAttachments(text: String, viewId: String?, contentDesc: String?): String? {
        // Check for attachment indicators
        val isAttachmentContext = viewId?.contains("attachment", true) == true ||
                                 contentDesc?.contains("attachment", true) == true ||
                                 text.contains("attachment", true) ||
                                 text.contains("📎") ||
                                 text.contains("file")

        if (isAttachmentContext) {
            // Look for file extensions
            val fileExtensions = listOf(".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".jpg", ".png", ".gif", ".zip", ".rar")
            for (ext in fileExtensions) {
                if (text.contains(ext, true)) {
                    return text.trim()
                }
            }

            // Return attachment indicator text
            if (text.isNotEmpty() && !text.contains("@")) {
                return text.trim()
            }
        }

        return null
    }

    /**
     * Finalize email data with proper fallbacks
     */
    private fun finalizeEmailData(
        senderName: String?,
        fromEmail: String?,
        toEmails: List<String>,
        ccEmails: List<String>,
        bccEmails: List<String>,
        subject: String?,
        date: String?,
        emailBodyBuilder: StringBuilder,
        attachmentSet: Set<String>
    ): EmailData {
        return EmailData(
            senderName = senderName ?: "Unknown Sender",
            fromEmail = fromEmail ?: "<EMAIL>",
            toEmails = toEmails.takeIf { it.isNotEmpty() } ?: listOf("<EMAIL>"),
            ccEmails = ccEmails,
            bccEmails = bccEmails,
            subject = subject ?: "No Subject",
            date = date ?: "Unknown Date",
            body = emailBodyBuilder.toString().trim().takeIf { it.isNotEmpty() } ?: "No Body Content",
            attachments = attachmentSet.joinToString(", ").takeIf { it.isNotEmpty() } ?: "No Attachments"
        )
    }

    /**
     * Process the extracted email data
     */
    private fun processExtractedEmailData(emailData: EmailData) {
        try {
            val androidId = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)

            Log.d(TAG, """
                📧 ===== YAHOO EMAIL EXTRACTION COMPLETE =====
                📎 Attachments: ${emailData.attachments}
            """.trimIndent())

            // Create EML file
            val emlContent = createEmlContent(emailData)
            val file = File(filesDir, "yahoo_email_${System.currentTimeMillis()}.eml")
            file.writeText(emlContent)

            // Determine receiver email using EmailDetectionHelper
            val receiverMail = EmailDetectionHelper.determineReceiverEmail(
                fromEmail = emailData.fromEmail,
                toEmails = emailData.toEmails,
                ccEmails = emailData.ccEmails,
                bccEmails = emailData.bccEmails,
                context = applicationContext,
                isForwardedEmail = false
            )

            Log.d(TAG, "🤖 AUTOMATIC receiver email determined: $receiverMail")

            // Process with API if we have valid data
            if (emailData.toEmails.isNotEmpty() &&
                emailData.fromEmail != "<EMAIL>" &&
                emailData.toEmails[0] != "<EMAIL>") {

                // Save receiver email to preferences
                sharedPrefManager.putString(AppConstant.receiverMail, receiverMail)

                // Process with hash API
               /* getHashId(file, receiverMail, emailData.fromEmail, emailData.ccEmails, emailData.bccEmails,
                         emailData.subject, emailData.date, emailData.body, emailData.attachments)*/
                Log.d(TAG, "YahooMailData: toMail$receiverMail  ,fromMail ${emailData.fromEmail} ,ccMail ${emailData.ccEmails} ,bccMail ${emailData.bccEmails} ,subject ${emailData.subject} ,date ${emailData.date} , ${emailData.body} ,attachment ${emailData.attachments}")

               // Log.d("getFromEmail", "processExtractedEmailData: "+emailData.fromEmail)

                if (emailData.fromEmail!="")
                {
                     getHashId(file, receiverMail, emailData.fromEmail, emailData.ccEmails, emailData.bccEmails,emailData.subject,emailData.date,emailData.body,emailData.attachments)
                }



            } else {
                Log.w(TAG, "⚠️ Insufficient email data for processing")
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error processing email: ${e.message}", e)
        }
    }


    private fun getHashId(emlContent:File,email: String, fromEmail: String, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String, attachments: String) {

        if (hasOverlayPermission(applicationContext)) {

            val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlContent)
            val bodyReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), body)

            val tomail = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), email.toString())
            val senderEmail = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), fromEmail.toString())

            val ccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), cc.toString())
            val bccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), bcc.toString())
            val subjectReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), subject)

            val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
            val retrofit = ApiClient.getRetrofitInstance(applicationContext)
            val apiService = retrofit!!.create(ApiService::class.java)
            val windowManager =getSystemService(WINDOW_SERVICE) as WindowManager
            val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as android.view.LayoutInflater
            val binding = LayoutProgressBinding.inflate(layoutInflater)
            val view = binding.root
            view.setBackgroundResource(R.drawable.bg_alert_dialog)
            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                else
                    WindowManager.LayoutParams.TYPE_PHONE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
                android.graphics.PixelFormat.TRANSLUCENT
            )
            params.dimAmount = 0.5f
            windowManager.addView(view, params)


            apiService.getHashKey(tomail,senderEmail,filePart,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
                .enqueue(object : Callback<HashResponse> {
                    override fun onResponse(
                        call: Call<HashResponse>,
                        response: Response<HashResponse>
                    ) {
                        windowManager.removeView(view)

                        if (response.body()!!.Code.toString()=="1")
                        {
                            windowManager.removeView(view)
                            sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                            sharedPrefManager.putString(AppConstant.receiverMail,email)
                            // Mark mail as configured when we successfully process an email
                            sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)
                            checkAiResponse(email,response.body()?.hashId.toString())
                        }

                        else
                        {
                            windowManager.removeView(view)
                            uploadFile(email,fromEmail,emlContent,cc,bcc,subject,date,body)
                        }
                    }

                    override fun onFailure(call: Call<HashResponse>, t: Throwable) {
                        windowManager.removeView(view)
                    }
                })
        }

        else
        {
            requestOverlayPermission(applicationContext)
        }
    }

    private fun uploadFile(email: String, fromEmail: String, emlContent: File, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String) {

        val emailRequestBody = RequestBody.create("text/plain".toMediaTypeOrNull(), email)
        val senderEmail = RequestBody.create("text/plain".toMediaTypeOrNull(), fromEmail.toString())
        val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlContent)
        val bodyReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), body)

        val ccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), cc.toString())
        val bccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), bcc.toString())
        val subjectReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), subject)


        val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)


        apiService.uploadFile(emailRequestBody,senderEmail,filePart,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
            .enqueue(object : Callback<EmailResponse> {
                override fun onResponse(
                    call: Call<EmailResponse>,
                    response: Response<EmailResponse>
                ) {

                    if (response.body()!!.Code==1)
                    {
                        sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                        sharedPrefManager.putString(AppConstant.receiverMail,email)
                        // Mark mail as configured when we successfully process an email
                        sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)
                        //  setResponsePopup(response.body()?.unsafeReasons,response.body()?.emailStatu0s.toString(),email,response.body()?.hashId.toString())
                        checkAiResponse(email,response.body()?.hashId.toString())
                    }
                    else
                    {
                        Toast.makeText(applicationContext, ""+response.body()!!.message, Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<EmailResponse?>, t: Throwable) {

                }

            })
    }

    private fun checkAiResponse(email: String, hashId: String) {
        // Get system services
        val windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater

        // Inflate loading layout
        val binding = LayoutProgressBinding.inflate(layoutInflater)
        val view = binding.root
        // view.setBackgroundResource(R.drawable.bg_alert_dialog)

        // Define layout params
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

        // Show loading overlay
        windowManager.addView(view, params)

        val handler = Handler(Looper.getMainLooper())
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)

        var retryCount = 0
        val maxRetries = 20

        // Polling function
        fun pollStatus() {
            if (retryCount >= maxRetries) {
                windowManager.removeView(view)
                Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                return
            }

            val request = PendingMailRequest(email = email, hashId = hashId)

            apiService.pendingMailStatusAi(request)
                .enqueue(object : Callback<PedingMailRes> {
                    override fun onResponse(call: Call<PedingMailRes>, response: Response<PedingMailRes>) {
                        val data = response.body()?.data
                        val status = data?.emlStatus?.lowercase()?.trim()
                        binding.tvStatus.setText(data?.unsafeReasons.toString())

                        if (status == "safe" || status == "unsafe") {
                            windowManager.removeView(view)
                            setResponsePopup(
                                unsafeReasons = data.unsafeReasons.toString(),
                                mailStatus = status,
                                email = email,
                                hashId = hashId
                            )
                        }
                        else if (retryCount==10 && status == "pending")
                        {
                            windowManager.removeView(view)
                        }
                        else {
                            retryCount++
                            handler.postDelayed({ pollStatus() }, 5000)
                        }
                    }

                    override fun onFailure(call: Call<PedingMailRes>, t: Throwable) {
                        Log.e("checkAiResponse", "API call failed: ${t.message}")
                        retryCount++
                        handler.postDelayed({ pollStatus() }, 5000)
                    }
                })
        }

        // Start polling
        pollStatus()
    }

    private fun setResponsePopup(unsafeReasons: String?, mailStatus: String, email: String, hashId: String) {
        Log.d("getReason", "setResponsePopup: $unsafeReasons   $mailStatus")

        // Use the new PopupManager for enhanced UX
        when (mailStatus.lowercase()) {
            "safe" -> {
                PopupManager.showSafeEmailPopup(
                    context = applicationContext,
                    message = unsafeReasons ?: "Email content has been verified as safe. No threats detected.",
                    onClose = {
                        // Log.d(TAG, "✅ Safe email popup closed")
                    }
                )
            }
            "unsafe" -> {
                PopupManager.showUnsafeEmailPopup(
                    context = applicationContext,
                    reasons = unsafeReasons ?: "This email contains suspicious content that may be harmful.",
                    onClose = {

                    },
                    onViewDetails = {

                    }
                )
            }
            "pending" -> {
                PopupManager.showPendingAnalysisPopup(
                    context = applicationContext,
                    message = "Analyzing email content for potential threats. Please wait..."
                )
            }
            else -> {
                // Fallback for unknown status
                PopupManager.showPopup(
                    context = applicationContext,
                    config = PopupManager.PopupConfig(
                        type = PopupManager.PopupType.WARNING,
                        title = "Email Analysis",
                        subtitle = "Status: $mailStatus",
                        message = unsafeReasons ?: "Email analysis completed with unknown status.",
                        primaryButtonText = "Close",
                        onPrimaryAction = {

                        }
                    )
                )
            }
        }
    }



    /* override fun onServiceConnected() {
         instance = this
     }

     fun pressBack() {
         performGlobalAction(GLOBAL_ACTION_BACK)
     }*/



   /* private fun setResponsePopup(unsafeReasons: String?, mailStatus: String, email: String, hashId: String) {

        if (!hasOverlayPermission(applicationContext)) {
            requestOverlayPermission(applicationContext)
            return
        }

        val windowManager = applicationContext.getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = applicationContext.getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val binding = LayoutValidationBinding.inflate(layoutInflater)


        currentPopupView?.let {
            try {
                windowManager.removeView(it)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        // Configure popup UI

       *//* val randomNumber = (1..2).random()
        println("Random number between 1 and 2: $randomNumber")
        when (randomNumber) {
            1 -> {
                binding.lvWarning.setAnimation(R.raw.safe)
                binding.btClose.visibility = View.VISIBLE
                binding.tvMessage.text = "All content varified as safe"
            }
            2 -> {
                binding.lvWarning.setAnimation(R.raw.unsafe)
                binding.btClose.visibility = View.VISIBLE
                binding.tvMessage.text = "All content varified as unsafe"
            }
        }*//*

        when (mailStatus) {
            "safe" -> {
                binding.lvWarning.setAnimation(R.raw.safe)
                binding.btClose.visibility = View.VISIBLE
            }
            "pending" -> {
                binding.lvWarning.setAnimation(R.raw.wait)
                binding.btClose.visibility = View.VISIBLE
            }
            "unsafe" -> {
                binding.lvWarning.setAnimation(R.raw.unsafe)
                binding.btClose.visibility = View.VISIBLE
               // pressBack()
            }
        }

        val view = binding.root
        view.setBackgroundColor(Color.parseColor("#80000000"))
       // view.setBackgroundResource(R.drawable.bg_alert_dialog)

        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f
        binding.tvMessage.text = unsafeReasons
        binding.btClose.setOnClickListener {
            try {
                windowManager.removeView(view)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        windowManager.addView(view, params)
        currentPopupView = view // Track the current popup
    }
*/


    private fun hasOverlayPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true
        }
    }

    private fun requestOverlayPermission(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(context)) {
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:$context.packageName")
                )
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
            }
        }
    }

    private fun readEmlFile(filePath: String): String? {
        val file = File(filePath)
        if (!file.exists()) {
            return null
        }
        val stringBuilder = StringBuilder()
        try {
            val reader = BufferedReader(FileReader(file))
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                stringBuilder.append(line).append("\n")
            }
            reader.close()
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
        return stringBuilder.toString()
    }

    private fun createEmlContent(emailData: EmailData): String {
        return """
            From: ${emailData.senderName} <${emailData.fromEmail}>
            To: ${emailData.toEmails.joinToString(", ")}
            Cc: ${emailData.ccEmails.joinToString(", ")}
            Bcc: ${emailData.bccEmails.joinToString(", ")}
            Subject: ${emailData.subject}
            Date: ${emailData.date}
            MIME-Version: 1.0
            Content-Type: text/plain; charset=UTF-8
            X-Mailer: Yahoo Accessibility Service
            X-Attachments: ${emailData.attachments}

            ${emailData.body}
        """.trimIndent()
    }

    private data class EmailData(
        val senderName: String,
        val fromEmail: String,
        val toEmails: List<String>,
        val ccEmails: List<String>,
        val bccEmails: List<String>,
        val subject: String,
        val date: String,
        val body: String,
        val attachments: String
    )

    fun String.extractEmails(): List<String> {
        return Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}").findAll(this).map { it.value }.toList()
    }

    fun String.isValidEmail(): Boolean {
        return this.matches(Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"))
    }

    fun String.extractEmail(): String? = this.extractEmails().firstOrNull()

    private fun logAccessibilityEvent(event: AccessibilityEvent) {
        val eventTypeString = when (event.eventType) {
            AccessibilityEvent.TYPE_VIEW_CLICKED -> "VIEW_CLICKED"
            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> "WINDOW_CONTENT_CHANGED"
            AccessibilityEvent.TYPE_VIEW_FOCUSED -> "VIEW_FOCUSED"
            AccessibilityEvent.TYPE_VIEW_SELECTED -> "VIEW_SELECTED"
            AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED -> "VIEW_TEXT_CHANGED"
            AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> "WINDOW_STATE_CHANGED"
            else -> "OTHER(${event.eventType})"
        }

        Log.d(TAG, """
            📱 ===== YAHOO ACCESSIBILITY EVENT =====
            🔸 Event Type: $eventTypeString
            🔸 Package: ${event.packageName}
            🔸 Class: ${event.className}
            🔸 Event Text: ${event.text}
            🔸 Content Description: ${event.contentDescription}
            🔸 Source Text: ${event.source?.text}
            🔸 Source ViewId: ${event.source?.viewIdResourceName}
            🔸 Source ContentDesc: ${event.source?.contentDescription}
            🔸 Source Clickable: ${event.source?.isClickable}
            🔸 Source Focusable: ${event.source?.isFocusable}
            🔸 Timestamp: ${System.currentTimeMillis()}
            =====================================
        """.trimIndent())

        // Special logging for potential sender clicks
        val sourceText = event.source?.text?.toString() ?: ""
        val sourceViewId = event.source?.viewIdResourceName ?: ""
        val sourceContentDesc = event.source?.contentDescription?.toString() ?: ""

        if (sourceText.contains("@") ||
            sourceText.contains("•") ||
            sourceViewId.contains("sender", true) ||
            sourceViewId.contains("from", true) ||
            sourceContentDesc.contains("sender", true) ||
            sourceContentDesc.contains("from", true)) {

            Log.d(TAG, "🎯 POTENTIAL SENDER INTERACTION DETECTED!")
            Log.d(TAG, "   Source details: '$sourceText'")
            Log.d(TAG, "   ViewId: '$sourceViewId'")
            Log.d(TAG, "   ContentDesc: '$sourceContentDesc'")
        }
    }

    override fun onInterrupt() {
        Log.d(TAG, "🔄 Yahoo Accessibility Service interrupted")
    }
}
