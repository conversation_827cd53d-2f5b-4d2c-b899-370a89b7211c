<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    android:id="@+id/cardAbout"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/_10sdp"
    android:layout_marginStart="@dimen/_10sdp"
    android:layout_marginEnd="@dimen/_10sdp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_8sdp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivIcon"
            android:layout_width="@dimen/_24sdp"
            android:layout_height="@dimen/_24sdp"
            android:src="@drawable/ic_info"
            app:tint="@color/app_color"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="About"
            android:textSize="@dimen/_12sdp"
            android:fontFamily="@font/roboto_semi_bold"
            android:textColor="@color/text_primary"
            app:layout_constraintStart_toEndOf="@id/ivIcon"
            app:layout_constraintTop_toTopOf="@id/ivIcon"
            android:layout_marginStart="16dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDescription"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="App information and version"
            android:textSize="@dimen/_10sdp"
            android:textColor="@color/text_secondary"
            android:fontFamily="@font/roboto_semi_regular"
            app:layout_constraintStart_toStartOf="@id/tvTitle"
            app:layout_constraintTop_toBottomOf="@id/tvTitle"
            android:layout_marginTop="4dp" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivForwardArrow"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/right_arrow"
            app:tint="@color/text_secondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>
