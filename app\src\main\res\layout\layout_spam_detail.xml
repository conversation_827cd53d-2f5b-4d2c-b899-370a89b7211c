<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_10sdp">

        <!-- Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cvMailItem"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_10sdp"
            android:foreground="?attr/selectableItemBackground"
            app:cardCornerRadius="14dp"
            app:cardElevation="6dp"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Sender -->
                <TextView
                    android:id="@+id/tvSender"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="From: <EMAIL>"
                    android:textColor="@android:color/black"
                    android:textSize="14sp"
                    android:fontFamily="@font/roboto_semi_regular"/>

                <!-- Receiver -->
                <TextView
                    android:id="@+id/tvReceiver"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="To: <EMAIL>"
                    android:textColor="@android:color/darker_gray"
                    android:textSize="13sp"
                    android:layout_marginTop="4dp"/>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/light_grey"
                    android:layout_marginVertical="8dp"/>

                <!-- Subject -->
                <TextView
                    android:id="@+id/tvSubject"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Subject line here"
                    android:textColor="@android:color/holo_blue_dark"
                    android:textSize="16sp"
                    android:textStyle="bold"/>

                <!-- Status -->
                <TextView
                    android:id="@+id/tvStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Status: Safe"
                    android:textColor="@android:color/holo_green_dark"
                    android:textStyle="bold"
                    android:layout_marginTop="6dp"/>

                <!-- Attachment -->
                <TextView
                    android:id="@+id/tvAttachment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Attachment: None"
                    android:textColor="@android:color/holo_orange_dark"
                    android:layout_marginTop="6dp"/>

                <!-- Body -->
                <TextView
                    android:id="@+id/tvBody"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="This is the email body preview..."
                    android:textColor="@android:color/black"
                    android:textSize="14sp"
                    android:layout_marginTop="8dp"/>

            </LinearLayout>
        </androidx.cardview.widget.CardView>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/actionButtons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginHorizontal="@dimen/_10sdp"
            android:layout_marginTop="@dimen/_10sdp"
            app:layout_constraintTop_toBottomOf="@id/cvMailItem"
            app:layout_constraintEnd_toEndOf="parent">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btDispute"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/raise_dispute"
                android:background="@drawable/bg_button_grey_color"
                android:textColor="@color/white"
                android:paddingLeft="@dimen/_5sdp"
                android:paddingRight="@dimen/_5sdp"
                android:fontFamily="@font/roboto_semi_medium"
                android:textAllCaps="false"
                android:textSize="@dimen/_10sdp"
                android:layout_marginEnd="8dp"
                android:elevation="4dp"/>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btClose"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/close"
                android:background="@drawable/bg_button_app_color"
                android:textColor="@color/white"
                android:fontFamily="@font/roboto_semi_medium"
                android:textAllCaps="false"
                android:textSize="@dimen/_10sdp"
                android:elevation="4dp"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
