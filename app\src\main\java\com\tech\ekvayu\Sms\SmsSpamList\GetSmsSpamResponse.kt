package com.tech.ekvayu.Sms.SmsSpamList

import com.google.gson.annotations.SerializedName

data class GetSmsSpamResponse(
    @SerializedName("count"        ) var count       : Int?               = null,
    @SerializedName("total_pages"  ) var totalPages  : Int?               = null,
    @SerializedName("current_page" ) var currentPage : Int?               = null,
    @SerializedName("page_size"    ) var pageSize    : Int?               = null,
    @SerializedName("next"         ) var next        : String?            = null,
    @SerializedName("previous"     ) var previous    : String?            = null,
    @SerializedName("results"      ) var results     : ArrayList<Results> = arrayListOf()
)


data class Results (
    @SerializedName("message"   ) var message   : String? = null,
    @SerializedName("timestamp" ) var timestamp : String? = null,
    @SerializedName("device_id" ) var deviceId  : String? = null,
    @SerializedName("type"      ) var type      : String? = null,
    @SerializedName("hashID"    ) var hashID    : String? = null,
    @SerializedName("status"    ) var status    : String? = null
)
