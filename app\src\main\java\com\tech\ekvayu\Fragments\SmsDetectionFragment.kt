package com.tech.ekvayu.Fragments

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import com.tech.ekvayu.Adapter.SmsSpamAdapter
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.CommonUtil
import com.tech.ekvayu.R
import com.tech.ekvayu.Sms.SmsSpamList.GetSmsSpamRequest
import com.tech.ekvayu.Sms.SmsSpamList.GetSmsSpamResponse
import com.tech.ekvayu.Sms.SmsSpamList.Results
import com.tech.ekvayu.databinding.FragmentSmsDetectionBinding
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response


class SmsDetectionFragment : Fragment(), SmsSpamAdapter.OnSpamSmsClickListener {
    
    private lateinit var binding: FragmentSmsDetectionBinding
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding= FragmentSmsDetectionBinding.inflate(inflater, container, false)

        getSpamSms()
        return binding.root
    }


    private fun getSpamSms() {
        val retrofit = ApiClient.getRetrofitInstance(requireContext())
        if (retrofit == null) {
            return
        }
        val apiService = retrofit.create(ApiService::class.java)
        val request = GetSmsSpamRequest()
        request.deviceId="f882e111c16dd299"

        CommonUtil.showProgressDialog(requireContext(), "Loading spam mails...")
        apiService.getSmsSpamlist(request)
            .enqueue(object : Callback<GetSmsSpamResponse> {
                override fun onResponse(
                    call: Call<GetSmsSpamResponse>,
                    response: Response<GetSmsSpamResponse>
                ) {
                    CommonUtil.hideProgressDialog()
                    if (response.isSuccessful && response.body() != null) {
                        val spamSms = response.body()!!.results
                        if (spamSms.isNotEmpty()) {
                            binding.rvSmsDetection.visibility = View.VISIBLE
                            binding.tvEmptyState.visibility = View.GONE
                            val adapter = SmsSpamAdapter(spamSms, this@SmsDetectionFragment)
                            binding.rvSmsDetection.adapter = adapter
                            binding.rvSmsDetection.layoutManager = GridLayoutManager(requireActivity(), 1)
                        } else {
                            binding.rvSmsDetection.visibility = View.GONE
                            binding.tvEmptyState.visibility = View.VISIBLE
                        }
                    } else {
                        val errorMsg = "Failed to load spam mails: ${response.message()}"
                        Log.e("SpamMailFragment", errorMsg)
                        Toast.makeText(requireContext(), errorMsg, Toast.LENGTH_LONG).show()
                    }

                }

                override fun onFailure(call: Call<GetSmsSpamResponse?>, t: Throwable) {
                    CommonUtil.hideProgressDialog()
                    val errorMsg = "Network error: ${t.message}"
                    Log.e("SpamMailFragment", errorMsg, t)
                    Toast.makeText(requireContext(), errorMsg, Toast.LENGTH_LONG).show()

                }
            })
    }



    override fun onSmsClicked(item: Results) {

    }


}
