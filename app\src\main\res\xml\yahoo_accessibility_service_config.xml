<?xml version="1.0" encoding="utf-8"?>

<accessibility-service
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:description="@string/accessibility_service_yahoo"
    android:accessibilityEventTypes="typeWindowContentChanged|typeViewTextChanged|typeViewClicked|typeViewFocused|typeWindowStateChanged|typeViewSelected"
    android:packageNames="com.yahoo.mobile.client.android.mail"
    android:notificationTimeout="100"
    android:canRetrieveWindowContent="true"
    android:accessibilityFeedbackType="feedbackGeneric"
    android:accessibilityFlags="flagDefault|flagRequestTouchExplorationMode"
    android:settingsActivity="com.yahoo.mobile.client.android.mail.settings.AccessibilitySettingsActivity" />



