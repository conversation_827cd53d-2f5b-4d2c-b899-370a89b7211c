package com.tech.ekvayu.Adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.tech.ekvayu.databinding.ItemSecurityTypeBinding
import com.tech.ekvayu.models.SecurityTypeModel


class SecurityTypeAdapter(private val items: List<SecurityTypeModel>, private val listener: onClickEventListner) : RecyclerView.Adapter<SecurityTypeAdapter.MenuViewHolder>() {

    private var selectedPosition = -1

    inner class MenuViewHolder(val binding: ItemSecurityTypeBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuViewHolder {
        val binding = ItemSecurityTypeBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MenuViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON>uViewHolder, position: Int) {
        val itemtype = items[position]
        holder.binding.tvTypeTitle.text = itemtype.type
        holder.binding.tvTypeDes.text = itemtype.des
        holder.binding.ivTypeIcon.setImageResource(itemtype.image)

        // Apply theme-aware styling based on selection
       /* if (selectedPosition == position) {
            holder.binding.cvMain.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, R.color.app_color))
            holder.binding.cvIconBackground.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
            holder.binding.tvMenuName.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
            holder.binding.tvDescripton.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
            holder.binding.ivIcon.setColorFilter(ContextCompat.getColor(holder.itemView.context, R.color.app_color))
        } else {
            // Normal state - use theme-aware colors
            holder.binding.cvMain.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, R.color.card_background))
            holder.binding.cvIconBackground.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, R.color.surface))
            holder.binding.tvMenuName.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.text_primary))
            holder.binding.tvDescripton.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.text_secondary))
            holder.binding.ivIcon.setColorFilter(ContextCompat.getColor(holder.itemView.context, R.color.text_primary))
        }*/

        holder.binding.carSecurityType.setOnClickListener {
           /* val previousPosition = selectedPosition
            selectedPosition = position

            if (previousPosition != -1) {
                notifyItemChanged(previousPosition)
            }
            notifyItemChanged(position)*/
            listener.onTypeClick(itemtype)
        }

    }

    override fun getItemCount() = items.size


    interface  onClickEventListner{
        fun onTypeClick(item: SecurityTypeModel)
    }


}
