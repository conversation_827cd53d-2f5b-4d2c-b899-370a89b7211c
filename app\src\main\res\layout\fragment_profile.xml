<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:fillViewport="true">

   <androidx.recyclerview.widget.RecyclerView
       android:id="@+id/rv_profile_menu"
       app:layout_constraintTop_toTopOf="parent"
       android:layout_width="match_parent"
       android:layout_height="match_parent">
   </androidx.recyclerview.widget.RecyclerView>



</androidx.constraintlayout.widget.ConstraintLayout>
