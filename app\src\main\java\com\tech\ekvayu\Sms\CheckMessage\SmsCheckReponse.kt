package com.tech.ekvayu.Sms.CheckMessage
import com.google.gson.annotations.SerializedName

data class SmsCheckReponse(
    @SerializedName("message"                   ) var message                 : String? = null,
    @SerializedName("STATUS"                    ) var STATUS                  : String? = null,
    @SerializedName("Code"                      ) var Code                    : Int?    = null,
    @SerializedName("message_status"            ) var messageStatus           : String? = null,
    @SerializedName("device_id"                 ) var deviceId                : String? = null,
    @SerializedName("hashId"                    ) var hashId                  : String? = null,
    @SerializedName("unsafe_reasons"            ) var unsafeReasons           : String? = null,
    @SerializedName("extracted_urls_count"      ) var extractedUrlsCount      : Int?    = null,
    @SerializedName("suspicious_keywords_found" ) var suspiciousKeywordsFound : Int?    = null
)
