package com.tech.ekvayu.Fragments

data class MmsMessage(
    val id: Long,
    val address: String,
    val subject: String,
    val date: Long,
    val isRead: Boolean,
    val messageBox: Int,
    val messageSize: Int,
    val parts: List<MmsPart>,
    var suspiciousScore: Int = 0,
    var suspiciousReasons: List<String> = emptyList()
) {
    fun getFormattedDate(): String {
        return java.text.SimpleDateFormat("MMM dd, yyyy HH:mm", java.util.Locale.getDefault())
            .format(java.util.Date(date))
    }
    
    fun getFormattedSize(): String {
        return when {
            messageSize < 1024 -> "${messageSize} B"
            messageSize < 1024 * 1024 -> "${messageSize / 1024} KB"
            else -> "${messageSize / (1024 * 1024)} MB"
        }
    }
    
    fun getThreatLevel(): ThreatLevel {
        return when {
            suspiciousScore >= 75 -> ThreatLevel.HIGH
            suspiciousScore >= 50 -> ThreatLevel.MEDIUM
            suspiciousScore >= 25 -> ThreatLevel.LOW
            else -> ThreatLevel.SAFE
        }
    }

    fun hasAttachments(): Boolean {
        return parts.any { it.contentType.startsWith("image/") || 
                          it.contentType.startsWith("video/") || 
                          it.contentType.startsWith("audio/") ||
                          it.contentType.startsWith("application/") }
    }
    
    fun getTextContent(): String {
        return parts.filter { it.contentType.startsWith("text/") }
            .joinToString(" ") { it.text }
    }
}

data class MmsPart(
    val id: Long,
    val contentType: String,
    val text: String,
    val data: String,
    val name: String
) {
    fun isImage(): Boolean = contentType.startsWith("image/")
    fun isVideo(): Boolean = contentType.startsWith("video/")
    fun isAudio(): Boolean = contentType.startsWith("audio/")
    fun isText(): Boolean = contentType.startsWith("text/")
    fun isApplication(): Boolean = contentType.startsWith("application/")
}

enum class ThreatLevel(val displayName: String, val colorRes: Int) {
    SAFE("Safe", android.R.color.holo_green_light),
    LOW("Low Risk", android.R.color.holo_orange_light),
    MEDIUM("Medium Risk", android.R.color.holo_orange_dark),
    HIGH("High Risk", android.R.color.holo_red_dark)
}
