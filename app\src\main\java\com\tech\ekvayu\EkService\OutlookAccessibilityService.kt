package com.tech.ekvayu.EkService


import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Toast
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.EmailDetectionHelper
import com.tech.ekvayu.BaseClass.PopupManager
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.R
import com.tech.ekvayu.Request.PendingMailRequest
import com.tech.ekvayu.Response.EmailResponse
import com.tech.ekvayu.Response.HashResponse
import com.tech.ekvayu.Response.PedingMailRes
import com.tech.ekvayu.databinding.LayoutProgressBinding
import com.tech.ekvayu.databinding.LayoutValidationBinding
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.io.BufferedReader
import java.io.File
import java.io.FileOutputStream
import java.io.FileReader
import java.io.IOException
import kotlin.String


class OutlookAccessibilityService : AccessibilityService() {

    companion object {
        private const val TAG = "OutlookAccessibilityService"
        private const val OUTLOOK_PACKAGE = "com.microsoft.office.outlook"
        private const val EXTRACTION_DELAY_MS = 500L
        private const val MAX_SCAN_DEPTH = 15
        private const val MIN_CONTENT_LENGTH = 3
    }

    private var currentPopupView: View? = null
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null) return

        // Only process click events on Outlook
        if (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED) {
            if (event.packageName == OUTLOOK_PACKAGE) {
                Log.d(TAG, "🔍 Outlook click detected, starting email extraction...")

                Handler(Looper.getMainLooper()).postDelayed({
                    try {
                        val rootNode = rootInActiveWindow
                        if (rootNode != null) {
                            extractEmailDetails(rootNode)
                        } else {
                            Log.w(TAG, "⚠️ Root node is null, cannot extract email details")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ Error in accessibility service: ${e.message}", e)
                    }
                }, EXTRACTION_DELAY_MS)
            }
        }
    }

    override fun onInterrupt() {
        Log.d(TAG, "🛑 Accessibility service interrupted")
    }

    override fun onServiceConnected() {
        super.onServiceConnected()
        Log.d(TAG, "✅ Outlook Accessibility Service connected")
    }

    override fun onDestroy() {
        super.onDestroy()
        // Clean up any overlay views
        currentPopupView?.let { view ->
            try {
                val windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
                windowManager.removeView(view)
            } catch (e: Exception) {
                Log.e(TAG, "Error removing overlay view: ${e.message}")
            }
        }
        Log.d(TAG, "🔄 Outlook Accessibility Service destroyed")
    }




    private fun extractEmailDetails(rootNode: AccessibilityNodeInfo) {
        Log.d("OutlookExtraction", "🔍 Starting email extraction from Outlook...")

        // Initialize email data variables
        var senderName: String? = null
        var fromEmail: String? = null
        var subject: String? = null
        var date: String? = null
        var attachments: String? = null

        val toEmails = mutableListOf<String>()
        val ccEmails = mutableListOf<String>()
        val bccEmails = mutableListOf<String>()
        val emailBodyBuilder = StringBuilder()
        val attachmentSet = mutableSetOf<String>()

        // Collect all nodes for processing
        val possibleNodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, possibleNodes)

        Log.d("OutlookExtraction", "📊 Found ${possibleNodes.size} nodes to analyze")

        // FIRST PASS: Extract sender information first
        Log.d("OutlookExtraction", "🔍 FIRST PASS: Extracting sender information...")

        // Strategy 1: Look for explicit sender patterns first
        for (node in possibleNodes) {
            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()

            val actualText = text ?: contentDesc ?: continue
            if (actualText.isEmpty()) continue

            // Log every node we're checking for sender
            Log.d("SenderScan", "🔍 Checking: '$actualText' | ViewId: '$viewId' | ContentDesc: '$contentDesc'")

            // Only extract sender info in first pass
            if (fromEmail == null) {
                extractSenderInfo(actualText, viewId, contentDesc)?.let { (name, email) ->
                    senderName = name
                    fromEmail = email
                    Log.d("SenderExtraction", "👤 FIRST PASS - Found sender: $name <$email>")
                }
            }
        }

        // Strategy 2: If no sender found, look for PRIORITIZED emails
        if (fromEmail == null) {
            Log.d("OutlookExtraction", "🔍 FALLBACK: Looking for prioritized sender emails...")

            // First, look for emails with sender-like context
            for (node in possibleNodes) {
                val text = node.text?.toString()?.trim() ?: continue
                val viewId = node.viewIdResourceName
                val contentDesc = node.contentDescription?.toString()?.trim()

                // Check if this looks like sender information
                val hasSenderIndicators = text.contains("•") ||
                                        text.contains("Message from", true) ||
                                        text.contains("Sent by", true) ||
                                        viewId?.contains("sender", true) == true ||
                                        viewId?.contains("from", true) == true ||
                                        contentDesc?.contains("sender", true) == true

                if (hasSenderIndicators && text.contains("@")) {
                    val emails = text.extractEmails()
                    if (emails.isNotEmpty()) {
                        fromEmail = emails[0]
                        // Try to extract name from the same text - IMPROVED
                        var nameCandidate = text.replace(fromEmail!!, "").trim()
                            .removePrefix("From:").removePrefix("Message from").removePrefix("•").trim()

                        // Clean up common patterns that shouldn't be in sender name
                        nameCandidate = nameCandidate
                            .replace(Regex("Sent to:.*", RegexOption.IGNORE_CASE), "")
                            .replace(Regex("Received on.*", RegexOption.IGNORE_CASE), "")
                            .replace(Regex("\\. Sent.*", RegexOption.IGNORE_CASE), "")
                            .replace(Regex("\\. Received.*", RegexOption.IGNORE_CASE), "")
                            .split(".")[0] // Take only the first sentence
                            .trim()

                        if (nameCandidate.isNotEmpty() &&
                            nameCandidate.length > 1 &&
                            nameCandidate.length < 50 && // Reasonable name length
                            !nameCandidate.contains("@") &&
                            !nameCandidate.contains("Sent", true) &&
                            !nameCandidate.contains("Received", true)) {
                            senderName = nameCandidate
                        }
                        Log.d("SenderExtraction", "👤 FALLBACK PRIORITY - Found sender: $senderName <$fromEmail>")
                        break
                    }
                }
            }

            // If still no sender, look for any email but prefer shorter/simpler ones
            if (fromEmail == null) {
                Log.d("OutlookExtraction", "🔍 FALLBACK GENERAL: Looking for any email as sender...")
                val allEmails = mutableListOf<String>()

                for (node in possibleNodes) {
                    val text = node.text?.toString()?.trim() ?: continue
                    if (text.contains("@")) {
                        allEmails.addAll(text.extractEmails())
                    }
                }

                // Prefer shorter emails (likely to be actual sender addresses)
                val sortedEmails = allEmails.distinct().sortedBy { it.length }
                if (sortedEmails.isNotEmpty()) {
                    fromEmail = sortedEmails[0]
                    Log.d("SenderExtraction", "👤 FALLBACK GENERAL - Found sender: $fromEmail")
                }
            }
        }

        // Strategy 3: Ultra-aggressive but selective scan
        if (fromEmail == null) {
            Log.d("OutlookExtraction", "🆘 ULTRA-AGGRESSIVE: Scanning all text for emails...")
            val allText = possibleNodes.mapNotNull { it.text?.toString() }.joinToString(" ")
            val allEmails = allText.extractEmails().distinct()

            if (allEmails.isNotEmpty()) {
                // Prefer emails that appear in sender-like contexts
                val senderLikeEmail = allEmails.find { email ->
                    possibleNodes.any { node ->
                        val text = node.text?.toString() ?: ""
                        val viewId = node.viewIdResourceName
                        val contentDesc = node.contentDescription?.toString()

                        text.contains(email) && (
                            text.contains("•") ||
                            text.contains("Message from", true) ||
                            viewId?.contains("sender", true) == true ||
                            viewId?.contains("from", true) == true ||
                            contentDesc?.contains("sender", true) == true
                        )
                    }
                }

                fromEmail = senderLikeEmail ?: allEmails.minByOrNull { it.length }
                Log.d("SenderExtraction", "👤 ULTRA-AGGRESSIVE - Found sender: $fromEmail")
            }
        }

        Log.d("OutlookExtraction", "📧 FIRST PASS COMPLETE - Sender identified: $senderName <$fromEmail>")

        // SECOND PASS: Extract all other data, now that we know the sender
        Log.d("OutlookExtraction", "🔍 SECOND PASS: Extracting recipients and other data...")
        Log.d("OutlookExtraction", "🚫 SENDER TO EXCLUDE: '$fromEmail'")

        for (node in possibleNodes) {
            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()
            val className = node.className?.toString()

            val actualText = text ?: contentDesc ?: continue
            if (actualText.isEmpty()) continue

            Log.d("NodeAnalysis", "📝 Text: '$actualText' | ViewId: '$viewId' | Class: '$className'")

            // Skip any text that contains the sender email
            val currentFromEmail = fromEmail
            if (currentFromEmail != null && actualText.contains(currentFromEmail, ignoreCase = true)) {
                Log.d("SenderFilter", "🚫 SKIPPING - Contains sender email: '$actualText'")
                continue
            }

            // 1. EXTRACT SUBJECT
            if (subject == null) {
                extractSubject(actualText, viewId, contentDesc)?.let { extractedSubject ->
                    subject = extractedSubject
                    Log.d("SubjectExtraction", "📧 Found subject: $subject")
                }
            }

            // 2. EXTRACT TO EMAILS (now we can filter out sender)
            extractToEmails(actualText, viewId, contentDesc, currentFromEmail)?.let { emails ->
                emails.forEach { email ->
                    if (!toEmails.contains(email) &&
                        email != currentFromEmail &&
                        !email.equals(currentFromEmail, ignoreCase = true)) {
                        toEmails.add(email)
                        Log.d("ToExtraction", "📬 Found TO email: $email")
                    } else {
                        Log.d("ToExtraction", "🚫 FILTERED OUT - Sender email: $email")
                    }
                }
            }

            // 3. EXTRACT CC EMAILS
            extractCcEmails(actualText, viewId, contentDesc)?.let { emails ->
                emails.forEach { email ->
                    if (!ccEmails.contains(email) &&
                        email != currentFromEmail &&
                        !email.equals(currentFromEmail, ignoreCase = true)) {
                        ccEmails.add(email)
                        Log.d("CcExtraction", "📋 Found CC email: $email")
                    } else {
                        Log.d("CcExtraction", "🚫 FILTERED OUT - Sender email: $email")
                    }
                }
            }

            // 4. EXTRACT BCC EMAILS
            extractBccEmails(actualText, viewId, contentDesc)?.let { emails ->
                emails.forEach { email ->
                    if (!bccEmails.contains(email) &&
                        email != currentFromEmail &&
                        !email.equals(currentFromEmail, ignoreCase = true)) {
                        bccEmails.add(email)
                        Log.d("BccExtraction", "🔒 Found BCC email: $email")
                    } else {
                        Log.d("BccExtraction", "🚫 FILTERED OUT - Sender email: $email")
                    }
                }
            }

            // 5. EXTRACT DATE
            if (date == null) {
                extractDate(actualText, viewId, contentDesc)?.let { extractedDate ->
                    date = extractedDate
                    Log.d("DateExtraction", "📅 Found date: $date")
                }
            }

            // 6. EXTRACT BODY CONTENT
            if (detectEmailBodyContent(actualText, viewId, contentDesc, node)) {
                emailBodyBuilder.appendLine(actualText)
                Log.d("BodyExtraction", "📄 Added to body: ${actualText.take(50)}...")
            }

            // 7. EXTRACT ATTACHMENTS (Enhanced)
            extractAttachments(actualText, viewId, contentDesc)?.let { attachment ->
                attachmentSet.add(attachment)
                Log.d("AttachmentExtraction", "📎 Found attachment: $attachment")
            }

            // Additional attachment detection strategies
            detectAdditionalAttachments(actualText, viewId, contentDesc, attachmentSet)
        }

        // Perform additional aggressive scans for missing data
        performAdditionalScans(possibleNodes, toEmails, ccEmails, bccEmails, attachmentSet, emailBodyBuilder, fromEmail)

        // FINAL AGGRESSIVE SCAN for missing critical data
        if (fromEmail == null || fromEmail == "<EMAIL>" || subject == null || date == null) {
            Log.d("OutlookExtraction", "🚨 CRITICAL DATA MISSING - Performing final aggressive scan...")
            performFinalAggressiveScan(possibleNodes, senderName, fromEmail, subject, date)?.let { result ->
                if (fromEmail == null || fromEmail == "<EMAIL>") fromEmail = result.fromEmail
                if (senderName == null) senderName = result.senderName
                if (subject == null) subject = result.subject
                if (date == null) date = result.date
            }
        }

        // LAST RESORT: If still no sender email, take the first email we can find
        if (fromEmail == null || fromEmail == "<EMAIL>") {
            Log.d("OutlookExtraction", "🆘 LAST RESORT - Looking for ANY email as sender...")
            for (node in possibleNodes) {
                val text = node.text?.toString()?.trim() ?: continue
                if (text.contains("@")) {
                    val emails = text.extractEmails()
                    if (emails.isNotEmpty()) {
                        fromEmail = emails[0]
                        Log.d("OutlookExtraction", "🆘 LAST RESORT - Using email as sender: $fromEmail")
                        break
                    }
                }
            }
        }

        // COMPREHENSIVE FINAL VALIDATION: Remove sender email from all recipient lists
        if (fromEmail != null) {
            val finalFromEmail = fromEmail // Create local copy for smart cast
            val originalToSize = toEmails.size
            val originalCcSize = ccEmails.size
            val originalBccSize = bccEmails.size

            // Remove sender email (case-insensitive)
            toEmails.removeAll { it.equals(finalFromEmail, ignoreCase = true) }
            ccEmails.removeAll { it.equals(finalFromEmail, ignoreCase = true) }
            bccEmails.removeAll { it.equals(finalFromEmail, ignoreCase = true) }

            val removedFromTo = originalToSize - toEmails.size
            val removedFromCc = originalCcSize - ccEmails.size
            val removedFromBcc = originalBccSize - bccEmails.size

            Log.d("EmailValidation", """
                🔍 ===== FINAL VALIDATION CLEANUP =====
                🚫 Sender email to remove: '$finalFromEmail'
                📬 TO emails removed: $removedFromTo
                📋 CC emails removed: $removedFromCc
                🔒 BCC emails removed: $removedFromBcc
                ✅ Final TO list: ${toEmails.joinToString(", ")}
                =====================================
            """.trimIndent())
        }

        // Log final email separation with detailed validation
        val finalFromEmailForValidation = fromEmail
        val senderInToList = toEmails.any { it.equals(finalFromEmailForValidation, ignoreCase = true) }
        val senderInCcList = ccEmails.any { it.equals(finalFromEmailForValidation, ignoreCase = true) }
        val senderInBccList = bccEmails.any { it.equals(finalFromEmailForValidation, ignoreCase = true) }

        Log.d("EmailSeparation", """
            📧 ===== EMAIL SEPARATION VALIDATION =====
            👤 FROM: $fromEmail
            📬 TO: ${toEmails.joinToString(", ")}
            📋 CC: ${ccEmails.joinToString(", ")}
            🔒 BCC: ${bccEmails.joinToString(", ")}

            🔍 VALIDATION CHECKS:
            ✅ Sender NOT in TO list: ${!senderInToList}
            ✅ Sender NOT in CC list: ${!senderInCcList}
            ✅ Sender NOT in BCC list: ${!senderInBccList}
            ✅ Overall separation: ${!senderInToList && !senderInCcList && !senderInBccList}
            ==========================================
        """.trimIndent())

        // Finalize extracted data with fallbacks
        val finalData = finalizeEmailData(
            senderName, fromEmail, toEmails, ccEmails, bccEmails,
            subject, date, emailBodyBuilder, attachmentSet
        )

        // Log final extraction results
        logExtractionResults(finalData)

        // Save and process the email
      //  processExtractedEmail(finalData)
    }

    // ==================== NODE SCANNING METHODS ====================

    /**
     * Recursively find all nodes that might contain email information
     */
    private fun findEmailNodes(rootNode: AccessibilityNodeInfo, result: MutableList<AccessibilityNodeInfo>, depth: Int = 0) {
        if (depth > MAX_SCAN_DEPTH) {
            Log.w(TAG, "⚠️ Maximum scan depth reached, stopping recursion")
            return
        }

        try {
            // Add current node if it has useful content
            if (isUsefulNode(rootNode)) {
                result.add(rootNode)
            }

            // Recursively scan children
            for (i in 0 until rootNode.childCount) {
                rootNode.getChild(i)?.let { child ->
                    findEmailNodes(child, result, depth + 1)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error scanning node at depth $depth: ${e.message}")
        }
    }

    /**
     * Check if a node contains useful information for email extraction
     */
    private fun isUsefulNode(node: AccessibilityNodeInfo): Boolean {
        val text = node.text?.toString()?.trim()
        val viewId = node.viewIdResourceName
        val contentDesc = node.contentDescription?.toString()?.trim()
        val className = node.className?.toString()

        // Skip nodes with no useful content
        if (text.isNullOrEmpty() && contentDesc.isNullOrEmpty()) return false

        // Include nodes with text content
        if (!text.isNullOrEmpty() && text.length >= MIN_CONTENT_LENGTH) return true

        // Include nodes with useful content descriptions
        if (!contentDesc.isNullOrEmpty() && contentDesc.length >= MIN_CONTENT_LENGTH) return true

        // Include nodes with email-related view IDs
        val emailRelatedIds = listOf(
            "subject", "sender", "recipient", "to", "cc", "bcc", "from", "date", "time",
            "body", "content", "message", "attachment", "email", "mail"
        )

        if (viewId != null && emailRelatedIds.any { viewId.contains(it, true) }) return true

        // Include text views and edit texts that might contain email content
        if (className in listOf("android.widget.TextView", "android.widget.EditText")) return true

        return false
    }

    // ==================== EMAIL EXTRACTION HELPER METHODS ====================

    /**
     * Extract sender name and email from text - PRIORITIZED APPROACH
     */
    private fun extractSenderInfo(text: String, viewId: String?, contentDesc: String?): Pair<String?, String?>? {
        Log.d("SenderExtraction", "🔍 Analyzing text: '$text' | ViewId: '$viewId' | ContentDesc: '$contentDesc'")

        // PRIORITY 1: "Name • <EMAIL>" (Outlook specific format) - HIGHEST PRIORITY
        if (text.contains("•")) {
            val parts = text.split("•").map { it.trim() }
            if (parts.size >= 2) {
                var name = parts[0].takeIf { it.isNotEmpty() && !it.contains("@") }
                val emailPart = parts[1]
                val email = emailPart.extractEmail() ?: if (emailPart.isValidEmail()) emailPart else null

                if (email != null) {
                    // Clean up the name - remove common prefixes and suffixes
                    name = name?.let { cleanSenderName(it) }
                    Log.d("SenderExtraction", "✅ PRIORITY 1 (•): Name='$name', Email='$email'")
                    return Pair(name, email)
                }
            }
        }

        // PRIORITY 2: Strong sender context indicators - SECOND HIGHEST
        val isStrongSenderContext = viewId?.contains("sender", true) == true ||
                                   viewId?.contains("from", true) == true ||
                                   contentDesc?.contains("sender", true) == true ||
                                   contentDesc?.contains("from", true) == true ||
                                   text.startsWith("From:", true)

        if (isStrongSenderContext) {
            val cleanText = text.removePrefix("From:").trim()

            // Check for "Name <<EMAIL>>" in sender context
            val emailInBrackets = Regex("<([^>]+)>").find(cleanText)
            if (emailInBrackets != null) {
                val email = emailInBrackets.groupValues[1].trim()
                val name = cleanText.replace(emailInBrackets.value, "").trim().takeIf { it.isNotEmpty() }
                if (email.isValidEmail()) {
                    Log.d("SenderExtraction", "✅ PRIORITY 2A (context+<>): Name='$name', Email='$email'")
                    return Pair(name, email)
                }
            }

            // Direct email in sender context
            if (cleanText.isValidEmail()) {
                Log.d("SenderExtraction", "✅ PRIORITY 2B (context): Email='$cleanText'")
                return Pair(null, cleanText)
            }

            // Extract first email from sender context
            val extractedEmail = cleanText.extractEmail()
            if (extractedEmail != null) {
                val name = cleanText.replace(extractedEmail, "").trim().takeIf { it.isNotEmpty() }
                Log.d("SenderExtraction", "✅ PRIORITY 2C (context+extract): Name='$name', Email='$extractedEmail'")
                return Pair(name, extractedEmail)
            }
        }

        // PRIORITY 3: "Name <<EMAIL>>" without strong context - MEDIUM PRIORITY
        val emailInBrackets = Regex("<([^>]+)>").find(text)
        if (emailInBrackets != null) {
            val email = emailInBrackets.groupValues[1].trim()
            val name = text.replace(emailInBrackets.value, "").trim().takeIf { it.isNotEmpty() }
            if (email.isValidEmail()) {
                Log.d("SenderExtraction", "✅ PRIORITY 3 (<>): Name='$name', Email='$email'")
                return Pair(name, email)
            }
        }

        // PRIORITY 4: Skip general email extraction to avoid false positives
        // We'll handle this in the fallback strategies in the main method

        return null
    }

    /**
     * Clean sender name by removing common unwanted patterns
     */
    private fun cleanSenderName(name: String): String? {
        var cleanName = name.trim()

        // Remove common prefixes
        cleanName = cleanName
            .removePrefix("Message from")
            .removePrefix("From:")
            .removePrefix("Sent by")
            .trim()

        // Remove common suffixes and patterns
        cleanName = cleanName
            .replace(Regex("\\. Sent to:.*", RegexOption.IGNORE_CASE), "")
            .replace(Regex("\\. Received on.*", RegexOption.IGNORE_CASE), "")
            .replace(Regex("Sent to:.*", RegexOption.IGNORE_CASE), "")
            .replace(Regex("Received on.*", RegexOption.IGNORE_CASE), "")
            .split(".")[0] // Take only the first sentence
            .trim()

        // Validate the cleaned name
        return if (cleanName.isNotEmpty() &&
                  cleanName.length in 2..50 && // Reasonable length
                  !cleanName.contains("@") &&
                  !cleanName.contains("Sent", true) &&
                  !cleanName.contains("Received", true) &&
                  !cleanName.contains("Message", true)) {
            cleanName
        } else {
            null
        }
    }

    /**
     * Extract subject from text - AGGRESSIVE APPROACH
     */
    private fun extractSubject(text: String, viewId: String?, contentDesc: String?): String? {
        Log.d("SubjectExtraction", "🔍 Analyzing text: '$text' | ViewId: '$viewId'")

        // Pattern 1: Explicit subject indicators
        val isSubjectContext = viewId?.contains("subject", true) == true ||
                              contentDesc?.contains("subject", true) == true ||
                              text.startsWith("Subject:", true) ||
                              text.startsWith("Re:", true) ||
                              text.startsWith("Fwd:", true) ||
                              text.startsWith("FW:", true)

        if (isSubjectContext) {
            val cleanSubject = text.removePrefix("Subject:")
                                  .removePrefix("Re:")
                                  .removePrefix("Fwd:")
                                  .removePrefix("FW:")
                                  .trim()
            if (cleanSubject.isNotEmpty()) {
                Log.d("SubjectExtraction", "✅ Found subject: '$cleanSubject'")
                return cleanSubject
            }
        }

        // Pattern 2: Look for conversation or message title patterns
        val titlePatterns = listOf("conversation", "message", "title", "header")
        val isTitleContext = titlePatterns.any {
            viewId?.contains(it, true) == true || contentDesc?.contains(it, true) == true
        }

        if (isTitleContext && text.length > 5 && !text.contains("@") && !text.matches(Regex(".*\\d{1,2}[:/]\\d{1,2}.*"))) {
            Log.d("SubjectExtraction", "✅ Found subject from title context: '$text'")
            return text.trim()
        }

        // Pattern 3: Text that looks like a subject (not email, not date, reasonable length)
        if (text.length in 5..100 &&
            !text.contains("@") &&
            !text.contains("AM", true) &&
            !text.contains("PM", true) &&
            !text.matches(Regex(".*\\d{1,2}[:/]\\d{1,2}.*")) &&
            text.split(" ").size >= 2) {

            // Additional checks to avoid false positives
            val isLikelySubject = !text.startsWith("To:", true) &&
                                 !text.startsWith("From:", true) &&
                                 !text.startsWith("Cc:", true) &&
                                 !text.contains("•") &&
                                 text.any { it.isLetter() }

            if (isLikelySubject) {
                Log.d("SubjectExtraction", "✅ Found subject from pattern: '$text'")
                return text.trim()
            }
        }

        return null
    }

    /**
     * Extract TO emails from text - STRICT SENDER FILTERING
     */
    private fun extractToEmails(text: String, viewId: String?, contentDesc: String?, senderEmail: String?): List<String>? {
        val emails = mutableListOf<String>()

        Log.d("ToExtraction", "🔍 Analyzing for TO emails: '$text' | Sender to exclude: '$senderEmail'")

        // STRICT CHECK: If text contains sender email, skip entirely
        if (senderEmail != null && text.contains(senderEmail, ignoreCase = true)) {
            Log.d("ToExtraction", "🚫 SKIPPING - Text contains sender email: '$text'")
            return null
        }

        // Pattern 1: Explicit "To:" prefix
        if (text.startsWith("To:", true)) {
            val extractedEmails = text.extractEmails()
            extractedEmails.forEach { email ->
                if (!email.equals(senderEmail, ignoreCase = true)) {
                    emails.add(email)
                    Log.d("ToExtraction", "✅ Found TO email from 'To:' prefix: $email")
                } else {
                    Log.d("ToExtraction", "🚫 FILTERED - Sender in To: field: $email")
                }
            }
        }

        // Pattern 2: ViewId indicates TO field (but NOT sender field)
        val isSenderContext = viewId?.contains("sender", true) == true ||
                             viewId?.contains("from", true) == true ||
                             contentDesc?.contains("sender", true) == true ||
                             contentDesc?.contains("from", true) == true

        val isToContext = (viewId?.contains("to", true) == true ||
                          viewId?.contains("recipient", true) == true ||
                          viewId?.contains("addressee", true) == true ||
                          contentDesc?.contains("to", true) == true ||
                          contentDesc?.contains("recipient", true) == true) &&
                          !isSenderContext

        if (isToContext && text.contains("@")) {
            val extractedEmails = text.extractEmails()
            extractedEmails.forEach { email ->
                if (!email.equals(senderEmail, ignoreCase = true)) {
                    emails.add(email)
                    Log.d("ToExtraction", "✅ Found TO email from context: $email (ViewId: $viewId)")
                } else {
                    Log.d("ToExtraction", "🚫 FILTERED - Sender in recipient context: $email")
                }
            }
        }

        // Pattern 3: STRICT - Only if we have explicit recipient indicators
        // AND it's not in a sender context
        if (!isSenderContext && text.isValidEmail() && !text.equals(senderEmail, ignoreCase = true)) {
            // Additional check: must have recipient-related context
            val hasRecipientIndicators = viewId?.let { id ->
                id.contains("recipient", true) ||
                id.contains("to", true) ||
                id.contains("addressee", true)
            } ?: false || contentDesc?.let { desc ->
                desc.contains("recipient", true) ||
                desc.contains("to", true) ||
                desc.contains("sent to", true)
            } ?: false

            if (hasRecipientIndicators) {
                emails.add(text)
                Log.d("ToExtraction", "✅ Found TO email from indicators: $text")
            }
        }

        return emails.takeIf { it.isNotEmpty() }
    }

    /**
     * Extract CC emails from text
     */
    private fun extractCcEmails(text: String, viewId: String?, contentDesc: String?): List<String>? {
        if (text.startsWith("Cc:", true) ||
            viewId?.contains("cc", true) == true ||
            contentDesc?.contains("cc", true) == true) {
            return text.extractEmails().takeIf { it.isNotEmpty() }
        }
        return null
    }

    /**
     * Extract BCC emails from text
     */
    private fun extractBccEmails(text: String, viewId: String?, contentDesc: String?): List<String>? {
        if (text.startsWith("Bcc:", true) ||
            viewId?.contains("bcc", true) == true ||
            contentDesc?.contains("bcc", true) == true) {
            return text.extractEmails().takeIf { it.isNotEmpty() }
        }
        return null
    }

    /**
     * Extract date from text - AGGRESSIVE APPROACH
     */
    private fun extractDate(text: String, viewId: String?, contentDesc: String?): String? {
        Log.d("DateExtraction", "🔍 Analyzing text: '$text' | ViewId: '$viewId'")

        // Pattern 1: Context-based detection
        val isDateContext = viewId?.contains("date", true) == true ||
                           viewId?.contains("time", true) == true ||
                           contentDesc?.contains("date", true) == true ||
                           contentDesc?.contains("time", true) == true

        // Pattern 2: Multiple date/time patterns
        val datePatterns = listOf(
            Regex(".*\\d{1,2}[:/]\\d{1,2}[:/]\\d{2,4}.*"),           // 12/31/2024, 12:30:45
            Regex(".*\\d{1,2} [A-Za-z]{3,9} \\d{4}.*"),              // 31 December 2024
            Regex(".*\\d{4}-\\d{2}-\\d{2}.*"),                       // 2024-12-31
            Regex(".*[A-Za-z]{3,9} \\d{1,2}, \\d{4}.*"),             // December 31, 2024
            Regex(".*\\d{1,2}:\\d{2}\\s*(AM|PM).*", RegexOption.IGNORE_CASE), // 2:30 PM
            Regex(".*\\d{1,2}\\s*(AM|PM).*", RegexOption.IGNORE_CASE),         // 2 PM
            Regex(".*\\b(Today|Yesterday|Tomorrow)\\b.*", RegexOption.IGNORE_CASE), // Today, Yesterday
            Regex(".*\\b(Mon|Tue|Wed|Thu|Fri|Sat|Sun)\\b.*", RegexOption.IGNORE_CASE), // Mon, Tue, etc.
            Regex(".*\\b(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)\\b.*", RegexOption.IGNORE_CASE)
        )

        val hasTimeIndicators = text.contains("AM", true) || text.contains("PM", true)
        val matchesDatePattern = datePatterns.any { it.matches(text) }

        if (isDateContext || hasTimeIndicators || matchesDatePattern) {
            Log.d("DateExtraction", "✅ Found date: '$text'")
            return text.trim().takeIf { it.isNotEmpty() }
        }

        // Pattern 3: Look for relative time expressions
        val relativeTimePatterns = listOf(
            "ago", "minutes", "hours", "days", "weeks", "months", "years",
            "just now", "recently", "earlier"
        )

        if (relativeTimePatterns.any { text.contains(it, true) }) {
            Log.d("DateExtraction", "✅ Found relative date: '$text'")
            return text.trim()
        }

        return null
    }

    /**
     * Extract attachments from text
     */
    private fun extractAttachments(text: String, viewId: String?, contentDesc: String?): String? {
        val attachmentKeywords = listOf(
            "attachment", "attached", "file", "document", "download", "paperclip",
            "📎", ".pdf", ".docx", ".xlsx", ".pptx", ".txt", ".zip", ".jpg", ".png"
        )

        val isAttachmentContext = viewId?.contains("attachment", true) == true ||
                                 contentDesc?.contains("attachment", true) == true ||
                                 attachmentKeywords.any { text.contains(it, true) }

        if (isAttachmentContext) {
            return text.trim().takeIf { it.isNotEmpty() }
        }

        return null
    }

    /**
     * Detect additional attachments with enhanced patterns
     */
    private fun detectAdditionalAttachments(text: String, viewId: String?, contentDesc: String?, attachmentSet: MutableSet<String>) {
        // Pattern 1: File extensions
        val fileExtensionPattern = Regex(
            "\\b[\\w\\s-]+\\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt|zip|rar|jpg|jpeg|png|gif|mp4|avi|mp3|wav)\\b",
            RegexOption.IGNORE_CASE
        )
        val fileMatches = fileExtensionPattern.findAll(text)
        for (match in fileMatches) {
            val fileName = match.value.trim()
            if (fileName.length > 3) {
                attachmentSet.add(fileName)
                Log.d("AttachmentDetection", "📎 Found file by extension: $fileName")
            }
        }

        // Pattern 2: Attachment count indicators
        val countPatterns = listOf(
            Regex("(\\d+)\\s*attachment[s]?", RegexOption.IGNORE_CASE),
            Regex("(\\d+)\\s*file[s]?\\s*attached", RegexOption.IGNORE_CASE),
            Regex("attachment[s]?\\s*\\((\\d+)\\)", RegexOption.IGNORE_CASE),
            Regex("📎\\s*(\\d+)", RegexOption.IGNORE_CASE)
        )

        for (pattern in countPatterns) {
            val match = pattern.find(text)
            if (match != null) {
                val count = match.groupValues[1]
                attachmentSet.add("$count attachment(s)")
                Log.d("AttachmentDetection", "📎 Found attachment count: $count")
            }
        }

        // Pattern 3: ViewId-based detection
        val attachmentViewIds = listOf("attachment", "file", "document", "download", "clip")
        if (attachmentViewIds.any { viewId?.contains(it, true) == true }) {
            attachmentSet.add(text.trim())
            Log.d("AttachmentDetection", "📎 Found by ViewId: ${text.trim()}")
        }

        // Pattern 4: Content description based
        val attachmentContentDescs = listOf("attachment", "file", "document", "attached")
        if (attachmentContentDescs.any { contentDesc?.contains(it, true) == true }) {
            attachmentSet.add(text.trim())
            Log.d("AttachmentDetection", "📎 Found by ContentDesc: ${text.trim()}")
        }
    }

    // ==================== UTILITY METHODS ====================

    fun String.extractEmails(): List<String> {
        return Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}").findAll(this).map { it.value }.toList()
    }

    fun String.isValidEmail(): Boolean {
        return this.matches(Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"))
    }

    fun String.extractEmail(): String? = this.extractEmails().firstOrNull()

    // ==================== FINAL AGGRESSIVE SCANNING ====================

    /**
     * Final aggressive scan for missing critical data
     */
    private fun performFinalAggressiveScan(
        nodes: List<AccessibilityNodeInfo>,
        currentSenderName: String?,
        currentFromEmail: String?,
        currentSubject: String?,
        currentDate: String?
    ): FinalScanResult? {

        Log.d("FinalScan", "🔍 FINAL AGGRESSIVE SCAN - Looking for missing data...")

        var foundSenderName = currentSenderName
        var foundFromEmail = currentFromEmail
        var foundSubject = currentSubject
        var foundDate = currentDate

        for (node in nodes) {
            val text = node.text?.toString()?.trim() ?: continue
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()

            if (text.isEmpty() || text.length < 3) continue

            Log.d("FinalScan", "📝 Scanning: '$text' | ViewId: '$viewId'")

            // AGGRESSIVE EMAIL SEARCH for sender
            if ((foundFromEmail == null || foundFromEmail == "<EMAIL>") && text.contains("@")) {
                val emails = text.extractEmails()
                if (emails.isNotEmpty()) {
                    // Take the first valid email as sender
                    foundFromEmail = emails[0]
                    // Try to extract name from the same text
                    if (foundSenderName == null) {
                        val nameCandidate = text.replace(foundFromEmail, "").trim()
                            .removePrefix("From:").removePrefix("•").trim()
                        if (nameCandidate.isNotEmpty() && nameCandidate.length > 1 && !nameCandidate.contains("@")) {
                            foundSenderName = nameCandidate
                        }
                    }
                    Log.d("FinalScan", "✅ AGGRESSIVE - Found sender: $foundSenderName <$foundFromEmail>")
                }
            }

            // AGGRESSIVE SUBJECT SEARCH
            if (foundSubject == null && text.length in 10..150) {
                // Look for text that could be a subject
                val couldBeSubject = !text.contains("@") &&
                                   !text.contains("AM", true) &&
                                   !text.contains("PM", true) &&
                                   !text.matches(Regex(".*\\d{1,2}[:/]\\d{1,2}.*")) &&
                                   text.split(" ").size >= 3 &&
                                   text.any { it.isLetter() } &&
                                   !text.startsWith("To:", true) &&
                                   !text.startsWith("From:", true) &&
                                   !text.startsWith("Cc:", true)

                if (couldBeSubject) {
                    foundSubject = text
                    Log.d("FinalScan", "✅ AGGRESSIVE - Found subject: '$foundSubject'")
                }
            }

            // AGGRESSIVE DATE SEARCH
            if (foundDate == null) {
                val couldBeDate = text.contains("AM", true) ||
                                text.contains("PM", true) ||
                                text.matches(Regex(".*\\d{1,2}[:/]\\d{1,2}.*")) ||
                                text.matches(Regex(".*\\d{1,2} [A-Za-z]{3,9} \\d{4}.*")) ||
                                text.contains("ago", true) ||
                                text.contains("Today", true) ||
                                text.contains("Yesterday", true) ||
                                text.matches(Regex(".*\\b(Mon|Tue|Wed|Thu|Fri|Sat|Sun)\\b.*", RegexOption.IGNORE_CASE))

                if (couldBeDate) {
                    foundDate = text
                    Log.d("FinalScan", "✅ AGGRESSIVE - Found date: '$foundDate'")
                }
            }
        }

        // Return results if we found anything new
        if (foundFromEmail != currentFromEmail ||
            foundSenderName != currentSenderName ||
            foundSubject != currentSubject ||
            foundDate != currentDate) {

            Log.d("FinalScan", """
                🎯 FINAL SCAN RESULTS:
                👤 Sender: $foundSenderName
                📧 Email: $foundFromEmail
                📝 Subject: $foundSubject
                📅 Date: $foundDate
            """.trimIndent())

            return FinalScanResult(foundSenderName, foundFromEmail, foundSubject, foundDate)
        }

        return null
    }

    private data class FinalScanResult(
        val senderName: String?,
        val fromEmail: String?,
        val subject: String?,
        val date: String?
    )

    // ==================== ADDITIONAL PROCESSING METHODS ====================

    private fun performAdditionalScans(
        nodes: List<AccessibilityNodeInfo>,
        toEmails: MutableList<String>,
        ccEmails: MutableList<String>,
        bccEmails: MutableList<String>,
        attachmentSet: MutableSet<String>,
        emailBodyBuilder: StringBuilder,
        senderEmail: String?
    ) {
        Log.d("AdditionalScans", "🔍 Performing additional scans for missing data...")

        // Aggressive email scanning if TO emails are empty
        if (toEmails.isEmpty()) {
            Log.d("AdditionalScans", "📧 No TO emails found, performing aggressive scan...")
            Log.d("AdditionalScans", "🚫 Sender to exclude: '$senderEmail'")

            for (node in nodes) {
                val text = node.text?.toString()?.trim() ?: continue
                val viewId = node.viewIdResourceName
                val contentDesc = node.contentDescription?.toString()?.trim()

                // STRICT: Skip any text containing sender email
                if (senderEmail != null && text.contains(senderEmail, ignoreCase = true)) {
                    Log.d("AdditionalScans", "🚫 SKIPPING - Contains sender: '$text'")
                    continue
                }

                if (text.isValidEmail() &&
                    !text.equals(senderEmail, ignoreCase = true) && // Case-insensitive check
                    !ccEmails.contains(text) &&
                    !bccEmails.contains(text)) {

                    // Additional check: avoid adding emails from sender-related contexts
                    val isSenderContext = viewId?.contains("sender", true) == true ||
                                         viewId?.contains("from", true) == true ||
                                         contentDesc?.contains("sender", true) == true ||
                                         contentDesc?.contains("from", true) == true

                    if (!isSenderContext) {
                        toEmails.add(text)
                        Log.d("AdditionalScans", "✅ Added fallback TO email: $text")
                    } else {
                        Log.d("AdditionalScans", "🚫 SKIPPED - Sender context: $text")
                    }
                } else if (text.equals(senderEmail, ignoreCase = true)) {
                    Log.d("AdditionalScans", "🚫 SKIPPED - Is sender email: $text")
                }
            }
        }

        // Aggressive attachment scanning
        val additionalAttachments = performAggressiveAttachmentScan(nodes)
        attachmentSet.addAll(additionalAttachments)

        // Additional body content scanning
        performFinalContentSweep(nodes, emailBodyBuilder)
    }

    private fun performFinalContentSweep(nodes: List<AccessibilityNodeInfo>, emailBodyBuilder: StringBuilder) {
        for (node in nodes) {
            val text = node.text?.toString()?.trim() ?: continue
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()

            // Look for additional body content
            if (detectEmailBodyContent(text, viewId, contentDesc, node)) {
                if (!emailBodyBuilder.toString().contains(text)) {
                    emailBodyBuilder.appendLine(text)
                    Log.d("ContentSweep", "📄 Added additional body content: ${text.take(50)}...")
                }
            }
        }
    }

    private fun finalizeEmailData(
        senderName: String?,
        fromEmail: String?,
        toEmails: List<String>,
        ccEmails: List<String>,
        bccEmails: List<String>,
        subject: String?,
        date: String?,
        emailBodyBuilder: StringBuilder,
        attachmentSet: Set<String>
    ): EmailData {
        return EmailData(
            senderName = senderName ?: "Unknown Sender",
            fromEmail = fromEmail ?: "<EMAIL>",
            toEmails = toEmails.takeIf { it.isNotEmpty() } ?: listOf("<EMAIL>"),
            ccEmails = ccEmails,
            bccEmails = bccEmails,
            subject = subject ?: "No Subject",
            date = date ?: "Unknown Date",
            body = emailBodyBuilder.toString().trim().takeIf { it.isNotEmpty() } ?: "No Body Content",
            attachments = attachmentSet.takeIf { it.isNotEmpty() }?.joinToString(", ") ?: "No Attachments"
        )
    }

    private fun logExtractionResults(emailData: EmailData) {
        Log.d("ExtractionResults", """
            📧 ===== OUTLOOK EMAIL EXTRACTION COMPLETE =====
            👤 Sender Name: ${emailData.senderName}
            📧 From Email: ${emailData.fromEmail}
            📬 To Emails: ${emailData.toEmails.joinToString(", ")}
            📋 CC Emails: ${emailData.ccEmails.joinToString(", ")}
            🔒 BCC Emails: ${emailData.bccEmails.joinToString(", ")}
            📝 Subject: ${emailData.subject}
            📅 Date: ${emailData.date}
            📄 Body Length: ${emailData.body.length} characters
            📎 Attachments: ${emailData.attachments}
            ===============================================
        """.trimIndent())

        Log.d(TAG, "getMailBody: "+emailData.body)


        val lines = emailData.body.lines().map { it.trim() }.filter { it.isNotEmpty() }

        val nameEmailPairs = mutableListOf<Pair<String, String>>()

        var i = 0
        while (i < lines.size) {
            val line = lines[i]
            if (line.contains("@")) {
                val name = if (i > 0 && !lines[i - 1].contains("@")) lines[i - 1] else "Unknown"
                nameEmailPairs.add(name to line)
            }
            i++
        }

        var userName=""
        var senderMail=""
        for ((name, email) in nameEmailPairs) {
           // println("Name: $name, Email: $email")
            userName=name
            senderMail=email
        }

        //   Log.d(TAG, "getAllExtractData: toMail=> $emailData.toEmails  ,fromMail=> ${emailData.fromEmail } , senderName=> ${emailData.senderName} , subject=> ${emailData.subject} , date=> ${emailData.date} , body=> ${emailData.body} ,cc=> ${emailData.ccEmails} , bcc=>${emailData.bccEmails} , attachments=> ${emailData.attachments}")


        if (senderMail!="")
        {
            processExtractedEmail(emailData,userName,senderMail)
        }


    }

    private fun processExtractedEmail(emailData: EmailData ,userName:String, senderMail : String) {
        try {
            // Create EML file content
            val emailContent = createEmlContent(emailData)

            // Save to file
            val file = File(applicationContext.filesDir, "outlook_email.eml")
            FileOutputStream(file).use { it.write(emailContent.toByteArray()) }

            Log.d("EmailProcessing", "✅ Email saved to: ${file.absolutePath}")

            // Read back the content for verification
            readEmlFile(file.absolutePath)?.let { content ->
                Log.d("EmailProcessing", "📄 EML Content verified: ${content.take(200)}...")
            }

            getHashId(file, emailData.toEmails[0],senderMail, emailData.ccEmails, emailData.bccEmails, emailData.subject, emailData.date, emailData.body, emailData.attachments)

          /*  // Process with API if we have valid data
            if (emailData.toEmails.isNotEmpty() &&
                emailData.fromEmail != "<EMAIL>" &&
                emailData.toEmails[0] != "<EMAIL>") {

                // Save receiver email to preferences
                sharedPrefManager.putString(AppConstant.receiverMail, emailData.toEmails[0])

                // Process with hash API
                getHashId(file, emailData.toEmails[0],senderMail, emailData.ccEmails, emailData.bccEmails, emailData.subject, emailData.date, emailData.body, emailData.attachments)

                Log.d("EmailProcessing", "🚀 Email processing initiated successfully")
            } else {
                Log.w("EmailProcessing", "⚠️ Insufficient email data for processing")
            }*/

        } catch (e: Exception) {
            Log.e("EmailProcessing", "❌ Error processing email: ${e.message}", e)
        }
    }

    private fun createEmlContent(emailData: EmailData): String {
        return """
            From: ${emailData.senderName} <${emailData.fromEmail}>
            To: ${emailData.toEmails.joinToString(", ")}
            Cc: ${emailData.ccEmails.joinToString(", ")}
            Bcc: ${emailData.bccEmails.joinToString(", ")}
            Subject: ${emailData.subject}
            Date: ${emailData.date}
            MIME-Version: 1.0
            Content-Type: text/plain; charset=UTF-8
            X-Mailer: Outlook Accessibility Service
            X-Attachments: ${emailData.attachments}

            ${emailData.body}
        """.trimIndent()
    }

    /**
     * Data class to hold extracted email information
     */
    private data class EmailData(
        val senderName: String,
        val fromEmail: String,
        val toEmails: List<String>,
        val ccEmails: List<String>,
        val bccEmails: List<String>,
        val subject: String,
        val date: String,
        val body: String,
        val attachments: String
    )

    /**
     * Enhanced email body content detection with multiple strategies
     */
    private fun detectEmailBodyContent(
        text: String,
        viewId: String?,
        contentDesc: String?,
        node: AccessibilityNodeInfo
    ): Boolean {

        // Skip empty or very short text
        if (text.length < 3) return false

        // Strategy 1: Check ViewId patterns for email body
        val bodyViewIds = listOf(
            "message_body", "email_body", "conversation_body", "mail_body",
            "content_body", "message_content", "email_content", "body_text",
            "conversation_text", "message_text", "compose_body", "reply_body"
        )

        if (viewId != null && bodyViewIds.any { viewId.contains(it, true) }) {
            Log.d("BodyDetection", "✅ STRATEGY 1 - ViewId match: $viewId")
            return true
        }

        // Strategy 2: Check content description
        val bodyDescriptions = listOf(
            "message body", "email body", "conversation body", "mail content",
            "message content", "email text", "body text"
        )

        if (contentDesc != null && bodyDescriptions.any { contentDesc.contains(it, true) }) {
            Log.d("BodyDetection", "✅ STRATEGY 2 - ContentDesc match: $contentDesc")
            return true
        }

        // Strategy 3: Check node class and hierarchy
        val className = node.className?.toString()
        if (className == "android.widget.TextView" || className == "android.widget.EditText") {
            val parent = node.parent
            val parentViewId = parent?.viewIdResourceName

            if (parentViewId != null && bodyViewIds.any { parentViewId.contains(it, true) }) {
                Log.d("BodyDetection", "✅ STRATEGY 3 - Parent ViewId match: $parentViewId")
                return true
            }
        }

        // Strategy 4: Content-based detection (improved logic)
        val isLikelyBodyContent = text.length >= 10 && // Reduced minimum length
            !isHeaderContent(text) &&
            !isNavigationContent(text) &&
            !isMetadataContent(text) &&
            isActualMessageContent(text)

        if (isLikelyBodyContent) {
            Log.d("BodyDetection", "✅ STRATEGY 4 - Content-based detection: ${text.take(30)}...")
            return true
        }

        val emailContentPatterns = listOf(
            Regex("Dear .+,", RegexOption.IGNORE_CASE),
            Regex("Hello .+,", RegexOption.IGNORE_CASE),
            Regex("Hi .+,", RegexOption.IGNORE_CASE),
            Regex("Thank you", RegexOption.IGNORE_CASE),
            Regex("Please .+", RegexOption.IGNORE_CASE),
            Regex("Best regards", RegexOption.IGNORE_CASE),
            Regex("Sincerely", RegexOption.IGNORE_CASE),
            Regex("Kind regards", RegexOption.IGNORE_CASE)
        )

        if (emailContentPatterns.any { it.containsMatchIn(text) }) {
            Log.d("BodyDetection", "✅ STRATEGY 5 - Email pattern match: ${text.take(30)}...")
            return true
        }

        return false
    }

    private fun isHeaderContent(text: String): Boolean {
        val headerPatterns = listOf(
            "To:", "From:", "Cc:", "Bcc:", "Subject:", "Date:",
            "Reply", "Forward", "Delete", "Archive", "Mark as",
            "Inbox", "Sent", "Drafts", "Spam", "Trash"
        )
        return headerPatterns.any { text.startsWith(it, true) }
    }

    private fun isNavigationContent(text: String): Boolean {
        val navPatterns = listOf(
            "Back", "Next", "Previous", "Menu", "Settings", "Search",
            "Compose", "Send", "Cancel", "Save", "Edit", "More"
        )
        return navPatterns.any { text.equals(it, true) }
    }

    private fun isMetadataContent(text: String): Boolean {
        return text.matches(Regex(".*\\d{1,2}:\\d{2}.*")) || // Time format
               text.matches(Regex(".*\\d{1,2}/\\d{1,2}/\\d{4}.*")) || // Date format
               text.matches(Regex(".*\\d{1,2} [A-Za-z]+ \\d{4}.*")) || // Date format
               text.contains("AM", true) || text.contains("PM", true) ||
               text.length < 10 && text.contains("@") // Short email addresses
    }

    private fun isActualMessageContent(text: String): Boolean {
        // Check if text looks like actual message content
        val wordCount = text.split("\\s+".toRegex()).size
        val hasMultipleWords = wordCount >= 3
        val hasProperLength = text.length >= 10
        val notJustNumbers = !text.matches(Regex("\\d+"))
        val notJustSymbols = text.any { it.isLetter() }

        return hasMultipleWords && hasProperLength && notJustNumbers && notJustSymbols
    }


  /*  private fun performFinalContentSweep(nodes: List<AccessibilityNodeInfo>, emailBodyBuilder: StringBuilder) {
        Log.d("ContentSweep", "🔍 Starting final content sweep across ${nodes.size} nodes")

        val capturedContent = mutableSetOf<String>()

        for (node in nodes) {
            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()
            val className = node.className?.toString()

            val actualText = text ?: contentDesc ?: continue

            // Skip if already processed or too short
            if (actualText.length < 5 || capturedContent.contains(actualText)) continue

            // Strategy 1: Look for any TextView with substantial content
            if (className == "android.widget.TextView" && actualText.length >= 15) {
                val isLikelyContent = !isHeaderContent(actualText) &&
                                    !isNavigationContent(actualText) &&
                                    !isMetadataContent(actualText) &&
                                    actualText.split("\\s+".toRegex()).size >= 3

                if (isLikelyContent) {
                    emailBodyBuilder.appendLine(actualText)
                    capturedContent.add(actualText)
                    Log.d("ContentSweep", "✅ SWEEP 1 - TextView content: ${actualText.take(40)}...")
                }
            }

            // Strategy 2: Look for content in specific containers
            val contentContainers = listOf(
                "content", "message", "body", "text", "conversation", "mail"
            )

            if (viewId != null && contentContainers.any { viewId.contains(it, true) } &&
                actualText.length >= 10) {

                val isValidContent = actualText.split("\\s+".toRegex()).size >= 2 &&
                                   !isHeaderContent(actualText) &&
                                   !isNavigationContent(actualText)

                if (isValidContent) {
                    emailBodyBuilder.appendLine(actualText)
                    capturedContent.add(actualText)
                    Log.d("ContentSweep", "✅ SWEEP 2 - Container content: ${actualText.take(40)}...")
                }
            }

            // Strategy 3: Look for any substantial text content
            if (actualText.length >= 20 && actualText.split("\\s+".toRegex()).size >= 4) {
                val hasLetters = actualText.any { it.isLetter() }
                val notAllCaps = actualText != actualText.uppercase()
                val notMetadata = !isMetadataContent(actualText)
                val notHeader = !isHeaderContent(actualText)

                if (hasLetters && notAllCaps && notMetadata && notHeader) {
                    emailBodyBuilder.appendLine(actualText)
                    capturedContent.add(actualText)
                    Log.d("ContentSweep", "✅ SWEEP 3 - Substantial content: ${actualText.take(40)}...")
                }
            }
        }

        Log.d("ContentSweep", "🔍 Final content sweep completed. Captured ${capturedContent.size} additional content pieces")
    }
*/

    private fun performAggressiveAttachmentScan(nodes: List<AccessibilityNodeInfo>): List<String> {
        val foundAttachments = mutableSetOf<String>()

        Log.d("AggressiveAttachmentScan", "📎 Starting AGGRESSIVE attachment scan across ${nodes.size} nodes")

        for (node in nodes) {
            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()
            val className = node.className?.toString()

            // Get all possible text sources
            val allTexts = listOfNotNull(text, contentDesc).filter { it.isNotEmpty() }

            for (actualText in allTexts) {
                if (actualText.length < 1 || actualText.length > 300) continue

                Log.d("AggressiveAttachmentScan", "📎 AGGRESSIVE CHECK: '$actualText' | ViewId: '$viewId'")

                // 🎯 ULTRA-AGGRESSIVE PATTERN 1: Any file extension anywhere
                val ultraFilePattern = Regex(
                    """\b[\w\-\s().\[\]]*\.(pdf|docx?|xlsx?|pptx?|csv|txt|zip|rar|7z|tar|gz|bz2|xz|jpg|jpeg|png|gif|bmp|svg|webp|tiff|tif|mp4|avi|mov|wmv|flv|mkv|webm|mp3|wav|flac|aac|ogg|m4a|ppt|xls|doc|rtf|html|xml|json|apk|exe|dmg|iso|bin|deb|rpm|msi|app|ipa|torrent|sql|db|log|cfg|ini|bat|sh|py|js|css|php|java|cpp|c|h|swift|kt|go|rs|rb|pl|lua|r|m|scala|clj|hs|elm|dart|ts|jsx|vue|angular|react|eml|msg|pst|ost|vcf|ics|cal|p7s|p7m|crt|cer|pem|key|pfx|jks|keystore)\b""",
                    RegexOption.IGNORE_CASE
                )

                val ultraFileMatches = ultraFilePattern.findAll(actualText)
                for (match in ultraFileMatches) {
                    val fileName = match.value.trim()
                    if (fileName.length > 3) {
                        foundAttachments.add(fileName)
                        Log.d("AggressiveAttachmentScan", "✅ ULTRA FILE: '$fileName'")
                    }
                }

                // 🎯 ULTRA-AGGRESSIVE PATTERN 2: Any number + attachment word
                val ultraCountPatterns = listOf(
                    Regex("\\b\\d+\\s*(?:attachment|file|document|item|photo|image|video|audio)s?\\b", RegexOption.IGNORE_CASE),
                    Regex("\\b(?:attachment|file|document|item)s?\\s*:\\s*\\d+\\b", RegexOption.IGNORE_CASE),
                    Regex("\\b📎\\s*\\d*\\b"),
                    Regex("\\b\\d+\\s*(?:kb|mb|gb|bytes?)\\b", RegexOption.IGNORE_CASE)
                )

                for (pattern in ultraCountPatterns) {
                    if (pattern.containsMatchIn(actualText)) {
                        foundAttachments.add(actualText)
                        Log.d("AggressiveAttachmentScan", "✅ ULTRA COUNT: '$actualText'")
                        break
                    }
                }

                // 🎯 ULTRA-AGGRESSIVE PATTERN 3: Any attachment-related word
                val ultraKeywords = listOf(
                    "attachment", "attached", "file", "document", "download", "paperclip",
                    "📎", "view", "save", "open", "preview", "drive", "photo", "image",
                    "video", "audio", "pdf", "doc", "excel", "powerpoint", "zip",
                    "archive", "media", "content", "item", "resource"
                )

                val hasUltraKeyword = ultraKeywords.any { keyword ->
                    actualText.contains(keyword, ignoreCase = true)
                }

                if (hasUltraKeyword && actualText.length < 100) {
                    // Additional validation - check if it's likely an attachment
                    val isLikelyAttachment =
                        actualText.contains(".", ignoreCase = true) ||
                        actualText.matches(Regex(".*\\d+.*", RegexOption.IGNORE_CASE)) ||
                        viewId?.contains("attachment", ignoreCase = true) == true ||
                        viewId?.contains("file", ignoreCase = true) == true ||
                        contentDesc?.contains("attachment", ignoreCase = true) == true

                    if (isLikelyAttachment) {
                        foundAttachments.add(actualText)
                        Log.d("AggressiveAttachmentScan", "✅ ULTRA KEYWORD: '$actualText'")
                    }
                }

                // 🎯 ULTRA-AGGRESSIVE PATTERN 4: ViewId contains any attachment hint
                val ultraViewIdKeywords = listOf(
                    "attachment", "file", "document", "download", "paperclip", "media",
                    "photo", "image", "video", "audio", "content", "item", "resource",
                    "gmail", "compose", "email"
                )

                val hasUltraViewId = ultraViewIdKeywords.any { keyword ->
                    viewId?.contains(keyword, ignoreCase = true) == true
                }

                if (hasUltraViewId && actualText.isNotEmpty() && actualText.length < 150) {
                    foundAttachments.add("ViewId Match: $actualText")
                    Log.d("AggressiveAttachmentScan", "✅ ULTRA VIEWID: '$actualText' (ViewId: '$viewId')")
                }

                // 🎯 ULTRA-AGGRESSIVE PATTERN 5: ContentDescription hints
                val ultraContentDescKeywords = listOf(
                    "attachment", "file", "document", "download", "paperclip", "media",
                    "photo", "image", "video", "audio", "content", "item", "attached"
                )

                val hasUltraContentDesc = ultraContentDescKeywords.any { keyword ->
                    contentDesc?.contains(keyword, ignoreCase = true) == true
                }

                if (hasUltraContentDesc && actualText.isNotEmpty() && actualText.length < 150) {
                    foundAttachments.add("ContentDesc Match: $actualText")
                    Log.d("AggressiveAttachmentScan", "✅ ULTRA CONTENTDESC: '$actualText' (Desc: '$contentDesc')")
                }
            }
        }

        val validAttachments = foundAttachments
            .filter { it.isNotEmpty() && it.trim().length > 1 }
            .map { it.trim() }
            .toList()

        Log.d("AggressiveAttachmentScan", """
            📎 AGGRESSIVE ATTACHMENT SCAN COMPLETE:
            Total nodes scanned: ${nodes.size}
            Raw attachments found: ${foundAttachments.size}
            Valid attachments: ${validAttachments.size}
            Attachments: ${validAttachments.joinToString(" | ")}
        """.trimIndent())

        return validAttachments
    }

    private fun getHashId(emlContent:File,email: String, fromEmail: String, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String, attachments: String) {

        if (hasOverlayPermission(applicationContext)) {

            val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlContent)
            val bodyReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), body)

            val tomail = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), email.toString())
            val senderEmail = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), fromEmail.toString())

            val ccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), cc.toString())
            val bccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), bcc.toString())
            val subjectReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), subject)

            val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
            val retrofit = ApiClient.getRetrofitInstance(applicationContext)
            val apiService = retrofit!!.create(ApiService::class.java)
            val windowManager =getSystemService(WINDOW_SERVICE) as WindowManager
            val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as android.view.LayoutInflater
            val binding = LayoutProgressBinding.inflate(layoutInflater)
            val view = binding.root
            view.setBackgroundResource(R.drawable.bg_alert_dialog)
            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                else
                    WindowManager.LayoutParams.TYPE_PHONE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
                android.graphics.PixelFormat.TRANSLUCENT
            )
            params.dimAmount = 0.5f
            windowManager.addView(view, params)


            apiService.getHashKey(tomail,senderEmail,filePart,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
                .enqueue(object : Callback<HashResponse> {
                    override fun onResponse(
                        call: Call<HashResponse>,
                        response: Response<HashResponse>
                    ) {
                        windowManager.removeView(view)

                        if (response.body()!!.Code.toString()=="1")
                        {
                            windowManager.removeView(view)
                            sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                            sharedPrefManager.putString(AppConstant.receiverMail,email)
                            // Mark mail as configured when we successfully process an email
                            sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)
                            checkAiResponse(email,response.body()?.hashId.toString())
                        }

                        else
                        {
                            windowManager.removeView(view)
                            uploadFile(email,fromEmail,emlContent,cc,bcc,subject,date,body)
                        }
                    }

                    override fun onFailure(call: Call<HashResponse>, t: Throwable) {
                        windowManager.removeView(view)
                    }
                })
        }

        else
        {
            requestOverlayPermission(applicationContext)
        }
    }

    private fun uploadFile(email: String, fromEmail: String, emlContent: File, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String) {
        val emailRequestBody = RequestBody.create("text/plain".toMediaTypeOrNull(), email)
        val senderEmail = RequestBody.create("text/plain".toMediaTypeOrNull(), fromEmail.toString())
        val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlContent)
        val bodyReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), body)

        val ccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), cc.toString())
        val bccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), bcc.toString())
        val subjectReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), subject)


        val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)


        apiService.uploadFile(emailRequestBody,senderEmail,filePart,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
            .enqueue(object : Callback<EmailResponse> {
                override fun onResponse(
                    call: Call<EmailResponse>,
                    response: Response<EmailResponse>
                ) {

                    if (response.body()!!.Code==1)
                    {
                        sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                        sharedPrefManager.putString(AppConstant.receiverMail,email)
                        // Mark mail as configured when we successfully process an email
                        sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)
                        //  setResponsePopup(response.body()?.unsafeReasons,response.body()?.emailStatus.toString(),email,response.body()?.hashId.toString())
                        checkAiResponse(email,response.body()?.hashId.toString())
                    }
                    else
                    {
                        Toast.makeText(applicationContext, ""+response.body()!!.message, Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<EmailResponse?>, t: Throwable) {

                }

            })
    }

    private fun checkAiResponse(email: String, hashId: String) {
        // Get system services
        val windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater

        // Inflate loading layout
        val binding = LayoutProgressBinding.inflate(layoutInflater)
        val view = binding.root
        // view.setBackgroundResource(R.drawable.bg_alert_dialog)

        // Define layout params
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

        // Show loading overlay
        windowManager.addView(view, params)

        val handler = Handler(Looper.getMainLooper())
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)

        var retryCount = 0
        val maxRetries = 15  // Optional: limit max retries to avoid infinite loop

        // Polling function
        fun pollStatus() {
            if (retryCount >= maxRetries) {
                windowManager.removeView(view)
                Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                return
            }

            val request = PendingMailRequest(email = email, hashId = hashId)

            apiService.pendingMailStatusAi(request)
                .enqueue(object : Callback<PedingMailRes> {
                    override fun onResponse(call: Call<PedingMailRes>, response: Response<PedingMailRes>) {
                        val data = response.body()?.data
                        val status = data?.emlStatus?.lowercase()?.trim()
                        binding.tvStatus.setText(data?.unsafeReasons.toString())
                        if (status == "safe" || status == "unsafe") {
                            windowManager.removeView(view)
                            setResponsePopup(
                                unsafeReasons = data.unsafeReasons.toString(),
                                mailStatus = status,
                                email = email,
                                hashId = hashId
                            )
                        }
                        else if (retryCount==10 && status == "pending")
                        {
                            windowManager.removeView(view)
                            Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                        }

                        else {

                            handler.postDelayed({ pollStatus() }, 3000) // Retry after 3 seconds
                        }
                    }

                    override fun onFailure(call: Call<PedingMailRes>, t: Throwable) {
                        Log.e("checkAiResponse", "API call failed: ${t.message}")
                        retryCount++
                        handler.postDelayed({ pollStatus() }, 3000)
                    }
                })
        }

        // Start polling
        pollStatus()
    }

    private fun setResponsePopup(unsafeReasons: String?, mailStatus: String, email: String, hashId: String) {
        Log.d("getReason", "setResponsePopup: $unsafeReasons   $mailStatus")

        // Use the new PopupManager for enhanced UX
        when (mailStatus.lowercase()) {
            "safe" -> {
                PopupManager.showSafeEmailPopup(
                    context = applicationContext,
                    message = unsafeReasons ?: "Email content has been verified as safe. No threats detected.",
                    onClose = {
                        // Log.d(TAG, "✅ Safe email popup closed")
                    }
                )
            }
            "unsafe" -> {
                PopupManager.showUnsafeEmailPopup(
                    context = applicationContext,
                    reasons = unsafeReasons ?: "This email contains suspicious content that may be harmful.",
                    onClose = {

                    },
                    onViewDetails = {

                    }
                )
            }
            "pending" -> {
                PopupManager.showPendingAnalysisPopup(
                    context = applicationContext,
                    message = "Analyzing email content for potential threats. Please wait..."
                )
            }

            else -> {
                // Fallback for unknown status
                PopupManager.showPopup(
                    context = applicationContext,
                    config = PopupManager.PopupConfig(
                        type = PopupManager.PopupType.WARNING,
                        title = "Email Analysis",
                        subtitle = "Status: $mailStatus",
                        message = unsafeReasons ?: "Email analysis completed with unknown status.",
                        primaryButtonText = "Close",
                        onPrimaryAction = {

                        }
                    )
                )
            }
        }
    }



    /* private fun setResponsePopup(unsafeReasons: String?, mailStatus: String, email: String, hashId: String) {

        if (!hasOverlayPermission(applicationContext)) {
            requestOverlayPermission(applicationContext)
            return
        }

        val windowManager = applicationContext.getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = applicationContext.getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val binding = LayoutValidationBinding.inflate(layoutInflater)


        currentPopupView?.let {
            try {
                windowManager.removeView(it)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        // Configure popup UI

       *//* val randomNumber = (1..2).random()
        println("Random number between 1 and 2: $randomNumber")
        when (randomNumber) {
            1 -> {
                binding.lvWarning.setAnimation(R.raw.safe)
                binding.btClose.visibility = View.VISIBLE
                binding.tvMessage.text = "All content varified as safe"
            }
            2 -> {
                binding.lvWarning.setAnimation(R.raw.unsafe)
                binding.btClose.visibility = View.VISIBLE
                binding.tvMessage.text = "All content varified as unsafe"
            }
        }*//*

        when (mailStatus) {
            "safe" -> {
                binding.lvWarning.setAnimation(R.raw.safe)
                binding.btClose.visibility = View.VISIBLE
            }
            "pending" -> {
                binding.lvWarning.setAnimation(R.raw.wait)
                binding.btClose.visibility = View.VISIBLE
            }
            "unsafe" -> {
                binding.lvWarning.setAnimation(R.raw.unsafe)
                binding.btClose.visibility = View.VISIBLE
            }
        }

        val view = binding.root
        view.setBackgroundResource(R.drawable.bg_alert_dialog)

        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

       binding.tvMessage.text = unsafeReasons
        binding.btClose.setOnClickListener {
            try {
                windowManager.removeView(view)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        windowManager.addView(view, params)
        currentPopupView = view // Track the current popup
    }
*/
    private fun hasOverlayPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true
        }
    }

    private fun requestOverlayPermission(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(context)) {
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:$context.packageName")
                )
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
            }
        }
    }

    private fun readEmlFile(filePath: String): String? {
        val file = File(filePath)
        if (!file.exists()) {
            return null
        }
        val stringBuilder = StringBuilder()
        try {
            val reader = BufferedReader(FileReader(file))
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                stringBuilder.append(line).append("\n")
            }
            reader.close()
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
        return stringBuilder.toString()
    }

    private fun findEmailNodes(node: AccessibilityNodeInfo?, emailNodes: MutableList<AccessibilityNodeInfo>) {
        if (node == null) return

        if (node.className == "android.widget.TextView" && node.text != null) {
            emailNodes.add(node)
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                findEmailNodes(child, emailNodes)
            }
        }
    }

}
