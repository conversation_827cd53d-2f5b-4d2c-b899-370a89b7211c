package com.tech.ekvayu.Sms.AiResponse

import com.google.gson.annotations.SerializedName

data class CheckSmsAiResponse (
    @SerializedName("message" ) var message : String? = null,
    @SerializedName("status"  ) var status  : String? = null,
    @SerializedName("code"    ) var code    : Int?    = null,
    @SerializedName("data"    ) var data    : Data?   = Data()
)

data class Data (
    @SerializedName("msg_status"      ) var msgStatus      : String?           = null,
    @SerializedName("hashId"          ) var hashId         : String?           = null,
    @SerializedName("unsafe_reasons"  ) var unsafeReasons  : ArrayList<String> = arrayListOf(),
    @SerializedName("message_content" ) var messageContent : String?           = null,
    @SerializedName("type"            ) var type           : String?           = null,
    @SerializedName("timestamp"       ) var timestamp      : String?           = null

)