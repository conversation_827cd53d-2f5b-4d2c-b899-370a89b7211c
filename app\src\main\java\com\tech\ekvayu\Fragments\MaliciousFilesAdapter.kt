package com.tech.ekvayu.Fragments

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.tech.ekvayu.R

class MaliciousFilesAdapter(
    private val maliciousFilesList: List<MaliciousFile>,
    private val onItemClick: (MaliciousFile) -> Unit
) : RecyclerView.Adapter<MaliciousFilesAdapter.MaliciousFileViewHolder>() {

    class MaliciousFileViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvFileName: TextView = itemView.findViewById(R.id.tvFileName)
        val tvFilePath: TextView = itemView.findViewById(R.id.tvFilePath)
        val tvFileSize: TextView = itemView.findViewById(R.id.tvFileSize)
        val tvFileDate: TextView = itemView.findViewById(R.id.tvFileDate)
        val tvThreatScore: TextView = itemView.findViewById(R.id.tvThreatScore)
        val tvThreatLevel: TextView = itemView.findViewById(R.id.tvThreatLevel)
        val tvFileExtension: TextView = itemView.findViewById(R.id.tvFileExtension)
        val tvThreatSummary: TextView = itemView.findViewById(R.id.tvThreatSummary)
        val ivThreatIcon: ImageView = itemView.findViewById(R.id.ivThreatIcon)
        val ivFileType: ImageView = itemView.findViewById(R.id.ivFileType)
        val cbSelectFile: CheckBox = itemView.findViewById(R.id.cbSelectFile)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MaliciousFileViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_malicious_file, parent, false)
        return MaliciousFileViewHolder(view)
    }

    override fun onBindViewHolder(holder: MaliciousFileViewHolder, position: Int) {
        val maliciousFile = maliciousFilesList[position]
        
        holder.tvFileName.text = maliciousFile.getFileName()
        holder.tvFilePath.text = maliciousFile.getFilePath()
        holder.tvFileSize.text = maliciousFile.getFormattedSize()
        holder.tvFileDate.text = maliciousFile.getFormattedDate()
        holder.tvThreatScore.text = "${maliciousFile.threatScore}/100"
        holder.tvFileExtension.text = maliciousFile.getFileExtension()
        holder.tvThreatSummary.text = maliciousFile.getThreatSummary()
        
        // Set threat level and colors
        val threatLevel = maliciousFile.getThreatLevel()
        holder.tvThreatLevel.text = threatLevel.displayName
        holder.tvThreatLevel.setTextColor(
            ContextCompat.getColor(holder.itemView.context, threatLevel.colorRes)
        )
        
        // Set threat score background color
        val scoreBackgroundColor = when {
            maliciousFile.threatScore >= 75 -> android.R.color.holo_red_light
            maliciousFile.threatScore >= 50 -> android.R.color.holo_orange_light
            else -> android.R.color.holo_blue_light
        }
        holder.tvThreatScore.setBackgroundColor(
            ContextCompat.getColor(holder.itemView.context, scoreBackgroundColor)
        )
        
        // Set threat icon
        when (threatLevel) {
            FileThreatLevel.HIGH -> {
                holder.ivThreatIcon.setImageResource(R.drawable.ic_warning)
                holder.ivThreatIcon.setColorFilter(
                    ContextCompat.getColor(holder.itemView.context, android.R.color.holo_red_dark)
                )
            }
            FileThreatLevel.MEDIUM -> {
                holder.ivThreatIcon.setImageResource(R.drawable.ic_warning)
                holder.ivThreatIcon.setColorFilter(
                    ContextCompat.getColor(holder.itemView.context, android.R.color.holo_orange_dark)
                )
            }
            FileThreatLevel.LOW -> {
                holder.ivThreatIcon.setImageResource(R.drawable.ic_info)
                holder.ivThreatIcon.setColorFilter(
                    ContextCompat.getColor(holder.itemView.context, android.R.color.holo_orange_light)
                )
            }
            FileThreatLevel.SAFE -> {
                holder.ivThreatIcon.setImageResource(R.drawable.ic_check)
                holder.ivThreatIcon.setColorFilter(
                    ContextCompat.getColor(holder.itemView.context, android.R.color.holo_green_light)
                )
            }
        }
        
        // Set file type icon
        when {
            maliciousFile.isExecutable() -> {
                holder.ivFileType.setImageResource(R.drawable.ic_warning)
                holder.ivFileType.setColorFilter(
                    ContextCompat.getColor(holder.itemView.context, android.R.color.holo_red_dark)
                )
            }
            maliciousFile.isHidden() -> {
                holder.ivFileType.setImageResource(R.drawable.ic_visibility_off)
                holder.ivFileType.setColorFilter(
                    ContextCompat.getColor(holder.itemView.context, android.R.color.darker_gray)
                )
            }
            else -> {
                holder.ivFileType.setImageResource(R.drawable.ic_file)
                holder.ivFileType.setColorFilter(
                    ContextCompat.getColor(holder.itemView.context, android.R.color.darker_gray)
                )
            }
        }
        
        // Set checkbox state
        holder.cbSelectFile.isChecked = maliciousFile.isSelected
        holder.cbSelectFile.setOnCheckedChangeListener { _, isChecked ->
            maliciousFile.isSelected = isChecked
        }
        
        // Set click listener
        holder.itemView.setOnClickListener {
            onItemClick(maliciousFile)
        }
        
        // Highlight high-threat files
        if (maliciousFile.threatScore >= 75) {
            holder.itemView.setBackgroundColor(
                ContextCompat.getColor(holder.itemView.context, R.color.error_light)
            )
        } else if (maliciousFile.threatScore >= 50) {
            holder.itemView.setBackgroundColor(
                ContextCompat.getColor(holder.itemView.context, R.color.warning_light)
            )
        } else {
            holder.itemView.setBackgroundColor(
                ContextCompat.getColor(holder.itemView.context, R.color.card_background)
            )
        }
    }

    override fun getItemCount(): Int = maliciousFilesList.size
}
