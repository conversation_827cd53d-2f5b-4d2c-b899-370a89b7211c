package com.tech.ekvayu.Fragments

import java.io.File
import java.text.SimpleDateFormat
import java.util.*

data class MaliciousFile(
    val file: File,
    val threatScore: Int,
    val threats: List<String>,
    val fileSize: Long,
    val lastModified: Long,
    var isSelected: Boolean = false
) {
    fun getFormattedSize(): String {
        return when {
            fileSize < 1024 -> "${fileSize} B"
            fileSize < 1024 * 1024 -> "${fileSize / 1024} KB"
            fileSize < 1024 * 1024 * 1024 -> "${fileSize / (1024 * 1024)} MB"
            else -> "${fileSize / (1024 * 1024 * 1024)} GB"
        }
    }
    
    fun getFormattedDate(): String {
        return SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
            .format(Date(lastModified))
    }
    
    fun getThreatLevel(): FileThreatLevel {
        return when {
            threatScore >= 75 -> FileThreatLevel.HIGH
            threatScore >= 50 -> FileThreatLevel.MEDIUM
            threatScore >= 25 -> FileThreatLevel.LOW
            else -> FileThreatLevel.SAFE
        }
    }
    
    fun getFileExtension(): String {
        return file.extension.uppercase().ifEmpty { "Unknown" }
    }
    
    fun getFileName(): String {
        return file.name
    }
    
    fun getFilePath(): String {
        return file.absolutePath
    }
    
    fun isExecutable(): Boolean {
        val executableExtensions = setOf("apk", "exe", "bat", "cmd", "scr", "jar", "dex")
        return executableExtensions.contains(file.extension.lowercase())
    }
    
    fun isHidden(): Boolean {
        return file.name.startsWith(".")
    }
    
    fun isInSystemDirectory(): Boolean {
        val systemPaths = setOf("/system", "/android", "/data/system")
        return systemPaths.any { file.absolutePath.lowercase().contains(it) }
    }
    
    fun getPrimaryThreat(): String {
        return threats.firstOrNull() ?: "Unknown threat"
    }
    
    fun getThreatSummary(): String {
        return when {
            threats.size == 1 -> threats.first()
            threats.size > 1 -> "${threats.first()} (+${threats.size - 1} more)"
            else -> "No specific threats identified"
        }
    }
}

enum class FileThreatLevel(val displayName: String, val colorRes: Int) {
    SAFE("Safe", android.R.color.holo_green_light),
    LOW("Low Risk", android.R.color.holo_orange_light),
    MEDIUM("Medium Risk", android.R.color.holo_orange_dark),
    HIGH("High Risk", android.R.color.holo_red_dark)
}
