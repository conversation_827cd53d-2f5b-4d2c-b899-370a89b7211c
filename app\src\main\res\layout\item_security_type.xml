<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/carSecurityType"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/_3sdp"
    android:layout_marginVertical="@dimen/_3sdp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_profile_menu"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivTypeIcon"
            android:layout_width="@dimen/_24sdp"
            android:layout_height="@dimen/_24sdp"
            android:src="@drawable/ic_sms"
            app:tint="@color/app_color"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvTypeTitle"
            app:layout_constraintBottom_toBottomOf="@+id/tvTypeDes" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTypeTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="SMS Detection"
            android:fontFamily="@font/roboto_semi_bold"
            android:textSize="@dimen/_12sdp"
            android:textColor="@color/text_primary"
            app:layout_constraintStart_toEndOf="@id/ivTypeIcon"
            app:layout_constraintTop_toTopOf="@id/ivTypeIcon"
            app:layout_constraintEnd_toStartOf="@id/iconArrow"
            android:layout_marginStart="16dp" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTypeDes"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Detect suspicious SMS messages"
            android:fontFamily="@font/roboto_semi_regular"
            android:textSize="@dimen/_10sdp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="4dp"
            app:layout_constraintTop_toBottomOf="@id/tvTypeTitle"
            app:layout_constraintStart_toStartOf="@id/tvTypeTitle"
            app:layout_constraintEnd_toEndOf="@id/tvTypeTitle" />


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iconArrow"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/right_arrow"
            app:tint="@color/text_secondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvTypeTitle"
            app:layout_constraintBottom_toBottomOf="@+id/tvTypeDes"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
