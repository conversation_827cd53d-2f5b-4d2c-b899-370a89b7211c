<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".activities.EmailAuthActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="24dp">

        <!-- Title -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Email Provider Authentication"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textAlignment="center"
            android:layout_marginBottom="32dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Description -->
        <TextView
            android:id="@+id/tv_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Authenticate with your email providers to access your emails securely using OAuth 2.0"
            android:textSize="16sp"
            android:textAlignment="center"
            android:layout_marginBottom="32dp"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Authentication Status -->
        <TextView
            android:id="@+id/tv_auth_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Authenticated providers: 0/3"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textAlignment="center"
            android:background="@drawable/bg_button_white"
            android:padding="12dp"
            android:layout_marginBottom="24dp"
            app:layout_constraintTop_toBottomOf="@id/tv_description"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Gmail Authentication -->
        <Button
            android:id="@+id/btn_gmail_auth"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:text="Authenticate Gmail"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/bg_button_app_color"
            android:textColor="@android:color/white"
            android:layout_marginBottom="16dp"
            app:layout_constraintTop_toBottomOf="@id/tv_auth_status"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Outlook Authentication -->
        <Button
            android:id="@+id/btn_outlook_auth"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:text="Authenticate Outlook"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/bg_button_app_color"
            android:textColor="@android:color/white"
            android:layout_marginBottom="16dp"
            app:layout_constraintTop_toBottomOf="@id/btn_gmail_auth"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Yahoo Authentication -->
        <Button
            android:id="@+id/btn_yahoo_auth"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:text="Authenticate Yahoo"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/bg_button_app_color"
            android:textColor="@android:color/white"
            android:layout_marginBottom="32dp"
            app:layout_constraintTop_toBottomOf="@id/btn_outlook_auth"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Progress Section -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/btn_yahoo_auth"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Authenticating..."
            android:textSize="14sp"
            android:textAlignment="center"
            android:visibility="gone"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="24dp"
            app:layout_constraintTop_toBottomOf="@id/progress_bar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Action Buttons -->
        <Button
            android:id="@+id/btn_check_status"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:text="Check Status"
            android:textSize="14sp"
            android:background="@drawable/bg_button_white"
            android:layout_marginEnd="8dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintTop_toBottomOf="@id/tv_progress"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btn_clear_auth" />

        <Button
            android:id="@+id/btn_clear_auth"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:text="Clear All"
            android:textSize="14sp"
            android:background="@drawable/bg_button_white"
            android:layout_marginStart="8dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintTop_toBottomOf="@id/tv_progress"
            app:layout_constraintStart_toEndOf="@id/btn_check_status"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Features Section -->
        <TextView
            android:id="@+id/tv_features_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Features"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintTop_toBottomOf="@id/btn_check_status"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/tv_features"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="✅ OAuth 2.0 Secure Authentication\n✅ Full Email Access (Headers, Body, Attachments)\n✅ Raw Email Content (.eml format)\n✅ Attachment Download\n✅ Secure Token Storage\n✅ Automatic Token Refresh\n✅ Multi-Provider Support"
            android:textSize="14sp"
            android:lineSpacingExtra="4dp"
            app:layout_constraintTop_toBottomOf="@id/tv_features_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
