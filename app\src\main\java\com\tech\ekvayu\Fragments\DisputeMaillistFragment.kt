package com.tech.ekvayu.Fragments

import android.content.Context
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.GridLayoutManager
import com.tech.ekvayu.Adapter.DisputeListAdapter
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.CommonUtil
import com.tech.ekvayu.BaseClass.PermissionHelper
import com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment
import com.tech.ekvayu.BottomSheet.ServiceSpecificWarningBottomSheet
import com.tech.ekvayu.Dispute.DisputeMailistResponse
import com.tech.ekvayu.Dispute.Results
import com.tech.ekvayu.databinding.FragmentDisputeMaillistBinding
import com.tech.ekvayu.databinding.LayoutSpamDetailBinding
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class DisputeMaillistFragment : Fragment(), DisputeListAdapter.OnDisputeMailListClickListener {

    private lateinit var binding: FragmentDisputeMaillistBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding= FragmentDisputeMaillistBinding.inflate(inflater, container, false)

      /*  binding.btRaiseDispute.setOnClickListener {
            val bottomSheet = RaiseBottomSheetFragment(item)
            bottomSheet.show(requireActivity().supportFragmentManager, bottomSheet.tag)
        }*/
        getDisputelist()
        return binding.root
    }


    private fun getDisputelist() {
        val retrofit = ApiClient.getRetrofitInstance(requireContext())
        val apiService = retrofit!!.create(ApiService::class.java)
        CommonUtil.showProgressDialog(requireContext(), "Please wait...")
        apiService.getDisputelist()
            .enqueue(object : Callback<DisputeMailistResponse> {
                override fun onResponse(call: Call<DisputeMailistResponse?>, response: Response<DisputeMailistResponse?>) {
                    CommonUtil.hideProgressDialog()
                    if (response.isSuccessful) {
                        val adapter = DisputeListAdapter(response.body()!!.results,this@DisputeMaillistFragment)
                        binding.rvDisputelist.adapter = adapter
                        binding.rvDisputelist.layoutManager = GridLayoutManager(requireActivity(), 1)
                    }
                    else
                    {
                        Toast.makeText(requireContext(), response.message(), Toast.LENGTH_SHORT).show()
                    }
                }
                override fun onFailure(call: Call<DisputeMailistResponse?>, t: Throwable) {
                    CommonUtil.hideProgressDialog()
                }
            })
    }

    override fun onDisputeClicked(item: com.tech.ekvayu.Dispute.Results) {
        // Check accessibility permission before showing dispute details
       /* if (checkAccessibilityPermissionAndShowBottomSheet()) {
            showCustomEmailDialog(requireContext(), item)
        }*/

        showCustomEmailDialog(requireContext(), item)
    }

    /**
     * Check Gmail accessibility permission and show warning bottom sheet if not granted
     * @return true if permission is granted, false if bottom sheet was shown
     */
    private fun checkAccessibilityPermissionAndShowBottomSheet(): Boolean {
        val isServiceEnabled = PermissionHelper.checkAndUpdateSpecificAccessibilityPermission(
            requireContext(),
            PermissionHelper.ServiceType.GMAIL
        )

        if (!isServiceEnabled) {
            // Show Gmail-specific accessibility permission warning bottom sheet
            val bottomSheet = ServiceSpecificWarningBottomSheet.newInstance(PermissionHelper.ServiceType.GMAIL)
            bottomSheet.setCancelable(false)
            bottomSheet.show(requireActivity().supportFragmentManager, bottomSheet.tag)

            Toast.makeText(
                requireContext(),
                "Gmail accessibility permission is required to view dispute mail details",
                Toast.LENGTH_LONG
            ).show()

            return false
        }
        return true
    }

    private fun showCustomEmailDialog(context: Context, item: Results) {

        val binding = LayoutSpamDetailBinding.inflate(LayoutInflater.from(context))
        binding.tvSubject.text = item.subject
        binding.tvSender.text = "From: ${item.sendersEmail}"
        binding.tvReceiver.text = "To: ${item.recieversEmail}"
        binding.tvStatus.text = "Status: ${item.status}"
        binding.tvBody.text = item.overallAiStatus.toString()

        val dialog = AlertDialog.Builder(context).setView(binding.root).setCancelable(true).create()
        binding.btDispute.setOnClickListener {
            dialog.dismiss()
        }

        binding.btDispute.setOnClickListener {
            dialog.dismiss()
            val bottomSheet = RaiseBottomSheetFragment(item)
            bottomSheet.show(requireActivity().supportFragmentManager, bottomSheet.tag)
        }

        dialog.show()

    }

}