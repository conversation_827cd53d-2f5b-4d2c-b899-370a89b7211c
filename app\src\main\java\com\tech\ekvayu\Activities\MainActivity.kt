package com.tech.ekvayu.Activities

import android.accessibilityservice.AccessibilityService
import android.animation.ObjectAnimator
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.text.TextUtils
import androidx.appcompat.app.AppCompatActivity
import com.tech.ekvayu.BaseClass.PermissionHelper
import com.tech.ekvayu.databinding.ActivityMainBinding

class MainActivity : AppCompatActivity() {

    private lateinit var binding : ActivityMainBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding= ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

    }


    fun buttonAnimate()
    {
        val scaleX = ObjectAnimator.ofFloat(binding.btPermission, "scaleX", 1f, 1.2f, 1f)
        val scaleY = ObjectAnimator.ofFloat(binding.btPermission, "scaleY", 1f, 1.2f, 1f)
        scaleX.duration = 300
        scaleY.duration = 300
        scaleX.start()
        scaleY.start()
    }


    fun openAccessibilitySettings(context: Context) {
        PermissionHelper.openAccessibilitySettings(context)
    }


    fun isAccessibilityServiceEnabled(context: Context, service: Class<out AccessibilityService>): Boolean {
        val componentName = ComponentName(context, service)
        val enabledServices = Settings.Secure.getString(context.contentResolver, Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES)

        if (enabledServices.isNullOrEmpty()) {
            return false
        }

        val colonSplitter = TextUtils.SimpleStringSplitter(':')
        colonSplitter.setString(enabledServices)  // This will not throw NullPointerException now

        while (colonSplitter.hasNext()) {
            if (colonSplitter.next().equals(componentName.flattenToString(), ignoreCase = true)) {
                return true
            }
        }
        return false
    }



}