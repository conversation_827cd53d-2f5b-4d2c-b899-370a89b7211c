<?xml version="1.0" encoding="utf-8"?>
<!--
<accessibility-service
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:description="@string/sms_accessibility_service_description"
    android:packageNames="com.google.android.apps.messaging,com.samsung.android.messaging,com.android.mms,com.textra,com.chomp.android.sms,com.microsoft.android.sms"
    android:accessibilityEventTypes="typeWindowContentChanged|typeViewTextChanged|typeViewClicked|typeViewFocused|typeViewSelected"
    android:accessibilityFeedbackType="feedbackGeneric"
    android:notificationTimeout="100"
    android:canRetrieveWindowContent="true"
    android:accessibilityFlags="flagDefault|flagIncludeNotImportantViews|flagReportViewIds" />
-->


<accessibility-service
xmlns:android="http://schemas.android.com/apk/res/android"
    android:description="@string/sms_accessibility_service_description"
android:accessibilityEventTypes="typeViewClicked|typeWindowContentChanged"
android:packageNames="com.google.android.apps.messaging,com.samsung.android.messaging"
android:accessibilityFeedbackType="feedbackGeneric"
android:notificationTimeout="100"
android:canRetrieveWindowContent="true"
android:accessibilityFlags="flagReportViewIds"
    />


<!--
<accessibility-service
xmlns:android="http://schemas.android.com/apk/res/android"
android:accessibilityEventTypes="typeViewClicked"
android:accessibilityFeedbackType="feedbackGeneric"
android:canRetrieveWindowContent="true"
android:packageNames="com.google.android.apps.messaging"
android:accessibilityFlags="flagReportViewIds"
android:description="@string/app_name"
android:notificationTimeout="100" />-->
