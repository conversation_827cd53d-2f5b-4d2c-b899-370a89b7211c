package com.tech.ekvayu.EkService


import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Toast
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.PopupManager
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.R
import com.tech.ekvayu.Request.PendingMailRequest
import com.tech.ekvayu.Response.EmailResponse
import com.tech.ekvayu.Response.HashResponse
import com.tech.ekvayu.Response.PedingMailRes
import com.tech.ekvayu.databinding.LayoutProgressBinding
import com.tech.ekvayu.databinding.LayoutValidationBinding
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.io.BufferedReader
import java.io.File
import java.io.FileOutputStream
import java.io.FileReader
import java.io.IOException
import kotlin.String


class NewGmailAccessibilityService : AccessibilityService() {
    var instance: YahooAccessibilityService? = null
    private var currentPopupView: View? = null
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }

    companion object {
        var instance: YahooAccessibilityService? = null
    }


    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null) return

        if (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED) {
            if (event.packageName == "com.google.android.gm") {
                Handler(Looper.getMainLooper()).postDelayed({
                    val rootNode = rootInActiveWindow
                    try {
                        val root = rootInActiveWindow
                        if (root != null) extractEmailDetails(root)

                    } catch (e: Exception) {
                        Log.e("AccessibilityCrash", "Error in service: ${e.message}")
                    }
                }, 500)
            }
        }
    }

    private fun extractEmailDetails(rootNode: AccessibilityNodeInfo) {
        var senderName: String? = null
        var fromEmail: String? = null

        var subject: String? = null
        var date: String? = null
        var attachments: String? = null
        val emailBodyBuilder = StringBuilder()

        val toEmails = mutableListOf<String>()
        val ccEmails = mutableListOf<String>()
        val bccEmails = mutableListOf<String>()

        val possibleNodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, possibleNodes)

        for (node in possibleNodes) {

            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()

            Log.d("NodeScan", "Text: $text | ViewId: $viewId | Desc: $contentDesc")
            val actualText = text ?: contentDesc ?: continue

            // Subject
            if (subject == null && (viewId?.contains("subject", true) == true || actualText.startsWith("Subject", true))) {
                subject = actualText.removePrefix("Subject:").trim()
            }

            // From (Name + Email)f
            if (fromEmail == null && actualText.contains("•")) {
                val parts = actualText.split("•").map { it.trim() }
                if ( parts.size == 2) {
                    senderName = parts[0]
                    fromEmail = parts[1].extractEmail()
                }
            }

            if ((viewId?.contains("to", true) == true || actualText.contains("To:", true)) && actualText.contains("@")) {
                toEmails.addAll(actualText.extractEmails())
            }

            if (actualText.isValidEmail() && !toEmails.contains(actualText)) {
                toEmails.add(actualText)
            }

            if (viewId?.endsWith("/to") == true) {
                toEmails.addAll(actualText.extractEmails())
            }

            if (actualText.contains("@")) {
                Log.d("EmailCheck", "Possible email text: $actualText from $viewId")
            }

            // To
            if (actualText.startsWith("To:", true)) {
                toEmails.addAll(actualText.extractEmails())
            }

            // Cc
            if (actualText.startsWith("Cc:", true)) {
                ccEmails.addAll(actualText.extractEmails())
            }

            // Bcc
            if (actualText.startsWith("Bcc:", true)) {
                bccEmails.addAll(actualText.extractEmails())
            }

            // Date
            if (date == null && (
                        actualText.contains("AM") || actualText.contains("PM") ||
                                actualText.matches(Regex(".*\\d{1,2} [A-Za-z]+ \\d{4}.*"))
                        )
            ) {
                date = actualText
            }

            // Enhanced body content detection
            if (detectEmailBodyContent(actualText, viewId, contentDesc, node)) {
                emailBodyBuilder.appendLine(actualText)
                Log.d("BodyExtraction2", "📄 Added to body: ${actualText.take(50)}...")
            }

            // Attachments
            if (attachments == null && (
                        actualText.contains("Attachment", true) ||
                                actualText.contains(".pdf", true) ||
                                actualText.contains(".docx", true) ||
                                actualText.contains("Download", true))
            ) {
                attachments = "Attachments found"
            }

        }

        // Fallbacks
        subject = subject ?: "No Subject"
        fromEmail = fromEmail ?: "Unknown Sender"
        date = date ?: "Unknown Date"
        val body = if (emailBodyBuilder.isNotEmpty()) emailBodyBuilder.toString().trim() else "No Body Content"
        attachments = attachments ?: "No Attachments"

        val androidId = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)

        Log.d("EmailDetails", """
        senderName: $senderName
        fromEmail: $fromEmail
        toEmail: ${toEmails.joinToString(", ")}
        cc: ${ccEmails.joinToString(", ")}
        bcc: ${bccEmails.joinToString(", ")}
        subject: $subject
        date: $date
        deviceId: $androidId
        body: $body
        attachments: $attachments
    """.trimIndent())


        val emailContent = """
                   fromEmail: $fromEmail
                   toEmail: ${toEmails.joinToString(", ")}
                   cc: ${ccEmails.joinToString(", ")}
                   bcc: ${bccEmails.joinToString(", ")}
                   subject: $subject
                   date: $date
                   body: $body
                   attachments: $attachments
                   Content-Type: text/plain; charset=UTF-8 
               """.trimIndent()

        val file = File(applicationContext.filesDir, "email.eml")

        Log.d("getFilePath", "emlFile: " + file)

        try {
            val fos = FileOutputStream(file)
            fos.write(emailContent.toByteArray())
            fos.close()
            val emlContent = readEmlFile(file.absolutePath)
            if (emlContent != null) {
                Log.d("getEmailContnettt", "saveEmailAsEml: " + emlContent)
            }
        } catch (e: IOException) {
        }



        if (toEmails[0].isNotEmpty() && (fromEmail!= "Unknown Sender"))
        {

            Log.d("GetAttachment", "extractEmailDetails: "+attachments)
            // Log.d("getAllRightData", "receiverMail: "+toEmails[1].toString()+" senderMail "+fromEmail+" cc "+ccEmails+" bcc "+bccEmails+" subject "+ subject +" date "+date+" body "+body+" attachments "+attachments+"")
            sharedPrefManager.putString(AppConstant.receiverMail,toEmails[0].toString())
            getHashId(file,toEmails[0].toString(),fromEmail,ccEmails,bccEmails,subject,date,body,attachments)
        }

    }

    fun String.extractEmails(): List<String> {
        return Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}").findAll(this).map { it.value }.toList()
    }

    fun String.isValidEmail(): Boolean {
        return this.matches(Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"))
    }

    fun String.extractEmail(): String? = this.extractEmails().firstOrNull()

    private fun detectEmailBodyContent(text: String, viewId: String?, contentDesc: String?, node: AccessibilityNodeInfo): Boolean {

        if (text.length < 3) return false

        // Strategy 1: Check ViewId patterns for email body
        val bodyViewIds = listOf(
            "message_body", "email_body", "conversation_body", "mail_body",
            "content_body", "message_content", "email_content", "body_text",
            "conversation_text", "message_text", "compose_body", "reply_body"
        )

        if (viewId != null && bodyViewIds.any { viewId.contains(it, true) }) {
            Log.d("BodyDetection", "✅ STRATEGY 1 - ViewId match: $viewId")
            return true
        }

        // Strategy 2: Check content description
        val bodyDescriptions = listOf(
            "message body", "email body", "conversation body", "mail content",
            "message content", "email text", "body text"
        )

        if (contentDesc != null && bodyDescriptions.any { contentDesc.contains(it, true) }) {
            Log.d("BodyDetection", "✅ STRATEGY 2 - ContentDesc match: $contentDesc")
            return true
        }

        // Strategy 3: Check node class and hierarchy
        val className = node.className?.toString()
        if (className == "android.widget.TextView" || className == "android.widget.EditText") {
            val parent = node.parent
            val parentViewId = parent?.viewIdResourceName

            if (parentViewId != null && bodyViewIds.any { parentViewId.contains(it, true) }) {
                Log.d("BodyDetection", "✅ STRATEGY 3 - Parent ViewId match: $parentViewId")
                return true
            }
        }

        // Strategy 4: Content-based detection (improved logic)
        val isLikelyBodyContent = text.length >= 10 && // Reduced minimum length
            !isHeaderContent(text) &&
            !isNavigationContent(text) &&
            !isMetadataContent(text) &&
            isActualMessageContent(text)

        if (isLikelyBodyContent) {
            Log.d("BodyDetection", "✅ STRATEGY 4 - Content-based detection: ${text.take(30)}...")
            return true
        }

        // Strategy 5: Check for common email content patterns
        val emailContentPatterns = listOf(
            Regex("Dear .+,", RegexOption.IGNORE_CASE),
            Regex("Hello .+,", RegexOption.IGNORE_CASE),
            Regex("Hi .+,", RegexOption.IGNORE_CASE),
            Regex("Thank you", RegexOption.IGNORE_CASE),
            Regex("Please .+", RegexOption.IGNORE_CASE),
            Regex("Best regards", RegexOption.IGNORE_CASE),
            Regex("Sincerely", RegexOption.IGNORE_CASE),
            Regex("Kind regards", RegexOption.IGNORE_CASE)
        )

        if (emailContentPatterns.any { it.containsMatchIn(text) }) {
            Log.d("BodyDetection", "✅ STRATEGY 5 - Email pattern match: ${text.take(30)}...")
            return true
        }
        return false
    }

    private fun isHeaderContent(text: String): Boolean {
        val headerPatterns = listOf(
            "To:", "From:", "Cc:", "Bcc:", "Subject:", "Date:",
            "Reply", "Forward", "Delete", "Archive", "Mark as",
            "Inbox", "Sent", "Drafts", "Spam", "Trash"
        )
        return headerPatterns.any { text.startsWith(it, true) }
    }

    private fun isNavigationContent(text: String): Boolean {
        val navPatterns = listOf(
            "Back", "Next", "Previous", "Menu", "Settings", "Search",
            "Compose", "Send", "Cancel", "Save", "Edit", "More"
        )
        return navPatterns.any { text.equals(it, true) }
    }

    private fun isMetadataContent(text: String): Boolean {
        // Check for timestamps, dates, and other metadata
        return text.matches(Regex(".*\\d{1,2}:\\d{2}.*")) || // Time format
               text.matches(Regex(".*\\d{1,2}/\\d{1,2}/\\d{4}.*")) || // Date format
               text.matches(Regex(".*\\d{1,2} [A-Za-z]+ \\d{4}.*")) || // Date format
               text.contains("AM", true) || text.contains("PM", true) ||
               text.length < 10 && text.contains("@") // Short email addresses
    }

    private fun isActualMessageContent(text: String): Boolean {
        // Check if text looks like actual message content
        val wordCount = text.split("\\s+".toRegex()).size
        val hasMultipleWords = wordCount >= 3
        val hasProperLength = text.length >= 10
        val notJustNumbers = !text.matches(Regex("\\d+"))
        val notJustSymbols = text.any { it.isLetter() }

        return hasMultipleWords && hasProperLength && notJustNumbers && notJustSymbols
    }

    private fun getHashId(emlContent:File,email: String, fromEmail: String, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String, attachments: String) {

        if (hasOverlayPermission(applicationContext)) {

            val fileRequestBody = emlContent.asRequestBody("application/octet-stream".toMediaTypeOrNull())
            val bodyReqBody = body.toRequestBody("application/octet-stream".toMediaTypeOrNull())

            val tomail = email.toString().toRequestBody("application/octet-stream".toMediaTypeOrNull())
            val senderEmail = fromEmail.toString().toRequestBody("application/octet-stream".toMediaTypeOrNull())

            val ccReqBody = cc.toString().toRequestBody("application/octet-stream".toMediaTypeOrNull())
            val bccReqBody = bcc.toString().toRequestBody("application/octet-stream".toMediaTypeOrNull())
            val subjectReqBody = subject.toRequestBody("application/octet-stream".toMediaTypeOrNull())

            val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)

            val retrofit = ApiClient.getRetrofitInstance(applicationContext)
            val apiService = retrofit!!.create(ApiService::class.java)
            val windowManager =getSystemService(WINDOW_SERVICE) as WindowManager
            val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as android.view.LayoutInflater
            val binding = LayoutProgressBinding.inflate(layoutInflater)
            val view = binding.root
            view.setBackgroundResource(R.drawable.bg_alert_dialog)
            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                else
                    WindowManager.LayoutParams.TYPE_PHONE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
                android.graphics.PixelFormat.TRANSLUCENT
            )
            params.dimAmount = 0.5f
            windowManager.addView(view, params)



            apiService.getHashKey(tomail,senderEmail,filePart,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
                .enqueue(object : Callback<HashResponse> {
                    override fun onResponse(
                        call: Call<HashResponse>,
                        response: Response<HashResponse>
                    ) {
                        windowManager.removeView(view)

                        if (response.body()!!.Code.toString()=="1")
                        {
                            windowManager.removeView(view)
                            sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                            sharedPrefManager.putString(AppConstant.receiverMail,email)
                            // Mark mail as configured when we successfully process an email
                            sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)
                            checkAiResponse(email,response.body()?.hashId.toString())
                        }

                        else
                        {
                            windowManager.removeView(view)
                            uploadFile(email,fromEmail,emlContent,cc,bcc,subject,date,body)
                        }
                    }

                    override fun onFailure(call: Call<HashResponse>, t: Throwable) {
                        windowManager.removeView(view)
                    }
                })
        }

        else
        {
            requestOverlayPermission(applicationContext)
        }
    }

    private fun uploadFile(email: String, fromEmail: String, emlContent: File, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String) {

        val emailRequestBody = email.toRequestBody("text/plain".toMediaTypeOrNull())
        val senderEmail = fromEmail.toString().toRequestBody("text/plain".toMediaTypeOrNull())
        val fileRequestBody = emlContent.asRequestBody("application/octet-stream".toMediaTypeOrNull())
        val bodyReqBody = body.toRequestBody("application/octet-stream".toMediaTypeOrNull())

        val ccReqBody = cc.toString().toRequestBody("application/octet-stream".toMediaTypeOrNull())
        val bccReqBody = bcc.toString().toRequestBody("application/octet-stream".toMediaTypeOrNull())
        val subjectReqBody = subject.toRequestBody("application/octet-stream".toMediaTypeOrNull())


        val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)


        apiService.uploadFile(emailRequestBody,senderEmail,filePart,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
            .enqueue(object : Callback<EmailResponse> {
                override fun onResponse(
                    call: Call<EmailResponse>,
                    response: Response<EmailResponse>
                ) {

                    if (response.body()!!.Code==1)
                    {
                        sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                        sharedPrefManager.putString(AppConstant.receiverMail,email)
                        // Mark mail as configured when we successfully process an email
                        sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)
                        //  setResponsePopup(response.body()?.unsafeReasons,response.body()?.emailStatu0s.toString(),email,response.body()?.hashId.toString())
                        checkAiResponse(email,response.body()?.hashId.toString())
                    }
                    else
                    {
                        Toast.makeText(applicationContext, ""+response.body()!!.message, Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<EmailResponse?>, t: Throwable) {

                }

            })
    }

    private fun checkAiResponse(email: String, hashId: String) {
        Log.d("getemailhashId", "checkAiResponse: "+email+"  "+hashId)
        // Get system services
        val windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater

        // Inflate loading layout
        val binding = LayoutProgressBinding.inflate(layoutInflater)
        val view = binding.root
       //  view.setBackgroundResource(R.drawable.bg_alert_dialog)

        // Define layout params
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

        windowManager.addView(view, params)
        val handler = Handler(Looper.getMainLooper())
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)

        var retryCount = 0
        val maxRetries = 20

        fun pollStatus() {
            if (retryCount >= maxRetries) {
                windowManager.removeView(view)
                Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                return
            }

            val request = PendingMailRequest(email = email, hashId = hashId)

            apiService.pendingMailStatusAi(request)
                .enqueue(object : Callback<PedingMailRes> {
                    override fun onResponse(call: Call<PedingMailRes>, response: Response<PedingMailRes>) {
                        val data = response.body()?.data
                        val status = data?.emlStatus?.lowercase()?.trim()
                        binding.tvStatus.setText(data?.unsafeReasons.toString())

                        if (status == "safe" || status == "unsafe") {
                            windowManager.removeView(view)
                            setResponsePopup(
                                unsafeReasons = data.unsafeReasons.toString(),
                                mailStatus = data.emlStatus.toString(),
                                email = email,
                                hashId = hashId
                            )
                        }
                        else if (retryCount==10 && status == "pending")
                        {
                            windowManager.removeView(view)
                        }

                        else {
                            retryCount++
                            handler.postDelayed({ pollStatus() }, 5000)
                        }
                    }

                    override fun onFailure(call: Call<PedingMailRes>, t: Throwable) {
                        Log.e("checkAiResponse", "API call failed: ${t.message}")
                        retryCount++
                        handler.postDelayed({ pollStatus() }, 5000)
                    }
                })
        }

        // Start polling
        pollStatus()
    }

    private fun setResponsePopup(unsafeReasons: String?, mailStatus: String, email: String, hashId: String) {
        Log.d("getReason", "setResponsePopup: $unsafeReasons   $mailStatus")

        // Use the new PopupManager for enhanced UX
        when (mailStatus.lowercase()) {
            "safe" -> {
                PopupManager.showSafeEmailPopup(
                    context = applicationContext,
                    message = unsafeReasons ?: "Email content has been verified as safe. No threats detected.",
                    onClose = {
                       // Log.d(TAG, "✅ Safe email popup closed")
                    }
                )
            }
            "unsafe" -> {
                PopupManager.showUnsafeEmailPopup(
                    context = applicationContext,
                    reasons = unsafeReasons ?: "This email contains suspicious content that may be harmful.",
                    onClose = {

                    },
                    onViewDetails = {

                    }
                )
            }
            "pending" -> {
                PopupManager.showPendingAnalysisPopup(
                    context = applicationContext,
                    message = "Analyzing email content for potential threats. Please wait..."
                )
            }
            else -> {
                // Fallback for unknown status
                PopupManager.showPopup(
                    context = applicationContext,
                    config = PopupManager.PopupConfig(
                        type = PopupManager.PopupType.WARNING,
                        title = "Email Analysis",
                        subtitle = "Status: $mailStatus",
                        message = unsafeReasons ?: "Email analysis completed with unknown status.",
                        primaryButtonText = "Close",
                        onPrimaryAction = {
                         //   Log.d(TAG, "❓ Unknown status popup closed")
                        }
                    )
                )
            }
        }
    }

    private fun hasOverlayPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true
        }
    }

    private fun requestOverlayPermission(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(context)) {
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:$context.packageName")
                )
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
            }
        }
    }

    private fun readEmlFile(filePath: String): String? {
        val file = File(filePath)
        if (!file.exists()) {
            return null
        }
        val stringBuilder = StringBuilder()
        try {
            val reader = BufferedReader(FileReader(file))
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                stringBuilder.append(line).append("\n")
            }
            reader.close()
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
        return stringBuilder.toString()
    }

    private fun findEmailNodes(node: AccessibilityNodeInfo?, emailNodes: MutableList<AccessibilityNodeInfo>) {
        if (node == null) return

        if (node.className == "android.widget.TextView" && node.text != null) {
            emailNodes.add(node)
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                findEmailNodes(child, emailNodes)
            }
        }
    }

    override fun onInterrupt() {

    }


}
