package com.tech.ekvayu.BaseClass

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.animation.DecelerateInterpolator
import androidx.core.content.ContextCompat
import com.tech.ekvayu.R
import com.tech.ekvayu.databinding.LayoutValidationBinding

object PopupManager {
    
    private const val TAG = "PopupManager"
    private var currentPopupView: View? = null
    private val handler = Handler(Looper.getMainLooper())
    
    enum class PopupType {
        SAFE, UNSAFE, PENDING, WARNING, ERROR
    }
    
    data class PopupConfig(
        val type: PopupType,
        val title: String,
        val subtitle: String,
        val message: String,
        val primaryButtonText: String = "Close",
        val secondaryButtonText: String? = null,
        val showProgress: Boolean = false,
        val autoDismissDelay: Long = 0L, // 0 means no auto dismiss
        val onPrimaryAction: (() -> Unit)? = null,
        val onSecondaryAction: (() -> Unit)? = null,
        val onDismiss: (() -> Unit)? = null
    )
    
    /**
     * Show enhanced popup with modern design
     */
    fun showPopup(context: Context, config: PopupConfig) {
        if (!hasOverlayPermission(context)) {
            Log.w(TAG, "Overlay permission not granted")
            return
        }
        
        try {
            // Remove existing popup
            dismissCurrentPopup(context)
            
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val layoutInflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            val binding = LayoutValidationBinding.inflate(layoutInflater)
            
            // Configure popup based on type and config
            configurePopupUI(binding, config)
            
            // Setup window parameters
            val params = createWindowParams()
            
            // Setup click listeners
            setupClickListeners(binding, config, windowManager)
            
            // Add popup to window with animation
            windowManager.addView(binding.root, params)
            currentPopupView = binding.root
            
            // Animate popup entrance
            animatePopupEntrance(binding.cardPopup)
            
            // Setup auto dismiss if configured
            if (config.autoDismissDelay > 0) {
                handler.postDelayed({
                    dismissCurrentPopup(context)
                    config.onDismiss?.invoke()
                }, config.autoDismissDelay)
            }
            
            Log.d(TAG, "✅ Popup displayed successfully: ${config.type}")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error showing popup: ${e.message}", e)
        }
    }
    
    /**
     * Configure popup UI based on type and configuration
     */
    private fun configurePopupUI(binding: LayoutValidationBinding, config: PopupConfig) {
        // Set titles and message
        binding.tvStatusTitle.text = config.title
        binding.tvStatusSubtitle.text = config.subtitle
        binding.tvMessage.text = config.message
        
        // Configure animation and colors based on type
        when (config.type) {
            PopupType.SAFE -> {
                binding.lvWarning.setAnimation(R.raw.safe)
                binding.cardStatusIndicator.setCardBackgroundColor(
                    ContextCompat.getColor(binding.root.context, R.color.green_light)
                )
                binding.tvStatusTitle.setTextColor(
                    ContextCompat.getColor(binding.root.context, R.color.green)
                )
            }
            PopupType.UNSAFE -> {
                binding.lvWarning.setAnimation(R.raw.unsafe)
                binding.cardStatusIndicator.setCardBackgroundColor(
                    ContextCompat.getColor(binding.root.context, R.color.red_light)
                )
                binding.tvStatusTitle.setTextColor(
                    ContextCompat.getColor(binding.root.context, R.color.red)
                )
            }
            PopupType.PENDING -> {
                binding.lvWarning.setAnimation(R.raw.wait)
                binding.cardStatusIndicator.setCardBackgroundColor(
                    ContextCompat.getColor(binding.root.context, R.color.orange_light)
                )
                binding.tvStatusTitle.setTextColor(
                    ContextCompat.getColor(binding.root.context, R.color.orange)
                )
            }
            PopupType.WARNING -> {
                binding.lvWarning.setAnimation(R.raw.wait)
                binding.cardStatusIndicator.setCardBackgroundColor(
                    ContextCompat.getColor(binding.root.context, R.color.yellow_light)
                )
                binding.tvStatusTitle.setTextColor(
                    ContextCompat.getColor(binding.root.context, R.color.yellow)
                )
            }
            PopupType.ERROR -> {
                binding.lvWarning.setAnimation(R.raw.unsafe)
                binding.cardStatusIndicator.setCardBackgroundColor(
                    ContextCompat.getColor(binding.root.context, R.color.red_light)
                )
                binding.tvStatusTitle.setTextColor(
                    ContextCompat.getColor(binding.root.context, R.color.red)
                )
            }
        }
        
        // Configure buttons
        binding.btClose.text = config.primaryButtonText
        
      /*  if (config.secondaryButtonText != null) {
            binding.btSecondary.visibility = View.VISIBLE
            binding.btSecondary.text = config.secondaryButtonText
        } else {
            binding.btSecondary.visibility = View.GONE
        }*/
        
        // Configure progress indicator
        binding.progressIndicator.visibility = if (config.showProgress) View.VISIBLE else View.GONE
    }
    
    /**
     * Setup click listeners for popup buttons
     */
    private fun setupClickListeners(
        binding: LayoutValidationBinding,
        config: PopupConfig,
        windowManager: WindowManager
    ) {
        binding.btClose.setOnClickListener {

            animatePopupExit(binding.cardPopup) {
                try {
                    windowManager.removeView(binding.root)
                    currentPopupView = null
                    config.onPrimaryAction?.invoke()
                } catch (e: Exception) {
                    Log.e(TAG, "Error removing popup: ${e.message}")
                }
            }

        }
        
        /*binding.btSecondary.setOnClickListener {
            config.onSecondaryAction?.invoke()
        }*/
    }
    
    /**
     * Create window layout parameters
     */
    private fun createWindowParams(): WindowManager.LayoutParams {
        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        ).apply {
            dimAmount = 0.6f
        }
    }
    
    /**
     * Animate popup entrance
     */
    private fun animatePopupEntrance(view: View) {
        view.alpha = 0f
        view.scaleX = 0.8f
        view.scaleY = 0.8f
        
        val fadeIn = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
        val scaleXIn = ObjectAnimator.ofFloat(view, "scaleX", 0.8f, 1f)
        val scaleYIn = ObjectAnimator.ofFloat(view, "scaleY", 0.8f, 1f)
        
        AnimatorSet().apply {
            playTogether(fadeIn, scaleXIn, scaleYIn)
            duration = 300
            interpolator = DecelerateInterpolator()
            start()
        }
    }
    
    /**
     * Animate popup exit
     */
    private fun animatePopupExit(view: View, onComplete: () -> Unit) {
        val fadeOut = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f)
        val scaleXOut = ObjectAnimator.ofFloat(view, "scaleX", 1f, 0.8f)
        val scaleYOut = ObjectAnimator.ofFloat(view, "scaleY", 1f, 0.8f)
        
        AnimatorSet().apply {
            playTogether(fadeOut, scaleXOut, scaleYOut)
            duration = 200
            interpolator = DecelerateInterpolator()
            start()
        }.addListener(object : android.animation.AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: android.animation.Animator) {
                onComplete()
            }
        })
    }
    
    /**
     * Dismiss current popup if exists
     */
    fun dismissCurrentPopup(context: Context) {
        currentPopupView?.let { view ->
            try {
                val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
                windowManager.removeView(view)
                currentPopupView = null
                Log.d(TAG, "✅ Current popup dismissed")
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error dismissing popup: ${e.message}")
            }
        }
    }
    
    /**
     * Check if overlay permission is granted
     */
    private fun hasOverlayPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true
        }
    }
    
    /**
     * Convenience methods for common popup types
     */
    fun showSafeEmailPopup(context: Context, message: String, onClose: (() -> Unit)? = null) {
        showPopup(context, PopupConfig(
            type = PopupType.SAFE,
            title = "Email is Safe",
            subtitle = "No threats detected",
            message = message,
           // primaryButtonText = "Continue",
            autoDismissDelay = 3000L,
            onPrimaryAction = onClose
        ))
    }
    
    fun showUnsafeEmailPopup(context: Context, reasons: String, onClose: (() -> Unit)? = null, onViewDetails: (() -> Unit)? = null) {
        showPopup(context, PopupConfig(
            type = PopupType.UNSAFE,
            title = "Threat Detected",
            subtitle = "This email may be dangerous",
            message = reasons,
            primaryButtonText = "Close",
            secondaryButtonText = if (onViewDetails != null) "View Details" else null,
            onPrimaryAction = onClose,
            onSecondaryAction = onViewDetails
        ))
    }
    
    fun showPendingAnalysisPopup(context: Context, message: String = "Analyzing email content for threats...") {
        showPopup(context, PopupConfig(
            type = PopupType.PENDING,
            title = "Security Check",
            subtitle = "Please wait",
            message = message,
            primaryButtonText = "Cancel",
            showProgress = true
        ))
    }
}
