package com.tech.ekvayu.Fragments

import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.tech.ekvayu.ActivityGraph.ActivityGraphResponse
import com.tech.ekvayu.Adapter.RecentActivityAdapter
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.BaseFragment
import com.tech.ekvayu.BaseClass.CommonUtil
import com.tech.ekvayu.BaseClass.DeviceInfoHelper
import com.tech.ekvayu.BaseClass.PermissionHelper
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.BaseClass.SmsDetectionHelper
import com.tech.ekvayu.R
import com.tech.ekvayu.Request.CommonRequest
import com.tech.ekvayu.databinding.FragmentAnalyticsBinding
import com.tech.ekvayu.models.*
import org.json.JSONArray
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.text.SimpleDateFormat
import java.util.*

class AnalyticsFragment : BaseFragment() {

    private var _binding: FragmentAnalyticsBinding? = null
    private val binding get() = _binding!!

    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    private lateinit var recentActivityAdapter: RecentActivityAdapter


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAnalyticsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupUI()
        loadAnalyticsData()
    }

    private fun setupUI() {
        // Setup swipe-to-refresh
        setupSwipeRefresh()

        // Setup recent activity RecyclerView
        setupRecentActivityRecyclerView()

        // Setup pie chart
        setupPieChart()
    }

    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.apply {

            setColorSchemeColors(
                ContextCompat.getColor(requireContext(), R.color.app_color),
                ContextCompat.getColor(requireContext(), R.color.app_color_light),
                ContextCompat.getColor(requireContext(), R.color.app_color_dark)
            )
            setProgressBackgroundColorSchemeColor(
                ContextCompat.getColor(requireContext(), R.color.surface)
            )
            setOnRefreshListener {
                refreshAnalyticsData()
            }
        }
    }

    private fun setupRecentActivityRecyclerView() {
        recentActivityAdapter = RecentActivityAdapter(mutableListOf()) { activity ->
            showActivityDetails(activity)
        }

        binding.rvRecentActivity.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = recentActivityAdapter
        }
    }

    private fun setupPieChart() {
        binding.pieChartEmails.apply {
            description.isEnabled = false
            isRotationEnabled = true
            setUsePercentValues(true)
            setEntryLabelTextSize(12f)
            setEntryLabelColor(ContextCompat.getColor(requireContext(), R.color.text_primary))
            centerText = "Email\nDetection"
            setCenterTextSize(14f)
            setCenterTextColor(ContextCompat.getColor(requireContext(), R.color.text_primary))
            legend.isEnabled = true
            legend.textColor = ContextCompat.getColor(requireContext(), R.color.text_primary)
            setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.card_background))
        }
    }

    private fun loadAnalyticsData() {
        Log.d(TAG, "Loading analytics data...")

        // Show loading state
        showLoadingState()

        // Load different data sources
        loadEmailStats()
        loadSmsStats()
        loadDeviceInfo()
        loadSecurityStatus()
        loadRecentActivity()

        // Update last updated time
        updateLastUpdatedTime()
    }

    private fun refreshAnalyticsData() {
        loadAnalyticsData()
        binding.swipeRefreshLayout.postDelayed({
            binding.swipeRefreshLayout.isRefreshing = false
        }, 1000)
    }

    private fun showLoadingState() {
        binding.tvTotalEmails.text = "..."
        binding.tvSpamEmails.text = "..."
        binding.tvSmsThreats.text = "..."
        binding.tvDisputes.text = "..."
    }

    private fun loadEmailStats() {
        val receiverEmail = sharedPrefManager.getString(AppConstant.receiverMail, "")

        if (receiverEmail.isEmpty()) {
            Log.w(TAG, "No receiver email configured")
            updateEmailStatsUI(EmailStats())
            return
        }

        val retrofit = ApiClient.getRetrofitInstance(requireContext())
        if (retrofit == null) {
            Log.e(TAG, "Failed to get Retrofit instance")
            updateEmailStatsUI(EmailStats())
            return
        }

        val apiService = retrofit.create(ApiService::class.java)
        val request = CommonRequest(emailId = receiverEmail)

        // Load activity graph data
        apiService.getActivtyGraph(request)
            .enqueue(object : Callback<ActivityGraphResponse> {
                override fun onResponse(
                    call: Call<ActivityGraphResponse>,
                    response: Response<ActivityGraphResponse>
                ) {
                    if (response.isSuccessful && response.body() != null) {
                        val data = response.body()!!.data
                        val emailStats = EmailStats(
                            totalEmails = data?.totalProcessedEmails ?: 0,
                            spamEmails = data?.totalSpamEmails ?: 0,
                            disputes = data?.totalDisputes ?: 0,
                            safeEmails = (data?.totalProcessedEmails ?: 0) - (data?.totalSpamEmails ?: 0),
                            lastProcessedTime = getCurrentTimeString()
                        )
                        updateEmailStatsUI(emailStats)
                        updateEmailChart(emailStats)
                    } else {
                        Log.e(TAG, "Failed to load email stats: ${response.message()}")
                        updateEmailStatsUI(EmailStats())
                    }

                    // Stop refresh animation if it's running
                    stopRefreshAnimation()
                }

                override fun onFailure(call: Call<ActivityGraphResponse>, t: Throwable) {
                    Log.e(TAG, "Error loading email stats: ${t.message}", t)
                    updateEmailStatsUI(EmailStats())

                    // Stop refresh animation if it's running
                    stopRefreshAnimation()
                }
            })
    }

    private fun loadSmsStats() {
        try {
            // Get SMS data from SharedPreferences (same way as SmsDetectionFragment)
            val smsDataJson = sharedPrefManager.getString("sms_detection_data", "[]")

            if (smsDataJson.isBlank() || smsDataJson == "[]") {
                Log.d(TAG, "📱 No SMS data found in SharedPreferences")
                updateSmsStatsUI(SmsStats())
                return
            }

            val smsArray = JSONArray(smsDataJson)

            var totalSms = 0
            var suspiciousSms = 0
            var highRiskSms = 0
            var mediumRiskSms = 0
            var lowRiskSms = 0

            for (i in 0 until smsArray.length()) {
                try {
                    val smsObject = smsArray.getJSONObject(i)
                    totalSms++

                    // Safely check for suspicious flag
                    if (smsObject.optBoolean("isSuspicious", false)) {
                        suspiciousSms++
                    }

                    // Safely get risk level
                    val riskLevel = smsObject.optString("riskLevel", "UNKNOWN")
                    when (riskLevel.uppercase()) {
                        "HIGH" -> highRiskSms++
                        "MEDIUM" -> mediumRiskSms++
                        "LOW" -> lowRiskSms++
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "⚠️ Error parsing SMS object at index $i: ${e.message}")
                }
            }

            val smsStats = SmsStats(
                totalSms = totalSms,
                suspiciousSms = suspiciousSms,
                safeSms = totalSms - suspiciousSms,
                highRiskSms = highRiskSms,
                mediumRiskSms = mediumRiskSms,
                lowRiskSms = lowRiskSms,
                lastScanTime = getCurrentTimeString()
            )

            updateSmsStatsUI(smsStats)
            Log.d(TAG, "✅ SMS stats loaded: Total=$totalSms, Suspicious=$suspiciousSms, High Risk=$highRiskSms")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error loading SMS stats: ${e.message}", e)
            updateSmsStatsUI(SmsStats())
        }
    }

    private fun loadDeviceInfo() {
        try {
            val deviceInfoHelper = DeviceInfoHelper(requireContext())
            val deviceInfo = deviceInfoHelper.getDeviceInfo()

            val deviceAnalytics = DeviceAnalytics(
                deviceModel = "${deviceInfo.manufacturer} ${deviceInfo.model}",
                androidVersion = "Android ${deviceInfo.androidVersion}",
                availableMemoryGB = deviceInfo.memoryInfo.getAvailableRAMGB(),
                totalMemoryGB = deviceInfo.memoryInfo.getTotalRAMGB(),
                storageUsedPercentage = deviceInfo.storageInfo.getStorageUsagePercentage(),
                freeStorageGB = deviceInfo.storageInfo.getFreeInternalGB(),
                batteryLevel = deviceInfo.batteryInfo.level,
                isCharging = deviceInfo.batteryInfo.isCharging,
                networkType = deviceInfo.networkInfo.connectionType
            )

            updateDeviceInfoUI(deviceAnalytics)
            Log.d(TAG, "✅ Device info loaded: ${deviceAnalytics.deviceModel}")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error loading device info: ${e.message}", e)
            updateDeviceInfoUI(DeviceAnalytics())
        }
    }

    private fun loadSecurityStatus() {
        val securityStatus = SecurityStatus(
            gmailServiceEnabled = PermissionHelper.checkSpecificAccessibilityService(
                requireContext(), PermissionHelper.ServiceType.GMAIL
            ),
            smsServiceEnabled = PermissionHelper.checkSpecificAccessibilityService(
                requireContext(), PermissionHelper.ServiceType.SMS
            ),
            yahooServiceEnabled = PermissionHelper.checkSpecificAccessibilityService(
                requireContext(), PermissionHelper.ServiceType.YAHOO
            ),
            outlookServiceEnabled = PermissionHelper.checkSpecificAccessibilityService(
                requireContext(), PermissionHelper.ServiceType.OUTLOOK
            ),
            accessibilityPermissionGranted = PermissionHelper.checkAndUpdateAccessibilityPermission(requireContext())
        )

        updateSecurityStatusUI(securityStatus)
    }

    private fun loadRecentActivity() {
        // Generate sample recent activity data
        val recentActivities = generateSampleRecentActivity()
        updateRecentActivityUI(recentActivities)
    }

    private fun generateSampleRecentActivity(): List<RecentActivity> {
        val activities = mutableListOf<RecentActivity>()
        val currentTime = System.currentTimeMillis()

        // Add some sample activities
        activities.add(
            RecentActivity(
                id = "1",
                type = ActivityType.EMAIL_DETECTED,
                title = "Email Detected",
                description = "New email processed from Gmail",
                timestamp = currentTime - 300000, // 5 minutes ago
                status = ActivityStatus.SUCCESS,
                icon = ActivityType.EMAIL_DETECTED.icon
            )
        )

        activities.add(
            RecentActivity(
                id = "2",
                type = ActivityType.SPAM_BLOCKED,
                title = "Spam Blocked",
                description = "Suspicious email blocked automatically",
                timestamp = currentTime - 900000, // 15 minutes ago
                status = ActivityStatus.BLOCKED,
                icon = ActivityType.SPAM_BLOCKED.icon
            )
        )

        activities.add(
            RecentActivity(
                id = "3",
                type = ActivityType.SMS_DETECTED,
                title = "SMS Analyzed",
                description = "SMS message scanned for threats",
                timestamp = currentTime - 1800000, // 30 minutes ago
                status = ActivityStatus.SUCCESS,
                icon = ActivityType.SMS_DETECTED.icon
            )
        )

        return activities
    }

    // UI Update Methods
    private fun updateEmailStatsUI(emailStats: EmailStats) {
        if (_binding == null) return

        binding.tvTotalEmails.text = emailStats.totalEmails.toString()
        binding.tvSpamEmails.text = emailStats.spamEmails.toString()
        binding.tvDisputes.text = emailStats.disputes.toString()
    }

    private fun updateSmsStatsUI(smsStats: SmsStats) {
        if (_binding == null) return

        binding.tvSmsThreats.text = smsStats.suspiciousSms.toString()
    }

    private fun updateDeviceInfoUI(deviceAnalytics: DeviceAnalytics) {
        if (_binding == null) return

        binding.tvDeviceModel.text = deviceAnalytics.deviceModel
        binding.tvAndroidVersion.text = deviceAnalytics.androidVersion
        binding.tvAvailableMemory.text = deviceAnalytics.availableMemoryGB
        binding.tvStorageUsed.text = "${deviceAnalytics.storageUsedPercentage}% (${deviceAnalytics.freeStorageGB} free)"
    }

    private fun updateSecurityStatusUI(securityStatus: SecurityStatus) {
        // Update Gmail status
        binding.tvGmailStatus.text = if (securityStatus.gmailServiceEnabled) "✅ Enabled" else "❌ Disabled"
        binding.tvGmailStatus.setTextColor(
            if (securityStatus.gmailServiceEnabled)
                ContextCompat.getColor(requireContext(), R.color.green)
            else
                ContextCompat.getColor(requireContext(), R.color.red)
        )

        // Update SMS status
        binding.tvSmsStatus.text = if (securityStatus.smsServiceEnabled) "✅ Enabled" else "❌ Disabled"
        binding.tvSmsStatus.setTextColor(
            if (securityStatus.smsServiceEnabled)
                ContextCompat.getColor(requireContext(), R.color.green)
            else
                ContextCompat.getColor(requireContext(), R.color.red)
        )

        // Update Yahoo status
        binding.tvYahooStatus.text = if (securityStatus.yahooServiceEnabled) "✅ Enabled" else "❌ Disabled"
        binding.tvYahooStatus.setTextColor(
            if (securityStatus.yahooServiceEnabled)
                ContextCompat.getColor(requireContext(), R.color.green)
            else
                ContextCompat.getColor(requireContext(), R.color.red)
        )

        // Update Outlook status
        binding.tvOutlookStatus.text = if (securityStatus.outlookServiceEnabled) "✅ Enabled" else "❌ Disabled"
        binding.tvOutlookStatus.setTextColor(
            if (securityStatus.outlookServiceEnabled)
                ContextCompat.getColor(requireContext(), R.color.green)
            else
                ContextCompat.getColor(requireContext(), R.color.red)
        )
    }

    private fun updateEmailChart(emailStats: EmailStats) {
        try {
            val entries = mutableListOf<PieEntry>()

            if (emailStats.safeEmails > 0) {
                entries.add(PieEntry(emailStats.safeEmails.toFloat(), "Safe"))
            }
            if (emailStats.spamEmails > 0) {
                entries.add(PieEntry(emailStats.spamEmails.toFloat(), "Spam"))
            }
            if (emailStats.pendingEmails > 0) {
                entries.add(PieEntry(emailStats.pendingEmails.toFloat(), "Pending"))
            }

            if (entries.isEmpty()) {
                entries.add(PieEntry(1f, "No Data"))
            }

            val dataSet = PieDataSet(entries, "Email Detection")
            dataSet.colors = listOf(
                ContextCompat.getColor(requireContext(), R.color.green),
                ContextCompat.getColor(requireContext(), R.color.red),
                ContextCompat.getColor(requireContext(), R.color.orange),
                ContextCompat.getColor(requireContext(), R.color.text_secondary)
            )

            val data = PieData(dataSet)
            data.setValueTextSize(12f)
            data.setValueTextColor(ContextCompat.getColor(requireContext(), R.color.text_primary))

            binding.pieChartEmails.data = data
            binding.pieChartEmails.invalidate()

        } catch (e: Exception) {
            Log.e(TAG, "Error updating email chart: ${e.message}", e)
        }
    }

    private fun updateRecentActivityUI(activities: List<RecentActivity>) {
        if (activities.isEmpty()) {
            binding.rvRecentActivity.visibility = View.GONE
            binding.tvNoActivity.visibility = View.VISIBLE
        } else {
            binding.rvRecentActivity.visibility = View.VISIBLE
            binding.tvNoActivity.visibility = View.GONE

            // Update adapter with new data
            recentActivityAdapter.updateActivities(activities)
        }
    }

    private fun updateLastUpdatedTime() {
        val currentTime = getCurrentTimeString()
        binding.tvLastUpdated.text = "Last updated: $currentTime"
    }

    private fun getCurrentTimeString(): String {
        val sdf = SimpleDateFormat("MMM dd, HH:mm", Locale.getDefault())
        return sdf.format(Date())
    }

    private fun stopRefreshAnimation() {
        if (_binding != null && binding.swipeRefreshLayout.isRefreshing) {
            binding.swipeRefreshLayout.isRefreshing = false
        }
    }

    /**
     * Programmatically trigger refresh (useful for testing or external triggers)
     */
    fun triggerRefresh() {
        if (_binding != null) {
            binding.swipeRefreshLayout.isRefreshing = true
            refreshAnalyticsData()
        }
    }

    private fun showActivityDetails(activity: RecentActivity) {
        val message = """
            Type: ${activity.type.displayName}
            Status: ${activity.status.displayName}
            Time: ${activity.getTimeAgo()}

            ${activity.description}
        """.trimIndent()

        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle(activity.title)
            .setMessage(message)
            .setPositiveButton("OK", null)
            .show()
    }

    override fun onResume() {
        super.onResume()
        // Refresh chart colors for theme changes
        setupPieChart()
        // Refresh swipe colors for theme changes
        setupSwipeRefresh()
        // Load data only on first time (onViewCreated handles initial load)
        // User can swipe to refresh manually
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
