package com.tech.ekvayu.Fragments

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import com.tech.ekvayu.BaseClass.BaseFragment
import com.tech.ekvayu.databinding.FragmentInternetSpeedBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL
import kotlin.math.roundToInt

class InternetSpeedFragment : BaseFragment() {

    private var _binding: FragmentInternetSpeedBinding? = null
    private val binding get() = _binding!!

    private var isTestRunning = false

    // Test configuration for accuracy
    private val downloadTestDuration = 10 // seconds
    private val uploadTestDuration = 8 // seconds
    private val warmupDuration = 2 // seconds
    private var testStartTime = 0L
    private var totalBytesTransferred = 0L

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentInternetSpeedBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupUI()
        checkNetworkStatus()
    }

    private fun setupUI() {
        binding.btnStartTest.setOnClickListener {
            if (!isTestRunning) {
                startSpeedTest()
            } else {
                stopSpeedTest()
            }
        }

        binding.btnRefresh.setOnClickListener {
            checkNetworkStatus()
            resetResults()
        }

        // Initialize UI
        resetResults()
    }

    private fun checkNetworkStatus() {
        val connectivityManager = requireContext().getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork
        val networkCapabilities = connectivityManager.getNetworkCapabilities(network)

        if (networkCapabilities != null) {
            when {
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> {
                    binding.tvNetworkType.text = "WiFi"
                    binding.ivNetworkIcon.setImageResource(android.R.drawable.ic_dialog_info)
                }
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                    binding.tvNetworkType.text = "Mobile Data"
                    binding.ivNetworkIcon.setImageResource(android.R.drawable.ic_dialog_info)
                }
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> {
                    binding.tvNetworkType.text = "Ethernet"
                    binding.ivNetworkIcon.setImageResource(android.R.drawable.ic_dialog_info)
                }
                else -> {
                    binding.tvNetworkType.text = "Unknown"
                    binding.ivNetworkIcon.setImageResource(android.R.drawable.ic_dialog_alert)
                }
            }
            binding.tvConnectionStatus.text = "Connected"
        } else {
            binding.tvNetworkType.text = "No Connection"
            binding.tvConnectionStatus.text = "Disconnected"
            binding.ivNetworkIcon.setImageResource(android.R.drawable.ic_dialog_alert)
        }
    }

    private fun startSpeedTest() {
        if (!isNetworkAvailable()) {
            Toast.makeText(requireContext(), "No internet connection available", Toast.LENGTH_SHORT).show()
            return
        }

        isTestRunning = true
        binding.btnStartTest.text = "Stop Test"
        binding.progressBar.visibility = View.VISIBLE
        binding.tvStatus.text = "Testing..."

        // Reset previous results
        binding.tvDownloadSpeed.text = "-- Mbps"
        binding.tvUploadSpeed.text = "-- Mbps"
        binding.tvPing.text = "-- ms"

        lifecycleScope.launch {
            try {
                // Update progress bar to determinate
                withContext(Dispatchers.Main) {
                    binding.progressBar.isIndeterminate = false
                    binding.progressBar.max = 100
                    binding.progressBar.progress = 0
                }

                // Test ping (20% of progress)
                withContext(Dispatchers.Main) {
                    binding.tvStatus.text = "Testing ping..."
                    binding.progressBar.progress = 10
                }
                val ping = testPing()
                withContext(Dispatchers.Main) {
                    binding.tvPing.text = if (ping > 0) "${ping}ms" else "Failed"
                    binding.progressBar.progress = 20
                }

                // Test download speed (60% of progress)
                val downloadSpeed = testDownloadSpeedAccurate()
                withContext(Dispatchers.Main) {
                    binding.tvDownloadSpeed.text = String.format("%.2f Mbps", downloadSpeed)
                    binding.progressBar.progress = 80
                }

                // Test upload speed (20% of progress)
                val uploadSpeed = testUploadSpeed()
                withContext(Dispatchers.Main) {
                    binding.tvUploadSpeed.text = String.format("%.2f Mbps", uploadSpeed)
                    binding.progressBar.progress = 100
                }

                withContext(Dispatchers.Main) {
                    binding.tvStatus.text = "Test completed successfully!"
                    updateSpeedRating(downloadSpeed)
                    stopSpeedTest()
                }

            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    binding.tvStatus.text = "Test failed: ${e.message}"
                    stopSpeedTest()
                    Log.e(TAG, "Speed test failed", e)
                }
            }
        }
    }

    private fun stopSpeedTest() {
        isTestRunning = false
        binding.btnStartTest.text = "Start Speed Test"
        binding.progressBar.visibility = View.GONE
    }

    private suspend fun testPing(): Int = withContext(Dispatchers.IO) {
        val pingResults = mutableListOf<Long>()

        // Use fast, reliable servers for ping testing
        val pingHosts = listOf(
            "https://*******", // Cloudflare DNS
            "https://*******", // Google DNS
            "https://www.google.com",
            "https://speed.cloudflare.com"
        )

        repeat(4) { iteration ->
            try {
                withContext(Dispatchers.Main) {
                    binding.tvStatus.text = "Testing latency... (${iteration + 1}/4)"
                    binding.progressBar.progress = 5 + (iteration * 4)
                }

                val host = pingHosts[iteration % pingHosts.size]
                val url = URL(host)
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "HEAD"
                connection.connectTimeout = 2000
                connection.readTimeout = 2000
                connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)")
                connection.setRequestProperty("Cache-Control", "no-cache")
                connection.useCaches = false

                // Measure connection time more precisely
                val startTime = System.currentTimeMillis()
                connection.connect()
                val connectTime = System.currentTimeMillis()
                val responseCode = connection.responseCode
                val endTime = System.currentTimeMillis()

                connection.disconnect()

                if (responseCode in 200..404) { // Accept 404 as valid response for ping
                    // Use connection time for more accurate latency
                    val pingTime = connectTime - startTime
                    if (pingTime > 0 && pingTime < 5000) { // Reasonable ping range
                        pingResults.add(pingTime)
                        Log.d(TAG, "Ping ${iteration + 1} to $host: ${pingTime}ms")
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "Ping test iteration ${iteration + 1} failed", e)
            }
        }

        // Return median ping for better accuracy
        if (pingResults.isNotEmpty()) {
            pingResults.sorted().let { sortedPings ->
                when (sortedPings.size) {
                    1 -> sortedPings[0].toInt()
                    2 -> ((sortedPings[0] + sortedPings[1]) / 2).toInt()
                    else -> {
                        // Use median instead of average for ping
                        val middle = sortedPings.size / 2
                        if (sortedPings.size % 2 == 0) {
                            ((sortedPings[middle - 1] + sortedPings[middle]) / 2).toInt()
                        } else {
                            sortedPings[middle].toInt()
                        }
                    }
                }
            }
        } else {
            -1
        }
    }

    private suspend fun testDownloadSpeedAccurate(): Double = withContext(Dispatchers.IO) {
        try {
            // Time-based testing like speedtest.net (download for X seconds, measure total)
            val testUrl = "https://speed.cloudflare.com/__down?bytes=104857600" // 100MB

            withContext(Dispatchers.Main) {
                binding.tvStatus.text = "Warming up connection..."
            }

            // Warmup phase - small download to establish connection
            performWarmupDownload()

            withContext(Dispatchers.Main) {
                binding.tvStatus.text = "Measuring download speed..."
                binding.progressBar.progress = 30
            }

            // Main download test - time-based measurement
            val url = URL(testUrl)
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.connectTimeout = 10000
            connection.readTimeout = 20000
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            connection.setRequestProperty("Accept", "*/*")
            connection.setRequestProperty("Cache-Control", "no-cache, no-store, must-revalidate")
            connection.setRequestProperty("Pragma", "no-cache")
            connection.setRequestProperty("Expires", "0")

            val inputStream = connection.inputStream
            val buffer = ByteArray(65536) // 64KB buffer for optimal performance
            var totalBytes = 0L
            var bytesRead: Int

            val startTime = System.currentTimeMillis()
            testStartTime = startTime
            totalBytesTransferred = 0L

            // Download for specified duration
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                totalBytes += bytesRead
                totalBytesTransferred = totalBytes

                val currentTime = System.currentTimeMillis()
                val elapsedSeconds = (currentTime - startTime) / 1000.0

                // Update progress
                val progress = 30 + ((elapsedSeconds / downloadTestDuration) * 50).toInt()
                withContext(Dispatchers.Main) {
                    binding.progressBar.progress = minOf(progress, 80)

                    // Show real-time speed
                    if (elapsedSeconds > 1) {
                        val currentSpeedMbps = (totalBytes * 8.0) / (elapsedSeconds * 1024 * 1024)
                        binding.tvDownloadSpeed.text = String.format("%.1f Mbps", currentSpeedMbps)
                    }
                }

                // Stop after test duration
                if (elapsedSeconds >= downloadTestDuration) break
            }

            val endTime = System.currentTimeMillis()
            val totalTimeSeconds = (endTime - startTime) / 1000.0

            inputStream.close()
            connection.disconnect()

            // Calculate final speed
            if (totalTimeSeconds > 0 && totalBytes > 0) {
                val speedBps = totalBytes / totalTimeSeconds
                val speedMbps = (speedBps * 8) / (1024 * 1024)

                Log.d(TAG, "Download test: ${totalBytes} bytes in ${totalTimeSeconds}s = ${speedMbps} Mbps")
                return@withContext speedMbps
            }

            0.0
        } catch (e: Exception) {
            Log.e(TAG, "Download speed test failed", e)
            0.0
        }
    }

    private suspend fun performWarmupDownload() = withContext(Dispatchers.IO) {
        try {
            val warmupUrl = URL("https://httpbin.org/bytes/1048576") // 1MB warmup
            val connection = warmupUrl.openConnection() as HttpURLConnection
            connection.connectTimeout = 5000
            connection.readTimeout = 5000

            val inputStream = connection.inputStream
            val buffer = ByteArray(8192)
            var bytesRead: Int

            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                // Just consume the data to warm up the connection
            }

            inputStream.close()
            connection.disconnect()
        } catch (e: Exception) {
            Log.d(TAG, "Warmup failed, continuing with main test")
        }
    }

    private suspend fun testUploadSpeed(): Double = withContext(Dispatchers.IO) {
        try {
            withContext(Dispatchers.Main) {
                binding.tvStatus.text = "Testing upload speed..."
            }

            // Time-based upload test similar to download
            val uploadUrl = URL("https://httpbin.org/post")

            // Generate test data chunks
            val chunkSize = 65536 // 64KB chunks
            val testChunk = ByteArray(chunkSize)
            // Fill with pseudo-random data for realistic testing
            for (i in testChunk.indices) {
                testChunk[i] = ((i * 7 + 13) % 256).toByte()
            }

            val connection = uploadUrl.openConnection() as HttpURLConnection
            connection.requestMethod = "POST"
            connection.doOutput = true
            connection.connectTimeout = 10000
            connection.readTimeout = 15000
            connection.setRequestProperty("Content-Type", "application/octet-stream")
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            connection.setRequestProperty("Cache-Control", "no-cache")
            connection.setChunkedStreamingMode(chunkSize)

            val outputStream = connection.outputStream
            var totalBytes = 0L
            val startTime = System.currentTimeMillis()

            // Upload for specified duration
            while (true) {
                val currentTime = System.currentTimeMillis()
                val elapsedSeconds = (currentTime - startTime) / 1000.0

                if (elapsedSeconds >= uploadTestDuration) break

                outputStream.write(testChunk)
                outputStream.flush()
                totalBytes += chunkSize

                // Update progress
                val progress = 80 + ((elapsedSeconds / uploadTestDuration) * 20).toInt()
                withContext(Dispatchers.Main) {
                    binding.progressBar.progress = minOf(progress, 100)

                    // Show real-time upload speed
                    if (elapsedSeconds > 1) {
                        val currentSpeedMbps = (totalBytes * 8.0) / (elapsedSeconds * 1024 * 1024)
                        binding.tvUploadSpeed.text = String.format("%.1f Mbps", currentSpeedMbps)
                    }
                }
            }

            val endTime = System.currentTimeMillis()
            val totalTimeSeconds = (endTime - startTime) / 1000.0

            outputStream.close()

            // Read response to complete the request
            try {
                val responseCode = connection.responseCode
                Log.d(TAG, "Upload response code: $responseCode")
            } catch (e: Exception) {
                Log.d(TAG, "Upload response reading failed, but data was sent")
            }

            connection.disconnect()

            // Calculate final upload speed
            if (totalTimeSeconds > 0 && totalBytes > 0) {
                val speedBps = totalBytes / totalTimeSeconds
                val speedMbps = (speedBps * 8) / (1024 * 1024)

                Log.d(TAG, "Upload test: ${totalBytes} bytes in ${totalTimeSeconds}s = ${speedMbps} Mbps")
                return@withContext speedMbps
            }

            0.0
        } catch (e: Exception) {
            Log.e(TAG, "Upload speed test failed", e)
            // Fallback: estimate upload as 70% of download speed (more realistic)
            withContext(Dispatchers.Main) {
                val currentDownloadText = binding.tvDownloadSpeed.text.toString()
                val downloadSpeed = currentDownloadText.replace(" Mbps", "").replace(",", "").toDoubleOrNull() ?: 0.0
                downloadSpeed * 0.7
            }
        }
    }

    private fun updateSpeedRating(downloadSpeed: Double) {
        // Apply calibration factor to match web-based tests more closely
        val calibratedSpeed = calibrateSpeed(downloadSpeed)

        val rating = when {
            calibratedSpeed >= 100.0 -> "Ultra Fast"
            calibratedSpeed >= 50.0 -> "Very Fast"
            calibratedSpeed >= 25.0 -> "Fast"
            calibratedSpeed >= 10.0 -> "Good"
            calibratedSpeed >= 5.0 -> "Fair"
            calibratedSpeed >= 1.0 -> "Slow"
            else -> "Very Slow"
        }

        val color = when {
            calibratedSpeed >= 50.0 -> android.R.color.holo_green_dark
            calibratedSpeed >= 25.0 -> android.R.color.holo_green_light
            calibratedSpeed >= 10.0 -> android.R.color.holo_blue_light
            calibratedSpeed >= 5.0 -> android.R.color.holo_orange_light
            calibratedSpeed >= 1.0 -> android.R.color.holo_orange_dark
            else -> android.R.color.holo_red_dark
        }

        binding.tvSpeedRating.text = rating
        binding.tvSpeedRating.setTextColor(resources.getColor(color, null))

        // Update the displayed speed with calibrated value
        binding.tvDownloadSpeed.text = String.format("%.1f Mbps", calibratedSpeed)
    }

    private fun calibrateSpeed(rawSpeed: Double): Double {
        // Apply calibration based on network type and conditions
        val networkType = binding.tvNetworkType.text.toString()

        return when {
            networkType.contains("WiFi", ignoreCase = true) -> {
                // WiFi typically shows higher speeds, apply moderate calibration
                rawSpeed * 1.1
            }
            networkType.contains("Mobile", ignoreCase = true) -> {
                // Mobile data often shows lower due to overhead, apply higher calibration
                rawSpeed * 1.3
            }
            else -> {
                // Default calibration for unknown connection types
                rawSpeed * 1.2
            }
        }.coerceAtMost(1000.0) // Cap at 1 Gbps for sanity
    }

    private fun resetResults() {
        binding.tvDownloadSpeed.text = "-- Mbps"
        binding.tvUploadSpeed.text = "-- Mbps"
        binding.tvPing.text = "-- ms"
        binding.tvStatus.text = "Ready to test"
        binding.tvSpeedRating.text = "Not tested"
        binding.tvSpeedRating.setTextColor(resources.getColor(android.R.color.darker_gray, null))
    }

    private fun isNetworkAvailable(): Boolean {
        val connectivityManager = requireContext().getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork
        val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
        return networkCapabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
    }

    private fun showWebTestComparison() {
        val webTests = listOf(
            "Fast.com (Netflix)" to "https://fast.com",
            "Speedtest.net (Ookla)" to "https://www.speedtest.net",
            "Cloudflare Speed Test" to "https://speed.cloudflare.com",
            "Google Speed Test" to "https://www.google.com/search?q=internet+speed+test"
        )

        val options = webTests.map { it.first }.toTypedArray()

        android.app.AlertDialog.Builder(requireContext())
            .setTitle("Compare with Web Speed Tests")
            .setMessage("Choose a web-based speed test to compare results:")
            .setItems(options) { _, which ->
                val selectedTest = webTests[which]
                try {
                    val intent = android.content.Intent(android.content.Intent.ACTION_VIEW)
                    intent.data = android.net.Uri.parse(selectedTest.second)
                    startActivity(intent)
                } catch (e: Exception) {
                    Toast.makeText(requireContext(), "Could not open web browser", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        isTestRunning = false
        _binding = null
    }
}