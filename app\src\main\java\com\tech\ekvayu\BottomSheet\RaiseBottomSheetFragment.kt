package com.tech.ekvayu.BottomSheet

import android.os.Bundle
import android.text.Editable
import android.text.Html
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.CommonUtil
import com.tech.ekvayu.BaseClass.CommonUtil.showSnackbar
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.Dispute.DisputeRaiseRequest
import com.tech.ekvayu.Dispute.DisputeRaiseResponse
import com.tech.ekvayu.Dispute.Results
import com.tech.ekvayu.R
import com.tech.ekvayu.Request.PendingMailRequest
import com.tech.ekvayu.Response.PedingMailRes
import com.tech.ekvayu.databinding.FragmentRaiseBottomSheetBinding
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response


class RaiseBottomSheetFragment(item: Results) : BottomSheetDialogFragment() {
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    private lateinit var binding: FragmentRaiseBottomSheetBinding
    private val resultItem=item

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        binding=FragmentRaiseBottomSheetBinding.inflate(inflater, container, false)

        binding.etReason.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (!s.isNullOrBlank()) {
                    val reason = binding.etReason.text.toString().trim()
                    val wordCount = reason.split("\\s+".toRegex()).filter { it.isNotEmpty() }.size

                    if (wordCount < 5) {
                        binding.btSubmit.background = ContextCompat.getDrawable(requireContext(), R.drawable.bg_button_grey_color)
                    }
                    else
                    {
                        binding.btSubmit.background = ContextCompat.getDrawable(requireContext(), R.drawable.bg_button_app_color)
                    }
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        binding.btSubmit.setOnClickListener {
            validation()
        }


        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        Log.d("getItemData", "onViewCreated: "+resultItem.msgId)

        binding.etMessageId.setText(resultItem.msgId)
        binding.etSenderMail.setText(resultItem.sendersEmail)
        binding.etStatus.setText(resultItem.status)
        val disputeCounter = resultItem.counter.toString()
        val htmlText = "<font color='#FF0000'>$disputeCounter</font> Of 3"
        binding.etCounter.setText(Html.fromHtml(htmlText, Html.FROM_HTML_MODE_LEGACY))


      //  setFormData()
    }

    private fun setFormData() = if (CommonUtil.isNetworkAvailable(requireContext())) {

        val retrofit = ApiClient.getRetrofitInstance(requireContext())
        val apiService = retrofit!!.create(ApiService::class.java)
        val request = PendingMailRequest(email = sharedPrefManager.getString(AppConstant.receiverMail,""), hashId = sharedPrefManager.getString(AppConstant.hashId,""))

        CommonUtil.showProgressDialog(requireContext(), "Please wait...")
        apiService.pendingMailStatusAi(request)
            .enqueue(object : Callback<PedingMailRes> {
                override fun onResponse(
                    call: Call<PedingMailRes>,
                    response: Response<PedingMailRes>
                ) {
                    CommonUtil.hideProgressDialog()
                    if (response.body()!!.data!!.code ==1)
                    {

                        val disputeCounter = resultItem.counter.toString()
                        val htmlText = "<font color='#FF0000'>$disputeCounter</font> Of 3"
                        binding.etCounter.setText(Html.fromHtml(htmlText, Html.FROM_HTML_MODE_LEGACY))

                      /*  binding.etMessageId.setText(response.body()!!.data!!.hashId)
                        binding.etSenderMail.setText(response.body()!!.data!!.email)
                        binding.etStatus.setText(response.body()!!.data!!.emlStatus)
                        val disputeCounter = response.body()?.data?.disputeCounter.toString()
                        val htmlText = "<font color='#FF0000'>$disputeCounter</font> Of 3"
                        binding.etCounter.setText(Html.fromHtml(htmlText, Html.FROM_HTML_MODE_LEGACY))*/

                    }
                }
                override fun onFailure(call: Call<PedingMailRes?>, t: Throwable) {
                    CommonUtil.hideProgressDialog()
                    showSnackbar(binding.clMain,"Failed to fetch data. Please try again.")
                }
            })
    } else {
        CommonUtil.hideProgressDialog()
        showSnackbar(binding.clMain,"No internet connection. Please check your network.")
    }


    private fun validation() {
        val reason = binding.etReason.text.toString().trim()
        val wordCount = reason.split("\\s+".toRegex()).filter { it.isNotEmpty() }.size

        if (reason.isBlank())
        {
            showSnackbar(binding.clMain,"Please enter reason")
        }
        else if (wordCount < 5) {
            showSnackbar(binding.clMain,"Please enter at least 2–5 word")
        }
        else {
            submitForm()
        }
    }


    private fun submitForm() {
        val retrofit = ApiClient.getRetrofitInstance(requireContext())
        if (retrofit == null) {
            return
        }

        val apiService = retrofit.create(ApiService::class.java)
        val request = DisputeRaiseRequest(email = sharedPrefManager.getString(AppConstant.receiverMail,""), hashId = sharedPrefManager.getString(
            AppConstant.hashId,""), userComment =binding.etReason.text.toString())

        CommonUtil.showProgressDialog(requireContext(), "Please wait...")
        apiService.disputeRaise(request)
            .enqueue(object : Callback<DisputeRaiseResponse> {
                override fun onResponse(call: Call<DisputeRaiseResponse?>, response: Response<DisputeRaiseResponse?>) {
                    CommonUtil.hideProgressDialog()
                    Log.d("getResponseMessage", "onResponse: "+response.message())
                    if (response.body()!!.code==1) {
                        showSnackbar(binding.clMain,"Dispute Raised Successfully & Id No. is ${response.body()!!.disputeInfoId}")
                        binding.btSubmit.background = ContextCompat.getDrawable(requireContext(), R.drawable.bg_button_grey_color)
                        binding.btSubmit.isEnabled=false
                        binding.etReason.setText("")
                        setFormData()
                    }
                    else
                    {
                        showSnackbar(binding.clMain, response.body()!!.message.toString())
                    }
                }

                override fun onFailure(call: Call<DisputeRaiseResponse?>, t: Throwable) {
                    CommonUtil.hideProgressDialog()
                }

            })
    }

    override fun onDestroyView() {
        super.onDestroyView()

    }


}