package com.tech.ekvayu.BottomSheet

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.CommonUtil.hideProgressDialog
import com.tech.ekvayu.BaseClass.CommonUtil.showProgressDialog
import com.tech.ekvayu.BaseClass.CommonUtil.showSnackbar
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.R
import com.tech.ekvayu.databinding.FragmentLoginBottomSheetBinding


class LoginBottomSheetFragment : BottomSheetDialogFragment() {
    private lateinit var binding: FragmentLoginBottomSheetBinding
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding=FragmentLoginBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.btSubmit.setOnClickListener {
            setValidation()
        }
    }

    private fun setValidation()
    {
       if(  binding.userId.text.toString().isEmpty())
        {
            binding.userId.error="Please enter user id"
        }
       else if (binding.userPass.text.toString().isEmpty())
        {
            binding.userPass.error="Please enter password"
        }
        else
       {
           setAuth(binding.userId.text.toString(),binding.userPass.text.toString())
       }
    }

    fun setAuth(userId: String, userPass: String) {
        sharedPrefManager.putString(AppConstant.userId,userId)
        showSnackbar(binding.clUserId,"Login Successfully")


    }


}