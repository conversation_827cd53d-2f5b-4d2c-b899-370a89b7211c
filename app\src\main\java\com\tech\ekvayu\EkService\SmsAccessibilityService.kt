package com.tech.ekvayu.EkService


import java.util.*
import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Toast
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.BaseClass.SmsDetectionHelper
import com.tech.ekvayu.R
import com.tech.ekvayu.Sms.AiResponse.CheckSmsAiRequest
import com.tech.ekvayu.Sms.AiResponse.CheckSmsAiResponse
import com.tech.ekvayu.Sms.CheckMessage.SmsCheckReponse
import com.tech.ekvayu.Sms.CheckMessage.SmsCheckRequest
import com.tech.ekvayu.Sms.HashId.GetHashRequest
import com.tech.ekvayu.Sms.HashId.GetHashResponse
import com.tech.ekvayu.databinding.LayoutProgressBinding
import com.tech.ekvayu.databinding.LayoutValidationBinding
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.util.regex.Pattern



class SmsAccessibilityService : AccessibilityService() {

    companion object {
        private const val TAG = "SmsAccessibilityService"
        private val SMS_APPS = setOf(
            "com.google.android.apps.messaging",
            "com.samsung.android.messaging",
            "com.android.mms",
            "com.textra",
            "com.chomp.android.sms",
            "com.microsoft.android.sms"
        )
    }

    private var currentPopupView: View? = null
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    private val handler = Handler(Looper.getMainLooper())

    // To prevent duplicate processing
    private val processedMessages = mutableSetOf<String>()
    private var lastProcessTime = 0L
    private var lastCleanupTime = 0L
    private val CLEANUP_INTERVAL = 5 * 60 * 1000L // 5 minutes
    private val MAX_PROCESSED_MESSAGES = 1000 // Limit memory usage
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null) return

        val packageName = event.packageName?.toString() ?: return

        // Only monitor SMS apps
        if (!SMS_APPS.contains(packageName)) return

        // Log all events for debugging
        logAccessibilityEvent(event, packageName)

        // Throttle events to prevent spam processing
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastProcessTime < 2000) { // 2 second throttle (increased)
            Log.d(TAG, "⏱️ Throttling event - too soon after last processing")
            return
        }
        lastProcessTime = currentTime

        when (event.eventType) {
            AccessibilityEvent.TYPE_VIEW_CLICKED -> {
                Log.d(TAG, "🔍 SMS Click detected in $packageName")
                handler.postDelayed({
                    try {
                        val rootNode = rootInActiveWindow
                        if (rootNode != null) {
                            handleSmsClick(rootNode, packageName, event)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error processing click event: ${e.message}", e)
                    }
                }, 500)
            }

            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                Log.d(TAG, "📱 Content changed in $packageName")
                handler.postDelayed({
                    try {
                        val rootNode = rootInActiveWindow
                        if (rootNode != null) {
                            handleContentChange(rootNode, packageName)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error processing content change: ${e.message}", e)



                    }
                }, 800)
            }

            AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED -> {
                Log.d(TAG, "📝 Text changed in $packageName")
                // Handle text changes for real-time typing detection
                handler.postDelayed({
                    try {
                        val rootNode = rootInActiveWindow
                        if (rootNode != null) {
                            extractSmsContent(rootNode, packageName)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error processing text change: ${e.message}", e)
                    }
                }, 1000)
            }
        }
    }

    private fun logAccessibilityEvent(event: AccessibilityEvent, packageName: String) {
        val eventTypeString = when (event.eventType) {
            AccessibilityEvent.TYPE_VIEW_CLICKED -> "VIEW_CLICKED"
            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> "WINDOW_CONTENT_CHANGED"
            AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED -> "VIEW_TEXT_CHANGED"
            else -> "OTHER(${event.eventType})"
        }

        Log.d(TAG, """
            📱 ===== SMS ACCESSIBILITY EVENT =====
            🔸 Event Type: $eventTypeString
            🔸 Package: $packageName
            🔸 Event Text: ${event.text}
            🔸 Source Text: ${event.source?.text}
            🔸 Source ViewId: ${event.source?.viewIdResourceName}
            🔸 Source Clickable: ${event.source?.isClickable}
            ====================================
        """.trimIndent())

        getHashID(event.source?.text.toString(),"",getDeviceId(applicationContext))
    }


    private fun handleSmsClick(rootNode: AccessibilityNodeInfo, packageName: String, event: AccessibilityEvent) {
        Log.d(TAG, "🎯 Processing SMS click for $packageName")

        // Check if this is a conversation/message click
        val clickedElement = event.source
        val clickedText = clickedElement?.text?.toString() ?: ""
        val clickedViewId = clickedElement?.viewIdResourceName ?: ""

        Log.d(TAG, "🔍 Clicked element: text='$clickedText', viewId='$clickedViewId'")

        // Determine if this is a conversation click or message click
        val isConversationClick = isConversationClick(clickedText, clickedViewId, packageName)
        val isMessageClick = isMessageClick(clickedText, clickedViewId, packageName)

        if (isConversationClick) {
            Log.d(TAG, "💬 Conversation clicked - extracting conversation data")
            extractConversationData(rootNode, packageName)
        } else if (isMessageClick) {
            Log.d(TAG, "📨 Message clicked - extracting message data")
            extractMessageData(rootNode, packageName)
        } else {
            Log.d(TAG, "📱 General SMS area clicked - extracting all visible content")
            extractAllVisibleSmsContent(rootNode, packageName)
        }
    }

    private fun handleContentChange(rootNode: AccessibilityNodeInfo, packageName: String) {
        Log.d(TAG, "📱 Processing content change for $packageName")

        // Check if we're in a conversation view
        if (isInConversationView(rootNode, packageName)) {
            Log.d(TAG, "💬 In conversation view - extracting messages")
            extractConversationData(rootNode, packageName)
        } else if (isInInboxView(rootNode, packageName)) {
            Log.d(TAG, "📥 In inbox view - extracting inbox content")
            extractInboxData(rootNode, packageName)
        }
    }
    
    override fun onInterrupt() {
        Log.d(TAG, "SMS Accessibility Service interrupted")
    }
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        Log.d(TAG, "SMS Accessibility Service connected")
        Toast.makeText(this, "SMS Security Monitor Active", Toast.LENGTH_SHORT).show()
    }

    private fun isConversationClick(clickedText: String, clickedViewId: String, packageName: String): Boolean {
        return when (packageName) {
            "com.google.android.apps.messaging" -> {
                clickedViewId.contains("conversation", true) ||
                clickedViewId.contains("contact", true) ||
                clickedText.matches(Regex("^[+]?[0-9\\s\\-()]+$")) || // Phone number
                clickedText.contains("@") // Email
            }
            "com.samsung.android.messaging" -> {
                clickedViewId.contains("conversation", true) ||
                clickedViewId.contains("thread", true)
            }
            else -> {
                clickedViewId.contains("conversation", true) ||
                clickedViewId.contains("thread", true) ||
                clickedViewId.contains("contact", true)
            }
        }
    }

    private fun isMessageClick(clickedText: String, clickedViewId: String, packageName: String): Boolean {
        return when (packageName) {
            "com.google.android.apps.messaging" -> {
                clickedViewId.contains("message", true) ||
                clickedViewId.contains("bubble", true) ||
                (clickedText.length > 10 && !clickedText.matches(Regex("^[+]?[0-9\\s\\-()]+$")))
            }
            "com.samsung.android.messaging" -> {
                clickedViewId.contains("message", true) ||
                clickedViewId.contains("text", true)
            }
            else -> {
                clickedViewId.contains("message", true) ||
                clickedViewId.contains("text", true) ||
                (clickedText.length > 10 && !clickedText.matches(Regex("^[0-9:\\s]+$")))
            }
        }
    }

    private fun isInConversationView(rootNode: AccessibilityNodeInfo, packageName: String): Boolean {
        val allNodes = getAllNodes(rootNode)
        return allNodes.any { node ->
            val viewId = node.viewIdResourceName ?: ""
            val text = node.text?.toString() ?: ""

            when (packageName) {
                "com.google.android.apps.messaging" -> {
                    viewId.contains("conversation_recyclerview", true) ||
                    viewId.contains("message_list", true) ||
                    text.contains("Type a message", true)
                }
                "com.samsung.android.messaging" -> {
                    viewId.contains("conversation", true) ||
                    viewId.contains("message_list", true)
                }
                else -> {
                    viewId.contains("conversation", true) ||
                    viewId.contains("message", true) ||
                    text.contains("Type", true)
                }
            }
        }
    }

    private fun isInInboxView(rootNode: AccessibilityNodeInfo, packageName: String): Boolean {
        val allNodes = getAllNodes(rootNode)
        return allNodes.any { node ->
            val viewId = node.viewIdResourceName ?: ""
            val text = node.text?.toString() ?: ""

            when (packageName) {
                "com.google.android.apps.messaging" -> {
                    viewId.contains("conversation_list", true) ||
                    viewId.contains("home_recyclerview", true) ||
                    text.contains("Search conversations", true)
                }
                "com.samsung.android.messaging" -> {
                    viewId.contains("conversation_list", true) ||
                    text.contains("Messages", true)
                }
                else -> {
                    viewId.contains("list", true) ||
                    text.contains("Messages", true) ||
                    text.contains("Inbox", true)
                }
            }
        }
    }

    private fun extractConversationData(rootNode: AccessibilityNodeInfo, packageName: String) {
        Log.d(TAG, "💬 Extracting conversation data for $packageName")

        val conversationData = when (packageName) {
            "com.google.android.apps.messaging" -> extractGoogleConversationData(rootNode)
            "com.samsung.android.messaging" -> extractSamsungConversationData(rootNode)
            else -> extractGenericConversationData(rootNode)
        }

        if (conversationData.isNotEmpty()) {
            Log.d(TAG, "✅ Found ${conversationData.size} messages in conversation")
            conversationData.forEach { messageData ->
                processSmsMessage(
                    sender = messageData["sender"] ?: "Unknown",
                    message = messageData["content"] ?: "",
                    timestamp = messageData["timestamp"]?.toLongOrNull() ?: System.currentTimeMillis(),
                    messageType = messageData["type"] ?: "received",
                    clickedNode = rootNode
                )
            }
        } else {
            Log.w(TAG, "⚠️ No conversation data found")
        }
    }

    private fun extractMessageData(rootNode: AccessibilityNodeInfo, packageName: String) {
        Log.d(TAG, "📨 Extracting message data for $packageName")

        val messageData = when (packageName) {
            "com.google.android.apps.messaging" -> extractGoogleMessageData(rootNode)
            "com.samsung.android.messaging" -> extractSamsungMessageData(rootNode)
            else -> extractGenericMessageData(rootNode)
        }

        if (messageData.isNotEmpty()) {
            Log.d(TAG, "✅ Found message data")
            processSmsMessage(
                sender = messageData["sender"] ?: "Unknown",
                message = messageData["content"] ?: "",
                timestamp = messageData["timestamp"]?.toLongOrNull() ?: System.currentTimeMillis(),
                messageType = messageData["type"] ?: "received",
                clickedNode = rootNode
            )
        } else {
            Log.w(TAG, "⚠️ No message data found")
        }
    }

    private fun extractSmsContent(rootNode: AccessibilityNodeInfo, packageName: String) {
        try {
            when (packageName) {
                "com.google.android.apps.messaging" -> extractGoogleMessagesContent(rootNode)
                "com.samsung.android.messaging" -> extractSamsungMessagesContent(rootNode)
                "com.android.mms" -> extractDefaultMessagesContent(rootNode)
                else -> extractGenericSmsContent(rootNode)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting SMS content: ${e.message}", e)
        }
    }

    private fun extractAllVisibleSmsContent(rootNode: AccessibilityNodeInfo, packageName: String) {
        Log.d(TAG, "📱 Extracting all visible SMS content for $packageName")

        // Dump all screen content for debugging
        dumpAllScreenContent(rootNode)

        // Try to extract any visible messages
        val allNodes = getAllNodes(rootNode)
        val extractedMessages = mutableListOf<Map<String, String>>()

        allNodes.forEach { node ->
            val text = node.text?.toString()?.trim() ?: ""
            val viewId = node.viewIdResourceName ?: ""

            if (text.isNotEmpty() && isLikelyMessage(text)) {
                val messageData = mutableMapOf<String, String>()
                messageData["content"] = text
                messageData["sender"] = extractSenderFromContext(node, allNodes)
                messageData["timestamp"] = extractTimestampFromContext(node, allNodes)
                messageData["type"] = "unknown"

                extractedMessages.add(messageData)
                Log.d(TAG, "📨 Found message: ${text.take(50)}... from ${messageData["sender"]} at ${messageData["timestamp"]}")
            }
        }

        // Process all found messages
        extractedMessages.forEach { messageData ->
            processSmsMessage(
                sender = messageData["sender"] ?: "Unknown",
                message = messageData["content"] ?: "",
                timestamp = messageData["timestamp"]?.toLongOrNull() ?: System.currentTimeMillis(),
                messageType = messageData["type"] ?: "unknown",
                clickedNode = rootNode // Pass the root node for ID generation
            )
        }
    }

    private fun dumpAllScreenContent(rootNode: AccessibilityNodeInfo) {
        val allNodes = getAllNodes(rootNode)

        Log.d(TAG, "🔍 ===== DUMPING ALL SMS SCREEN CONTENT =====")
        Log.d(TAG, "Total nodes found: ${allNodes.size}")

        allNodes.forEachIndexed { index, node ->
            val text = node.text?.toString()?.trim() ?: ""
            val viewId = node.viewIdResourceName ?: ""
            val contentDesc = node.contentDescription?.toString() ?: ""
            val className = node.className?.toString() ?: ""

            if (text.isNotEmpty() || viewId.isNotEmpty()) {
                Log.d(TAG, """
                    📱 Node $index:
                       Text: '$text'
                       ViewId: '$viewId'
                       ContentDesc: '$contentDesc'
                       ClassName: '$className'
                       Clickable: ${node.isClickable}
                    ---
                """.trimIndent())
            }
        }
        Log.d(TAG, "🔍 ===== END SMS SCREEN CONTENT DUMP =====")
    }

    private fun extractGoogleConversationData(rootNode: AccessibilityNodeInfo): List<Map<String, String>> {
        val messages = mutableListOf<Map<String, String>>()
        val allNodes = getAllNodes(rootNode)

        // Look for message bubbles and conversation elements
        allNodes.forEach { node ->
            val text = node.text?.toString()?.trim() ?: ""
            val viewId = node.viewIdResourceName ?: ""

            // Google Messages specific patterns
            if ((viewId.contains("message", true) || viewId.contains("bubble", true)) &&
                text.isNotEmpty() && isLikelyMessage(text)) {

                val messageData = mutableMapOf<String, String>()
                messageData["content"] = text
                messageData["sender"] = extractSenderFromGoogleMessages(node, allNodes)
                messageData["timestamp"] = extractTimestampFromContext(node, allNodes)
                messageData["type"] = if (viewId.contains("outgoing", true)) "sent" else "received"

                messages.add(messageData)
                Log.d(TAG, "📨 Google Messages: ${text.take(30)}... from ${messageData["sender"]}")
            }
        }

        return messages
    }

    private fun extractSamsungConversationData(rootNode: AccessibilityNodeInfo): List<Map<String, String>> {
        val messages = mutableListOf<Map<String, String>>()
        val allNodes = getAllNodes(rootNode)

        // Look for Samsung-specific message elements
        allNodes.forEach { node ->
            val text = node.text?.toString()?.trim() ?: ""
            val viewId = node.viewIdResourceName ?: ""

            if ((viewId.contains("message", true) || viewId.contains("text", true)) &&
                text.isNotEmpty() && isLikelyMessage(text)) {

                val messageData = mutableMapOf<String, String>()
                messageData["content"] = text
                messageData["sender"] = extractSenderFromSamsungMessages(node, allNodes)
                messageData["timestamp"] = extractTimestampFromContext(node, allNodes)
                messageData["type"] = if (viewId.contains("sent", true)) "sent" else "received"

                messages.add(messageData)
                Log.d(TAG, "📨 Samsung Messages: ${text.take(30)}... from ${messageData["sender"]}")
            }
        }

        return messages
    }

    private fun extractGenericConversationData(rootNode: AccessibilityNodeInfo): List<Map<String, String>> {
        val messages = mutableListOf<Map<String, String>>()
        val allNodes = getAllNodes(rootNode)

        allNodes.forEach { node ->
            val text = node.text?.toString()?.trim() ?: ""

            if (text.isNotEmpty() && isLikelyMessage(text)) {
                val messageData = mutableMapOf<String, String>()
                messageData["content"] = text
                messageData["sender"] = extractSenderFromContext(node, allNodes)
                messageData["timestamp"] = extractTimestampFromContext(node, allNodes)
                messageData["type"] = "unknown"

                messages.add(messageData)
                Log.d(TAG, "📨 Generic: ${text.take(30)}... from ${messageData["sender"]} at ${formatTimestamp(messageData["timestamp"]?.toLongOrNull())}")
            }
        }

        return messages
    }

    private fun extractGoogleMessagesContent(rootNode: AccessibilityNodeInfo) {
        val messageNodes = findNodesByText(rootNode, "")
        val conversationNodes = findNodesByClassName(rootNode, "android.widget.TextView")

        conversationNodes.forEach { node ->
            val text = node.text?.toString()
            if (!text.isNullOrEmpty() && text.length > 10) {
                // Try to extract sender and message
                val senderPattern = Pattern.compile("^([^:]+):")
                val matcher = senderPattern.matcher(text)

                if (matcher.find()) {
                    val sender = matcher.group(1) ?: "Unknown"
                    val message = text.substring(matcher.end()).trim()

                    if (message.isNotEmpty()) {
                        processSmsMessage(sender, message, System.currentTimeMillis(), "received", node)
                    }
                } else if (isLikelyMessage(text)) {
                    processSmsMessage("Unknown", text, System.currentTimeMillis(), "received", node)
                }
            }
        }
    }
    
    private fun extractSamsungMessagesContent(rootNode: AccessibilityNodeInfo) {
        // Samsung Messages specific extraction
        val messageNodes = findNodesByResourceId(rootNode, "message_text")
        val senderNodes = findNodesByResourceId(rootNode, "sender_name")
        
        if (messageNodes.isNotEmpty() && senderNodes.isNotEmpty()) {
            for (i in messageNodes.indices) {
                val message = messageNodes.getOrNull(i)?.text?.toString()
                val sender = senderNodes.getOrNull(i)?.text?.toString() ?: "Unknown"
                
                if (!message.isNullOrEmpty()) {
                    processSmsMessage(sender, message, System.currentTimeMillis(), "received", null)
                }
            }
        } else {
            extractGenericSmsContent(rootNode)
        }
    }
    
    private fun extractDefaultMessagesContent(rootNode: AccessibilityNodeInfo) {
        // Default Android Messages extraction
        extractGenericSmsContent(rootNode)
    }
    
    private fun extractGenericSmsContent(rootNode: AccessibilityNodeInfo) {
        val textNodes = findNodesByClassName(rootNode, "android.widget.TextView")
        
        textNodes.forEach { node ->
            val text = node.text?.toString()
            if (!text.isNullOrEmpty() && isLikelyMessage(text)) {
                processSmsMessage("Unknown", text, System.currentTimeMillis(), "received", node)
            }
        }
    }

    private fun processSmsMessage(sender: String, message: String, timestamp: Long = System.currentTimeMillis(), messageType: String = "received", clickedNode: AccessibilityNodeInfo? = null) {
        // Extract the real SMS node ID (this should be consistent for the same SMS)
        val realSmsId = extractRealSmsNodeId(clickedNode, sender, message, timestamp)

        // Create multiple unique identifiers for better duplicate detection
        val messageHash = "${sender}_${message.hashCode()}_${message.length}"
        val contentHash = "${sender.trim()}_${message.trim().hashCode()}"
        val timeBasedHash = "${sender}_${message.hashCode()}_${timestamp / 60000}" // Group by minute

        // Check if we've already processed this message using any of the hashes
        if (processedMessages.contains(messageHash) ||
            processedMessages.contains(contentHash) ||
            processedMessages.contains(timeBasedHash)) {
            Log.d(TAG, "📱 Skipping duplicate SMS processing for ID: $realSmsId (Hash: $messageHash)")
            return
        }

        // Add all hashes to prevent future duplicates
        processedMessages.add(messageHash)
        processedMessages.add(contentHash)
        processedMessages.add(timeBasedHash)

        // Cleanup old processed messages periodically to prevent memory leaks
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastCleanupTime > CLEANUP_INTERVAL || processedMessages.size > MAX_PROCESSED_MESSAGES) {
            val sizeBefore = processedMessages.size
            processedMessages.clear() // Simple cleanup - clear all old entries
            lastCleanupTime = currentTime
            Log.d(TAG, "🧹 Cleaned up processed messages cache: $sizeBefore -> ${processedMessages.size}")
        }

        // Add to processed messages (keep only last 50 to prevent memory issues)
        processedMessages.add(messageHash)
        if (processedMessages.size > 50) {
            val iterator = processedMessages.iterator()
            repeat(10) { // Remove oldest 10 entries
                if (iterator.hasNext()) {
                    iterator.next()
                    iterator.remove()
                }
            }
        }

        Log.d(TAG, """
            📱 ===== PROCESSING SMS MESSAGE =====
            🆔 Real SMS Node ID: $realSmsId
            👤 Sender: $sender
            📨 Message: ${message.take(100)}...
            🕒 Timestamp: $timestamp (${formatTimestamp(timestamp)})
            📋 Type: $messageType
            🔗 Node Info: ${getNodeIdentifiers(clickedNode)}
            🔗 Node Hierarchy: ${getNodeHierarchy(clickedNode)}
            ===================================
        """.trimIndent())

      //  Log.d("getMessageDetails", "sender: "+sender+" message "+message+" type "+messageType+" realSmsId: "+realSmsId)

        // Analyze the message
        val analysisResult = SmsDetectionHelper.analyzeSmsContent(
            sender = sender,
            body = message,
            timestamp = timestamp,
            context = this
        )

        // Store the data with real SMS ID
        SmsDetectionHelper.storeSmsData(
            sender = sender,
            body = message,
            timestamp = timestamp,
            analysisResult = analysisResult,
            context = this
        )

       getHashID(message, timestamp.toString(),getDeviceId(applicationContext))
    }

    fun getDeviceId(context: Context): String {
        return Settings.Secure.getString(
            context.contentResolver,
            Settings.Secure.ANDROID_ID
        )
    }


    private fun getHashID(message: String, time: String, androidId: String) {

        if (hasOverlayPermission(applicationContext)) {
            val retrofit = ApiClient.getRetrofitInstance(applicationContext)
            val apiService = retrofit!!.create(ApiService::class.java)
            val windowManager =getSystemService(WINDOW_SERVICE) as WindowManager
            val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as android.view.LayoutInflater
            val binding = LayoutProgressBinding.inflate(layoutInflater)
            val view = binding.root
            view.setBackgroundResource(R.drawable.bg_alert_dialog)
            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                else
                    WindowManager.LayoutParams.TYPE_PHONE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
                android.graphics.PixelFormat.TRANSLUCENT
            )
            params.dimAmount = 0.5f
            windowManager.addView(view, params)

            val request= GetHashRequest()

            request.message= message
            request.deviceId=androidId

            apiService.getSMSHashKey(request)
                .enqueue(object : Callback<GetHashResponse> {
                    override fun onResponse(
                        call: Call<GetHashResponse>,
                        response: Response<GetHashResponse>
                    ) {
                        windowManager.removeView(view)
                        if (response.body()!!.Code.toString()=="1")
                        {
                            windowManager.removeView(view)
                            checkSmsAiResponse(message,response.body()?.hashId.toString(),androidId)
                        }

                        else
                        {
                            windowManager.removeView(view)
                            checkSms(message, time, androidId)
                        }
                    }

                    override fun onFailure(call: Call<GetHashResponse>, t: Throwable) {
                        windowManager.removeView(view)
                    }
                })
        }

        else
        {
            requestOverlayPermission(applicationContext)
        }
    }

    private fun checkSms(message: String, timestamp: String,deviceId: String) {

        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)

        val request= SmsCheckRequest()
        request.message=message
        request.deviceId=deviceId

        apiService.checkSms(request)
            .enqueue(object : Callback<SmsCheckReponse> {
                override fun onResponse(
                    call: Call<SmsCheckReponse>,
                    response: Response<SmsCheckReponse>
                ) {
                    if (response.body()?.Code==1)
                    {
                        checkSmsAiResponse(message,response.body()?.hashId.toString(),deviceId)
                    }
                    else
                    {
                        Toast.makeText(applicationContext, ""+response.body()!!.message, Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<SmsCheckReponse?>, t: Throwable) {

                }

            })
    }

    private fun checkSmsAiResponse(message: String,hashId: String, deviceId: String) {

        val windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater

        val binding = LayoutProgressBinding.inflate(layoutInflater)
        val view = binding.root
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

        // Show loading overlay
        windowManager.addView(view, params)

        val handler = Handler(Looper.getMainLooper())
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)

        var retryCount = 0
        val maxRetries = 10

        // Polling function
        fun pollStatus() {
            if (retryCount >= maxRetries) {
                windowManager.removeView(view)
                Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                return
            }

            val request = CheckSmsAiRequest()
            request.deviceId=deviceId
            request.hashId=hashId

            apiService.getPendingStatuscheckMsg(request)
                .enqueue(object : Callback<CheckSmsAiResponse> {
                    override fun onResponse(call: Call<CheckSmsAiResponse>, response: Response<CheckSmsAiResponse>) {

                        if (response.body()?.data?.msgStatus== "safe" || response.body()?.data?.msgStatus == "unsafe") {
                            windowManager.removeView(view)
                            setResponsePopup(response.body()?.data?.unsafeReasons.toString() , response.body()?.data?.msgStatus.toString(),response.body()?.data?.messageContent.toString())
                        }
                        else if (retryCount==10 && response.body()?.data?.msgStatus == "pending")
                        {
                            windowManager.removeView(view)
                            Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                        }

                        else {
                            retryCount++
                            handler.postDelayed({ pollStatus() }, 3000) // Retry after 3 seconds
                        }
                    }

                    override fun onFailure(call: Call<CheckSmsAiResponse>, t: Throwable) {
                        Log.e("checkAiResponse", "API call failed: ${t.message}")
                        retryCount++
                        handler.postDelayed({ pollStatus() }, 3000)
                    }
                })
        }

        // Start polling
        pollStatus()
    }

    private fun setResponsePopup(unsafeReasons: String?, msgStatus: String,msgContent: String) {

        if (!hasOverlayPermission(applicationContext)) {
            requestOverlayPermission(applicationContext)
            return
        }

        val windowManager = applicationContext.getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = applicationContext.getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val binding = LayoutValidationBinding.inflate(layoutInflater)


        currentPopupView?.let {
            try {
                windowManager.removeView(it)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        when (msgStatus) {
            "safe" -> {
                binding.lvWarning.setAnimation(R.raw.safe)
                binding.btClose.visibility = View.VISIBLE
            }
            "pending" -> {
                binding.lvWarning.setAnimation(R.raw.wait)
                binding.btClose.visibility = View.VISIBLE
            }
            "unsafe" -> {
                binding.lvWarning.setAnimation(R.raw.unsafe)
                binding.btClose.visibility = View.VISIBLE
            }
        }

        val view = binding.root
        view.setBackgroundResource(R.drawable.bg_alert_dialog)

        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

        binding.tvStatusTitle.text = msgStatus
        binding.tvMessage.text = msgContent
        binding.btClose.setOnClickListener {
            try {
                windowManager.removeView(view)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        windowManager.addView(view, params)
        currentPopupView = view
    }

    private fun hasOverlayPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true
        }
    }

    private fun requestOverlayPermission(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(context)) {
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:$context.packageName")
                )
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
            }
        }
    }

    private fun extractRealSmsNodeId(clickedNode: AccessibilityNodeInfo?, sender: String, message: String, timestamp: Long): String {
        if (clickedNode == null) {
            Log.w(TAG, "⚠️ No clicked node available, generating fallback ID")
            return generateFallbackId(sender, message, timestamp)
        }

        Log.d(TAG, "🔍 Extracting real SMS node ID from accessibility node...")

        // Method 1: Try to get the actual node ID from the system
        val systemNodeId = extractSystemNodeId(clickedNode)
        if (systemNodeId != null) {
            Log.d(TAG, "✅ Found system node ID: $systemNodeId")
            return systemNodeId
        }

        // Method 2: Look for SMS database ID patterns in the node hierarchy
        val databaseId = extractSmsDbId(clickedNode)
        if (databaseId != null) {
            Log.d(TAG, "✅ Found SMS database ID: $databaseId")
            return databaseId
        }

        // Method 3: Look for thread/conversation ID
        val threadId = extractThreadId(clickedNode)
        if (threadId != null) {
            Log.d(TAG, "✅ Found thread ID: $threadId")
            return threadId
        }

        // Method 4: Use node's unique system identifier
        val nodeSystemId = getNodeSystemIdentifier(clickedNode)
        if (nodeSystemId != null) {
            Log.d(TAG, "✅ Found node system identifier: $nodeSystemId")
            return nodeSystemId
        }

        // Method 5: Generate consistent ID based on node properties
        val consistentId = generateConsistentNodeId(clickedNode, sender, message)
        Log.d(TAG, "✅ Generated consistent node ID: $consistentId")
        return consistentId
    }

    private fun extractSystemNodeId(node: AccessibilityNodeInfo): String? {
        try {
            // Check if the node has a unique identifier in its resource name
            val resourceName = node.viewIdResourceName
            if (resourceName != null) {
                Log.d(TAG, "🔍 Checking resource name: $resourceName")

                // Look for numeric IDs in resource names
                val patterns = listOf(
                    Regex("message[_\\-]?(\\d+)", RegexOption.IGNORE_CASE),
                    Regex("sms[_\\-]?(\\d+)", RegexOption.IGNORE_CASE),
                    Regex("msg[_\\-]?(\\d+)", RegexOption.IGNORE_CASE),
                    Regex("item[_\\-]?(\\d+)", RegexOption.IGNORE_CASE),
                    Regex("text[_\\-]?(\\d+)", RegexOption.IGNORE_CASE),
                    Regex("bubble[_\\-]?(\\d+)", RegexOption.IGNORE_CASE),
                    Regex("conversation[_\\-]?(\\d+)", RegexOption.IGNORE_CASE)
                )

                for (pattern in patterns) {
                    pattern.find(resourceName)?.let { match ->
                        val id = match.groupValues[1]
                        if (id.isNotEmpty()) {
                            return "NODE_$id"
                        }
                    }
                }

                // If resource name contains a hash-like identifier
                val hashPattern = Regex("([a-f0-9]{8,})", RegexOption.IGNORE_CASE)
                hashPattern.find(resourceName)?.let { match ->
                    val hash = match.groupValues[1]
                    return "HASH_$hash"
                }
            }

            // Check content description for IDs
            val contentDesc = node.contentDescription?.toString()
            if (contentDesc != null) {
                Log.d(TAG, "🔍 Checking content description: $contentDesc")

                val idPattern = Regex("(id|identifier)[:\\s]*(\\d+)", RegexOption.IGNORE_CASE)
                idPattern.find(contentDesc)?.let { match ->
                    val id = match.groupValues[2]
                    return "DESC_$id"
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error extracting system node ID: ${e.message}", e)
        }

        return null
    }

    private fun extractSmsDbId(node: AccessibilityNodeInfo): String? {
        try {
            // Check current node and all parents for database ID patterns
            val nodesToCheck = mutableListOf(node)
            nodesToCheck.addAll(getParentNodes(node))

            for (nodeToCheck in nodesToCheck) {
                val resourceName = nodeToCheck.viewIdResourceName
                if (resourceName != null) {
                    Log.d(TAG, "🔍 Checking for DB ID in: $resourceName")

                    // Look for database-style IDs
                    val dbPatterns = listOf(
                        Regex("_id[_\\-]?(\\d+)", RegexOption.IGNORE_CASE),
                        Regex("row[_\\-]?(\\d+)", RegexOption.IGNORE_CASE),
                        Regex("record[_\\-]?(\\d+)", RegexOption.IGNORE_CASE),
                        Regex("entry[_\\-]?(\\d+)", RegexOption.IGNORE_CASE)
                    )

                    for (pattern in dbPatterns) {
                        pattern.find(resourceName)?.let { match ->
                            val id = match.groupValues[1]
                            return "DB_$id"
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting SMS DB ID: ${e.message}", e)
        }

        return null
    }

    private fun extractThreadId(node: AccessibilityNodeInfo): String? {
        try {
            val nodesToCheck = mutableListOf(node)
            nodesToCheck.addAll(getParentNodes(node))

            for (nodeToCheck in nodesToCheck) {
                val resourceName = nodeToCheck.viewIdResourceName
                if (resourceName != null) {
                    Log.d(TAG, "🔍 Checking for thread ID in: $resourceName")

                    val threadPatterns = listOf(
                        Regex("thread[_\\-]?(\\d+)", RegexOption.IGNORE_CASE),
                        Regex("conversation[_\\-]?(\\d+)", RegexOption.IGNORE_CASE),
                        Regex("chat[_\\-]?(\\d+)", RegexOption.IGNORE_CASE)
                    )

                    for (pattern in threadPatterns) {
                        pattern.find(resourceName)?.let { match ->
                            val id = match.groupValues[1]
                            return "THREAD_$id"
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting thread ID: ${e.message}", e)
        }

        return null
    }

    private fun getNodeSystemIdentifier(node: AccessibilityNodeInfo): String? {
        try {
            // Use the node's system hash code as a consistent identifier
            val nodeHashCode = System.identityHashCode(node)

            // Also try to get a more stable identifier from the node's properties
            val stableProperties = mutableListOf<String>()

            node.viewIdResourceName?.let { stableProperties.add(it) }
            node.className?.let { stableProperties.add(it.toString()) }

            // Get parent information for more stability
            node.parent?.let { parent ->
                parent.viewIdResourceName?.let { stableProperties.add("P:$it") }
                parent.className?.let { stableProperties.add("PC:${it}") }
            }

            if (stableProperties.isNotEmpty()) {
                val stableHash = stableProperties.joinToString("|").hashCode()
                return "STABLE_${stableHash.toString(16).uppercase()}"
            }

            return "SYS_${nodeHashCode.toString(16).uppercase()}"

        } catch (e: Exception) {
            Log.e(TAG, "Error getting node system identifier: ${e.message}", e)
        }

        return null
    }

    private fun generateConsistentNodeId(node: AccessibilityNodeInfo, sender: String, message: String): String {
        val components = mutableListOf<String>()

        // Add stable node properties
        node.viewIdResourceName?.let { components.add("R:$it") }
        node.className?.let { components.add("C:${it.toString().split(".").lastOrNull()}") }

        // Add parent context for stability
        node.parent?.let { parent ->
            parent.viewIdResourceName?.let { components.add("PR:$it") }
            parent.className?.let { components.add("PC:${it.toString().split(".").lastOrNull()}") }
        }

        // Add position in parent if available
        try {
            val parent = node.parent
            if (parent != null) {
                for (i in 0 until parent.childCount) {
                    if (parent.getChild(i) == node) {
                        components.add("POS:$i")
                        break
                    }
                }
            }
        } catch (e: Exception) {
            // Ignore position errors
        }

        // Add content-based components for additional uniqueness
        components.add("S:${sender.hashCode()}")
        components.add("M:${message.take(20).hashCode()}")

        val combinedString = components.joinToString("|")
        val consistentHash = combinedString.hashCode().toString(16).uppercase()

        return "CONSISTENT_$consistentHash"
    }

    private fun generateFallbackId(sender: String, message: String, timestamp: Long): String {
        val components = listOf(
            sender.hashCode().toString(16),
            message.take(30).hashCode().toString(16),
            (timestamp / 60000).toString(16) // Round to minute
        )

        return "FALLBACK_${components.joinToString("_").uppercase()}"
    }

    private fun getNodeHierarchy(node: AccessibilityNodeInfo?): String {
        if (node == null) return "No node"

        val hierarchy = mutableListOf<String>()

        // Add current node
        val currentInfo = "${node.className?.toString()?.split(".")?.lastOrNull() ?: "Unknown"}(${node.viewIdResourceName ?: "no-id"})"
        hierarchy.add(currentInfo)

        // Add parent hierarchy
        val parents = getParentNodes(node)
        parents.take(3).forEach { parent ->
            val parentInfo = "${parent.className?.toString()?.split(".")?.lastOrNull() ?: "Unknown"}(${parent.viewIdResourceName ?: "no-id"})"
            hierarchy.add(parentInfo)
        }

        return hierarchy.joinToString(" -> ")
    }

    private fun extractInboxData(rootNode: AccessibilityNodeInfo, packageName: String) {
        Log.d(TAG, "📥 Extracting inbox data for $packageName")

        val allNodes = getAllNodes(rootNode)
        val conversations = mutableListOf<Map<String, String>>()

        allNodes.forEach { node ->
            val text = node.text?.toString()?.trim() ?: ""
            val viewId = node.viewIdResourceName ?: ""

            // Look for conversation items in inbox
            if (isConversationItem(text, viewId, packageName)) {
                val conversationData = mutableMapOf<String, String>()
                conversationData["sender"] = extractSenderFromInboxItem(node, allNodes)
                conversationData["preview"] = extractPreviewFromInboxItem(node, allNodes)
                conversationData["timestamp"] = extractTimestampFromContext(node, allNodes)

                conversations.add(conversationData)
                Log.d(TAG, "💬 Inbox conversation: ${conversationData["sender"]} - ${conversationData["preview"]?.take(30)}...")
            }
        }

        Log.d(TAG, "📥 Found ${conversations.size} conversations in inbox")
    }

    private fun isConversationItem(text: String, viewId: String, packageName: String): Boolean {
        return when (packageName) {
            "com.google.android.apps.messaging" -> {
                viewId.contains("conversation_item", true) ||
                viewId.contains("contact_name", true) ||
                (text.isNotEmpty() && text.length < 50 && !text.contains("Type", true))
            }
            "com.samsung.android.messaging" -> {
                viewId.contains("conversation", true) ||
                viewId.contains("thread", true)
            }
            else -> {
                viewId.contains("conversation", true) ||
                viewId.contains("item", true) ||
                (text.isNotEmpty() && text.length < 100)
            }
        }
    }

    private fun extractSenderFromGoogleMessages(messageNode: AccessibilityNodeInfo, allNodes: List<AccessibilityNodeInfo>): String {
        // Look for sender name in nearby nodes
        val nodeIndex = allNodes.indexOf(messageNode)

        // Check previous nodes for sender information
        for (i in (nodeIndex - 3).coerceAtLeast(0) until nodeIndex) {
            val node = allNodes[i]
            val text = node.text?.toString()?.trim() ?: ""
            val viewId = node.viewIdResourceName ?: ""

            if ((viewId.contains("contact", true) || viewId.contains("sender", true)) &&
                text.isNotEmpty() && !isLikelyMessage(text)) {
                return text
            }
        }

        // Check if message is outgoing (sent by user)
        val viewId = messageNode.viewIdResourceName ?: ""
        if (viewId.contains("outgoing", true) || viewId.contains("sent", true)) {
            return "You"
        }

        return "Unknown"
    }

    private fun extractSenderFromSamsungMessages(messageNode: AccessibilityNodeInfo, allNodes: List<AccessibilityNodeInfo>): String {
        val nodeIndex = allNodes.indexOf(messageNode)

        // Look for sender in nearby nodes
        for (i in (nodeIndex - 2).coerceAtLeast(0) until nodeIndex) {
            val node = allNodes[i]
            val text = node.text?.toString()?.trim() ?: ""
            val viewId = node.viewIdResourceName ?: ""

            if (viewId.contains("sender", true) || viewId.contains("name", true)) {
                return text.ifEmpty { "Unknown" }
            }
        }

        return "Unknown"
    }

    private fun extractSenderFromContext(messageNode: AccessibilityNodeInfo, allNodes: List<AccessibilityNodeInfo>): String {
        val nodeIndex = allNodes.indexOf(messageNode)

        // Look in nearby nodes for potential sender
        for (i in (nodeIndex - 3).coerceAtLeast(0) until nodeIndex) {
            val node = allNodes[i]
            val text = node.text?.toString()?.trim() ?: ""

            // Check if text looks like a name or phone number
            if (text.isNotEmpty() && !isLikelyMessage(text) &&
                (text.matches(Regex("^[A-Za-z\\s]+$")) || // Name
                 text.matches(Regex("^[+]?[0-9\\s\\-()]+$")))) { // Phone
                return text
            }
        }

        return "Unknown"
    }
    
    private fun showSmsWarningOverlay(sender: String, message: String, analysisResult: SmsDetectionHelper.SmsAnalysisResult) {
        try {
            // Remove existing popup if any
            currentPopupView?.let { view ->
                try {
                    val windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
                    windowManager.removeView(view)
                } catch (e: Exception) {
                    Log.e(TAG, "Error removing existing popup: ${e.message}")
                }
            }
            
            // For now, just use notification instead of popup
            // TODO: Create layout_sms_warning_popup.xml for custom popup
            SmsDetectionHelper.showSuspiciousMessageNotification(
                context = this,
                sender = sender,
                body = message,
                riskLevel = analysisResult.riskLevel
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error showing SMS warning overlay: ${e.message}", e)
            
            // Fallback to notification
            SmsDetectionHelper.showSuspiciousMessageNotification(
                context = this,
                sender = sender,
                body = message,
                riskLevel = analysisResult.riskLevel
            )
        }
    }
    
    private fun findNodesByText(node: AccessibilityNodeInfo, text: String): List<AccessibilityNodeInfo> {
        val result = mutableListOf<AccessibilityNodeInfo>()
        
        if (node.text?.toString()?.contains(text, ignoreCase = true) == true) {
            result.add(node)
        }
        
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                result.addAll(findNodesByText(child, text))
            }
        }
        
        return result
    }
    
    private fun findNodesByClassName(node: AccessibilityNodeInfo, className: String): List<AccessibilityNodeInfo> {
        val result = mutableListOf<AccessibilityNodeInfo>()
        
        if (node.className?.toString() == className) {
            result.add(node)
        }
        
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                result.addAll(findNodesByClassName(child, className))
            }
        }
        
        return result
    }
    
    private fun findNodesByResourceId(node: AccessibilityNodeInfo, resourceId: String): List<AccessibilityNodeInfo> {
        val result = mutableListOf<AccessibilityNodeInfo>()
        
        if (node.viewIdResourceName?.contains(resourceId) == true) {
            result.add(node)
        }
        
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                result.addAll(findNodesByResourceId(child, resourceId))
            }
        }
        
        return result
    }

    private fun extractTimestampFromContext(messageNode: AccessibilityNodeInfo, allNodes: List<AccessibilityNodeInfo>): String {
        val nodeIndex = allNodes.indexOf(messageNode)

        // Look for timestamp in nearby nodes
        for (i in (nodeIndex - 3).coerceAtLeast(0)..(nodeIndex + 3).coerceAtMost(allNodes.size - 1)) {
            val node = allNodes[i]
            val text = node.text?.toString()?.trim() ?: ""
            val viewId = node.viewIdResourceName ?: ""

            // Check for timestamp indicators in ViewId
            if (viewId.contains("time", true) || viewId.contains("date", true) ||
                viewId.contains("timestamp", true)) {
                val timestamp = parseTimestampText(text)
                if (timestamp != null) {
                    Log.d(TAG, "🕒 Found timestamp from ViewId: $text -> $timestamp")
                    return timestamp.toString()
                }
            }

            // Check for various time patterns in text
            val timestamp = parseTimestampText(text)
            if (timestamp != null) {
                Log.d(TAG, "🕒 Found timestamp from text: $text -> $timestamp")
                return timestamp.toString()
            }
        }

        // Fallback: try to extract from message content itself
        val messageText = messageNode.text?.toString() ?: ""
        val timestampFromMessage = extractTimestampFromMessageContent(messageText)
        if (timestampFromMessage != null) {
            Log.d(TAG, "🕒 Found timestamp from message content: $timestampFromMessage")
            return timestampFromMessage.toString()
        }

        Log.w(TAG, "⚠️ No timestamp found, using current time")
        return System.currentTimeMillis().toString()
    }

    private fun parseTimestampText(text: String): Long? {
        if (text.isEmpty()) return null

        try {
            // Pattern 1: "12:34 PM" or "12:34 AM"
            val timePattern1 = Regex("(\\d{1,2}):(\\d{2})\\s*(AM|PM)", RegexOption.IGNORE_CASE)
            timePattern1.find(text)?.let { match ->
                val hour = match.groupValues[1].toInt()
                val minute = match.groupValues[2].toInt()
                val ampm = match.groupValues[3].uppercase()

                val adjustedHour = when {
                    ampm == "AM" && hour == 12 -> 0
                    ampm == "PM" && hour != 12 -> hour + 12
                    else -> hour
                }

                return getTodayTimestamp(adjustedHour, minute)
            }

            // Pattern 2: "12:34" (24-hour format)
            val timePattern2 = Regex("^(\\d{1,2}):(\\d{2})$")
            timePattern2.find(text)?.let { match ->
                val hour = match.groupValues[1].toInt()
                val minute = match.groupValues[2].toInt()

                if (hour in 0..23 && minute in 0..59) {
                    return getTodayTimestamp(hour, minute)
                }
            }

            // Pattern 3: "Yesterday 12:34 PM"
            val yesterdayPattern = Regex("Yesterday\\s+(\\d{1,2}):(\\d{2})\\s*(AM|PM)", RegexOption.IGNORE_CASE)
            yesterdayPattern.find(text)?.let { match ->
                val hour = match.groupValues[1].toInt()
                val minute = match.groupValues[2].toInt()
                val ampm = match.groupValues[3].uppercase()

                val adjustedHour = when {
                    ampm == "AM" && hour == 12 -> 0
                    ampm == "PM" && hour != 12 -> hour + 12
                    else -> hour
                }

                return getYesterdayTimestamp(adjustedHour, minute)
            }

            // Pattern 4: "Mon 12:34 PM" or "Monday 12:34 PM"
            val dayPattern = Regex("(Mon|Tue|Wed|Thu|Fri|Sat|Sun|Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)\\s+(\\d{1,2}):(\\d{2})\\s*(AM|PM)", RegexOption.IGNORE_CASE)
            dayPattern.find(text)?.let { match ->
                val hour = match.groupValues[2].toInt()
                val minute = match.groupValues[3].toInt()
                val ampm = match.groupValues[4].uppercase()

                val adjustedHour = when {
                    ampm == "AM" && hour == 12 -> 0
                    ampm == "PM" && hour != 12 -> hour + 12
                    else -> hour
                }

                // For now, assume it's from this week (could be enhanced)
                return getTodayTimestamp(adjustedHour, minute)
            }

            // Pattern 5: "12/25/2023 12:34 PM" or "25/12/2023 12:34 PM"
            val fullDatePattern = Regex("(\\d{1,2})/(\\d{1,2})/(\\d{4})\\s+(\\d{1,2}):(\\d{2})\\s*(AM|PM)", RegexOption.IGNORE_CASE)
            fullDatePattern.find(text)?.let { match ->
                val month = match.groupValues[1].toInt()
                val day = match.groupValues[2].toInt()
                val year = match.groupValues[3].toInt()
                val hour = match.groupValues[4].toInt()
                val minute = match.groupValues[5].toInt()
                val ampm = match.groupValues[6].uppercase()

                val adjustedHour = when {
                    ampm == "AM" && hour == 12 -> 0
                    ampm == "PM" && hour != 12 -> hour + 12
                    else -> hour
                }

                return getSpecificDateTimestamp(year, month, day, adjustedHour, minute)
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error parsing timestamp: $text", e)
        }

        return null
    }

    private fun extractTimestampFromMessageContent(messageText: String): Long? {
        // Look for timestamp patterns within the message
        val patterns = listOf(
            Regex("Sent at (\\d{1,2}):(\\d{2})\\s*(AM|PM)", RegexOption.IGNORE_CASE),
            Regex("Received at (\\d{1,2}):(\\d{2})\\s*(AM|PM)", RegexOption.IGNORE_CASE),
            Regex("(\\d{1,2}):(\\d{2})\\s*(AM|PM)", RegexOption.IGNORE_CASE)
        )

        for (pattern in patterns) {
            pattern.find(messageText)?.let { match ->
                val hour = match.groupValues[1].toInt()
                val minute = match.groupValues[2].toInt()
                val ampm = match.groupValues[3].uppercase()

                val adjustedHour = when {
                    ampm == "AM" && hour == 12 -> 0
                    ampm == "PM" && hour != 12 -> hour + 12
                    else -> hour
                }

                return getTodayTimestamp(adjustedHour, minute)
            }
        }

        return null
    }

    private fun getTodayTimestamp(hour: Int, minute: Int): Long {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(java.util.Calendar.HOUR_OF_DAY, hour)
        calendar.set(java.util.Calendar.MINUTE, minute)
        calendar.set(java.util.Calendar.SECOND, 0)
        calendar.set(java.util.Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }

    private fun getYesterdayTimestamp(hour: Int, minute: Int): Long {
        val calendar = java.util.Calendar.getInstance()
        calendar.add(java.util.Calendar.DAY_OF_YEAR, -1)
        calendar.set(java.util.Calendar.HOUR_OF_DAY, hour)
        calendar.set(java.util.Calendar.MINUTE, minute)
        calendar.set(java.util.Calendar.SECOND, 0)
        calendar.set(java.util.Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }

    /**
     * Get timestamp for specific date and time
     */
    private fun getSpecificDateTimestamp(year: Int, month: Int, day: Int, hour: Int, minute: Int): Long {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(year, month - 1, day, hour, minute, 0) // Month is 0-based
        calendar.set(java.util.Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }

    private fun extractSenderFromInboxItem(itemNode: AccessibilityNodeInfo, allNodes: List<AccessibilityNodeInfo>): String {
        val nodeIndex = allNodes.indexOf(itemNode)

        // Look for sender name in the same item or nearby
        for (i in (nodeIndex - 1).coerceAtLeast(0)..(nodeIndex + 1).coerceAtMost(allNodes.size - 1)) {
            val node = allNodes[i]
            val text = node.text?.toString()?.trim() ?: ""
            val viewId = node.viewIdResourceName ?: ""

            if ((viewId.contains("name", true) || viewId.contains("contact", true)) &&
                text.isNotEmpty() && !isLikelyMessage(text)) {
                return text
            }
        }

        return "Unknown"
    }

    private fun extractPreviewFromInboxItem(itemNode: AccessibilityNodeInfo, allNodes: List<AccessibilityNodeInfo>): String {
        val nodeIndex = allNodes.indexOf(itemNode)

        // Look for message preview
        for (i in (nodeIndex - 1).coerceAtLeast(0)..(nodeIndex + 2).coerceAtMost(allNodes.size - 1)) {
            val node = allNodes[i]
            val text = node.text?.toString()?.trim() ?: ""
            val viewId = node.viewIdResourceName ?: ""

            if ((viewId.contains("preview", true) || viewId.contains("snippet", true)) &&
                text.isNotEmpty() && isLikelyMessage(text)) {
                return text
            }
        }

        return ""
    }

    private fun extractGoogleMessageData(rootNode: AccessibilityNodeInfo): Map<String, String> {
        val allNodes = getAllNodes(rootNode)
        val messageData = mutableMapOf<String, String>()

        allNodes.forEach { node ->
            val text = node.text?.toString()?.trim() ?: ""
            val viewId = node.viewIdResourceName ?: ""

            if (viewId.contains("message", true) && text.isNotEmpty() && isLikelyMessage(text)) {
                messageData["content"] = text
                messageData["sender"] = extractSenderFromGoogleMessages(node, allNodes)
                messageData["timestamp"] = extractTimestampFromContext(node, allNodes)
                messageData["type"] = if (viewId.contains("outgoing", true)) "sent" else "received"
                return messageData
            }
        }

        return messageData
    }

    private fun extractSamsungMessageData(rootNode: AccessibilityNodeInfo): Map<String, String> {
        val allNodes = getAllNodes(rootNode)
        val messageData = mutableMapOf<String, String>()

        allNodes.forEach { node ->
            val text = node.text?.toString()?.trim() ?: ""
            val viewId = node.viewIdResourceName ?: ""

            if (viewId.contains("message", true) && text.isNotEmpty() && isLikelyMessage(text)) {
                messageData["content"] = text
                messageData["sender"] = extractSenderFromSamsungMessages(node, allNodes)
                messageData["timestamp"] = extractTimestampFromContext(node, allNodes)
                messageData["type"] = if (viewId.contains("sent", true)) "sent" else "received"
                return messageData
            }
        }

        return messageData
    }

    private fun extractGenericMessageData(rootNode: AccessibilityNodeInfo): Map<String, String> {
        val allNodes = getAllNodes(rootNode)
        val messageData = mutableMapOf<String, String>()

        allNodes.forEach { node ->
            val text = node.text?.toString()?.trim() ?: ""

            if (text.isNotEmpty() && isLikelyMessage(text)) {
                messageData["content"] = text
                messageData["sender"] = extractSenderFromContext(node, allNodes)
                messageData["timestamp"] = extractTimestampFromContext(node, allNodes)
                messageData["type"] = "unknown"
                return messageData
            }
        }

        return messageData
    }

    private fun getAllNodes(rootNode: AccessibilityNodeInfo): List<AccessibilityNodeInfo> {
        val allNodes = mutableListOf<AccessibilityNodeInfo>()
        collectAllNodes(rootNode, allNodes)
        return allNodes
    }

    private fun collectAllNodes(node: AccessibilityNodeInfo, collector: MutableList<AccessibilityNodeInfo>) {
        try {
            collector.add(node)
            for (i in 0 until node.childCount) {
                node.getChild(i)?.let { child ->
                    collectAllNodes(child, collector)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error collecting nodes: ${e.message}")
        }
    }

    private fun formatTimestamp(timestamp: Long?): String {
        if (timestamp == null) return "Unknown time"

        return try {
            val dateFormat = java.text.SimpleDateFormat("MMM dd, yyyy HH:mm", java.util.Locale.getDefault())
            dateFormat.format(java.util.Date(timestamp))
        } catch (e: Exception) {
            "Invalid time"
        }
    }

    private fun getParentNodes(node: AccessibilityNodeInfo): List<AccessibilityNodeInfo> {
        val parents = mutableListOf<AccessibilityNodeInfo>()
        var current = node.parent

        while (current != null && parents.size < 5) { // Limit to 5 levels
            parents.add(current)
            current = current.parent
        }

        return parents
    }

    private fun generateHierarchyBasedId(node: AccessibilityNodeInfo): String {
        val hierarchyComponents = mutableListOf<String>()

        // Add current node info
        node.className?.let { className ->
            hierarchyComponents.add(className.toString().split(".").lastOrNull() ?: "Unknown")
        }

        // Add parent hierarchy (limited)
        val parents = getParentNodes(node)
        parents.take(3).forEach { parent ->
            parent.className?.let { className ->
                hierarchyComponents.add(className.toString().split(".").lastOrNull() ?: "Unknown")
            }
        }

        // Create hash from hierarchy
        val hierarchyString = hierarchyComponents.joinToString("->")
        return hierarchyString.hashCode().toString(16).uppercase()
    }

    private fun normalizeSender(sender: String): String {
        return sender.trim()
            .replace(Regex("[^a-zA-Z0-9+]"), "") // Remove special chars except +
            .lowercase()
    }

    private fun getNodeIdentifiers(node: AccessibilityNodeInfo?): String {
        if (node == null) return "No node"

        val info = mutableListOf<String>()

        node.viewIdResourceName?.let { info.add("ID:$it") }
        node.className?.let { info.add("Class:${it.toString().split(".").lastOrNull()}") }
        node.text?.let { info.add("Text:${it.toString().take(20)}") }

        return info.joinToString(", ")
    }

    private fun isLikelyMessage(text: String): Boolean {
        return text.length > 10 &&
               !text.matches(Regex("^\\d{1,2}:\\d{2}.*")) && // Not a timestamp
               !text.matches(Regex("^[A-Z]{3}\\s\\d{1,2}.*")) && // Not a date
               !text.matches(Regex("^[+]?[0-9\\s\\-()]+$")) && // Not just a phone number
               !text.matches(Regex("^(Yesterday|Today|Mon|Tue|Wed|Thu|Fri|Sat|Sun).*", RegexOption.IGNORE_CASE)) && // Not date indicators
               text.split(" ").size > 2 && // Has multiple words
               !text.contains("Type a message", true) && // Not input placeholder
               !text.contains("Search", true) // Not search text
    }


   /* private fun  checkSms(){
        val retrofit = ApiClient.getRetrofitInstance(requireContext())
        if (retrofit == null) {
            Log.e(TAG, "Failed to get Retrofit instance")
            updateEmailStatsUI(EmailStats())
            return
        }

        val apiService = retrofit.create(ApiService::class.java)
        val request = CommonRequest(emailId = receiverEmail)

        // Load activity graph data
        apiService.getActivtyGraph(request)
            .enqueue(object : Callback<ActivityGraphResponse> {
                override fun onResponse(
                    call: Call<ActivityGraphResponse>,
                    response: Response<ActivityGraphResponse>
                ) {
                    if (response.isSuccessful && response.body() != null) {
                        val data = response.body()!!.data
                        val emailStats = EmailStats(
                            totalEmails = data?.totalProcessedEmails ?: 0,
                            spamEmails = data?.totalSpamEmails ?: 0,
                            disputes = data?.totalDisputes ?: 0,
                            safeEmails = (data?.totalProcessedEmails ?: 0) - (data?.totalSpamEmails ?: 0),
                            lastProcessedTime = getCurrentTimeString()
                        )
                        updateEmailStatsUI(emailStats)
                        updateEmailChart(emailStats)
                    } else {
                        Log.e(TAG, "Failed to load email stats: ${response.message()}")
                        updateEmailStatsUI(EmailStats())
                    }

                    // Stop refresh animation if it's running
                    stopRefreshAnimation()
                }

                override fun onFailure(call: Call<ActivityGraphResponse>, t: Throwable) {
                    Log.e(TAG, "Error loading email stats: ${t.message}", t)
                    updateEmailStatsUI(EmailStats())

                    // Stop refresh animation if it's running
                    stopRefreshAnimation()
                }
            })
    }
*/

}
