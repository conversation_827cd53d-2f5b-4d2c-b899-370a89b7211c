package com.tech.ekvayu.BaseClass

import android.accessibilityservice.AccessibilityService
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import android.widget.Toast
import com.tech.ekvayu.EkService.NewGmailAccessibilityService
import com.tech.ekvayu.EkService.OutlookAccessibilityService
import com.tech.ekvayu.EkService.SmsAccessibilityService
import com.tech.ekvayu.EkService.YahooAccessibilityService

/**
 * Helper class to manage permission states and provide centralized permission checking
 * Enhanced with device-specific accessibility settings support
 */
object PermissionHelper {

    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    private const val TAG = "PermissionHelper"

    /**
     * Enum for different accessibility service types
     */
    enum class ServiceType(val serviceName: String, val displayName: String) {
        GMAIL("Gmail", "Gmail Detection"),
        <PERSON><PERSON><PERSON>O<PERSON>("Yahoo", "Yahoo Detection"),
        OUTLOOK("Outlook", "Outlook Detection"),
        SMS("SMS", "SMS Detection")
    }
    
    /**
     * Check if accessibility service is enabled and update SharedPreferences (Gmail by default)
     */
    fun checkAndUpdateAccessibilityPermission(context: Context): Boolean {
        val isEnabled = isAccessibilityServiceEnabled(context, NewGmailAccessibilityService::class.java)
        sharedPrefManager.putBoolean(AppConstant.isAccessibilityPermissionGranted, isEnabled)
        return isEnabled
    }

    /**
     * Check specific accessibility service based on service type
     */
    fun checkSpecificAccessibilityService(context: Context, serviceType: ServiceType): Boolean {
        return when (serviceType) {
            ServiceType.GMAIL -> isAccessibilityServiceEnabled(context, NewGmailAccessibilityService::class.java)
            ServiceType.YAHOO -> isAccessibilityServiceEnabled(context, YahooAccessibilityService::class.java)
            ServiceType.OUTLOOK -> isAccessibilityServiceEnabled(context, OutlookAccessibilityService::class.java)
            ServiceType.SMS -> isAccessibilityServiceEnabled(context, SmsAccessibilityService::class.java)
        }
    }

    /**
     * Check and update specific accessibility service permission
     */
    fun checkAndUpdateSpecificAccessibilityPermission(context: Context, serviceType: ServiceType): Boolean {
        val isEnabled = checkSpecificAccessibilityService(context, serviceType)

        // Update SharedPreferences based on service type
        when (serviceType) {
            ServiceType.GMAIL -> sharedPrefManager.putBoolean(AppConstant.isAccessibilityPermissionGranted, isEnabled)
            ServiceType.YAHOO -> sharedPrefManager.putBoolean("isYahooAccessibilityEnabled", isEnabled)
            ServiceType.OUTLOOK -> sharedPrefManager.putBoolean("isOutlookAccessibilityEnabled", isEnabled)
            ServiceType.SMS -> sharedPrefManager.putBoolean("isSmsAccessibilityEnabled", isEnabled)
        }

        Log.d(TAG, "${serviceType.displayName} accessibility service enabled: $isEnabled")
        return isEnabled
    }
    
    /**
     * Check if mail is configured and update SharedPreferences
     */
    fun checkAndUpdateMailConfiguration(): Boolean {
        val receiverMail = sharedPrefManager.getString(AppConstant.receiverMail, "")
        val isConfigured = receiverMail.isNotEmpty()
        sharedPrefManager.putBoolean(AppConstant.isMailConfigured, isConfigured)
        return isConfigured
    }
    
    /**
     * Get current permission states from SharedPreferences
     */
    fun getPermissionStates(): PermissionStates {
        return PermissionStates(
            isAccessibilityPermissionGranted = sharedPrefManager.getBoolean(AppConstant.isAccessibilityPermissionGranted, false),
            isMailConfigured = sharedPrefManager.getBoolean(AppConstant.isMailConfigured, false),
            receiverMail = sharedPrefManager.getString(AppConstant.receiverMail, "")
        )
    }
    
    /**
     * Check if both permissions are granted
     */
    fun areAllPermissionsGranted(context: Context): Boolean {
        val accessibilityEnabled = checkAndUpdateAccessibilityPermission(context)
        val mailConfigured = checkAndUpdateMailConfiguration()
        return accessibilityEnabled && mailConfigured
    }
    
    /**
     * Reset all permission states (useful for testing or logout)
     */
    fun resetPermissionStates() {
        sharedPrefManager.putBoolean(AppConstant.isAccessibilityPermissionGranted, false)
        sharedPrefManager.putBoolean(AppConstant.isMailConfigured, false)
        sharedPrefManager.putString(AppConstant.receiverMail, "")
    }

    /**
     * Enhanced method to open accessibility settings that works on all devices
     * Especially handles Xiaomi/Redmi devices with HyperOS
     */
    fun openAccessibilitySettings(context: Context) {
        try {
            Log.d(TAG, "Opening accessibility settings for device: ${Build.MANUFACTURER} ${Build.MODEL}")

            when {
                // Xiaomi/Redmi devices (MIUI/HyperOS)
                isXiaomiDevice() -> openXiaomiAccessibilitySettings(context)

                // Samsung devices
                isSamsungDevice() -> openSamsungAccessibilitySettings(context)

                // Huawei devices
                isHuaweiDevice() -> openHuaweiAccessibilitySettings(context)

                // OnePlus devices
                isOnePlusDevice() -> openOnePlusAccessibilitySettings(context)

                // Oppo devices
                isOppoDevice() -> openOppoAccessibilitySettings(context)

                // Vivo devices
                isVivoDevice() -> openVivoAccessibilitySettings(context)

                // Default for other devices
                else -> openDefaultAccessibilitySettings(context)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error opening accessibility settings: ${e.message}")
            // Fallback to default settings
            openDefaultAccessibilitySettings(context)
        }
    }

    private fun isXiaomiDevice(): Boolean {
        return Build.MANUFACTURER.equals("Xiaomi", ignoreCase = true) ||
                Build.MANUFACTURER.equals("Redmi", ignoreCase = true) ||
                Build.BRAND.equals("Xiaomi", ignoreCase = true) ||
                Build.BRAND.equals("Redmi", ignoreCase = true)
    }

    private fun isSamsungDevice(): Boolean {
        return Build.MANUFACTURER.equals("Samsung", ignoreCase = true)
    }

    private fun isHuaweiDevice(): Boolean {
        return Build.MANUFACTURER.equals("Huawei", ignoreCase = true) ||
                Build.MANUFACTURER.equals("Honor", ignoreCase = true)
    }

    private fun isOnePlusDevice(): Boolean {
        return Build.MANUFACTURER.equals("OnePlus", ignoreCase = true)
    }

    private fun isOppoDevice(): Boolean {
        return Build.MANUFACTURER.equals("Oppo", ignoreCase = true)
    }

    private fun isVivoDevice(): Boolean {
        return Build.MANUFACTURER.equals("Vivo", ignoreCase = true)
    }

    /**
     * Open accessibility settings for Xiaomi/Redmi devices (MIUI/HyperOS)
     */
    private fun openXiaomiAccessibilitySettings(context: Context) {
        Log.d(TAG, "Opening Xiaomi/Redmi accessibility settings")

        val attempts = listOf(
            // HyperOS specific intent
            Intent().apply {
                component = ComponentName("com.miui.securitycenter", "com.miui.permcenter.permissions.PermissionsEditorActivity")
                putExtra("extra_pkgname", context.packageName)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            },

            // MIUI Security Center
            Intent().apply {
                component = ComponentName("com.miui.securitycenter", "com.miui.permcenter.permissions.AppPermissionsEditorActivity")
                putExtra("extra_pkgname", context.packageName)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            },

            // MIUI Accessibility Settings
            Intent().apply {
                component = ComponentName("com.android.settings", "com.android.settings.AccessibilitySettings")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            },

            // Alternative MIUI path
            Intent().apply {
                action = "miui.intent.action.APP_PERM_EDITOR"
                putExtra("extra_pkgname", context.packageName)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
        )

        if (!tryIntents(context, attempts)) {
            openDefaultAccessibilitySettings(context)
        }
    }

    /**
     * Open accessibility settings for Samsung devices
     */
    private fun openSamsungAccessibilitySettings(context: Context) {
        Log.d(TAG, "Opening Samsung accessibility settings")

        val attempts = listOf(
            Intent().apply {
                component = ComponentName("com.android.settings", "com.android.settings.accessibility.AccessibilitySettings")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            },
            Intent().apply {
                component = ComponentName("com.samsung.android.settings.accessibility", "com.samsung.android.settings.accessibility.AccessibilitySettings")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
        )

        if (!tryIntents(context, attempts)) {
            openDefaultAccessibilitySettings(context)
        }
    }

    /**
     * Open accessibility settings for Huawei devices
     */
    private fun openHuaweiAccessibilitySettings(context: Context) {
        Log.d(TAG, "Opening Huawei accessibility settings")

        val attempts = listOf(
            Intent().apply {
                component = ComponentName("com.huawei.systemmanager", "com.huawei.permissionmanager.ui.MainActivity")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            },
            Intent().apply {
                component = ComponentName("com.android.settings", "com.android.settings.accessibility.AccessibilitySettings")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
        )

        if (!tryIntents(context, attempts)) {
            openDefaultAccessibilitySettings(context)
        }
    }

    /**
     * Open accessibility settings for OnePlus devices
     */
    private fun openOnePlusAccessibilitySettings(context: Context) {
        Log.d(TAG, "Opening OnePlus accessibility settings")

        val attempts = listOf(
            Intent().apply {
                component = ComponentName("com.android.settings", "com.android.settings.accessibility.AccessibilitySettings")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            },
            Intent().apply {
                component = ComponentName("com.oneplus.security", "com.oneplus.security.chainlaunch.view.ChainLaunchAppListActivity")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
        )

        if (!tryIntents(context, attempts)) {
            openDefaultAccessibilitySettings(context)
        }
    }

    /**
     * Open accessibility settings for Oppo devices
     */
    private fun openOppoAccessibilitySettings(context: Context) {
        Log.d(TAG, "Opening Oppo accessibility settings")

        val attempts = listOf(
            Intent().apply {
                component = ComponentName("com.coloros.safecenter", "com.coloros.safecenter.permission.startup.FakeActivity")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            },
            Intent().apply {
                component = ComponentName("com.android.settings", "com.android.settings.accessibility.AccessibilitySettings")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
        )

        if (!tryIntents(context, attempts)) {
            openDefaultAccessibilitySettings(context)
        }
    }

    /**
     * Open accessibility settings for Vivo devices
     */
    private fun openVivoAccessibilitySettings(context: Context) {
        Log.d(TAG, "Opening Vivo accessibility settings")

        val attempts = listOf(
            Intent().apply {
                component = ComponentName("com.iqoo.secure", "com.iqoo.secure.ui.phoneoptimize.AddWhiteListActivity")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            },
            Intent().apply {
                component = ComponentName("com.vivo.permissionmanager", "com.vivo.permissionmanager.activity.BgStartUpManagerActivity")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            },
            Intent().apply {
                component = ComponentName("com.android.settings", "com.android.settings.accessibility.AccessibilitySettings")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
        )

        if (!tryIntents(context, attempts)) {
            openDefaultAccessibilitySettings(context)
        }
    }

    /**
     * Default accessibility settings for standard Android devices
     */
    private fun openDefaultAccessibilitySettings(context: Context) {
        Log.d(TAG, "Opening default accessibility settings")

        val attempts = listOf(
            Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            },
            Intent().apply {
                component = ComponentName("com.android.settings", "com.android.settings.accessibility.AccessibilitySettings")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            },
            Intent().apply {
                action = Settings.ACTION_SETTINGS
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
        )

        if (!tryIntents(context, attempts)) {
            Toast.makeText(context, "Unable to open accessibility settings. Please open manually from Settings > Accessibility", Toast.LENGTH_LONG).show()
        }
    }

    /**
     * Check if accessibility service is enabled
     */
    private fun isAccessibilityServiceEnabled(context: Context, service: Class<out AccessibilityService>): Boolean {
        val componentName = ComponentName(context, service)
        val enabledServices = Settings.Secure.getString(context.contentResolver, Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES)

        if (enabledServices.isNullOrEmpty()) {
            return false
        }

        val colonSplitter = TextUtils.SimpleStringSplitter(':')
        colonSplitter.setString(enabledServices)

        while (colonSplitter.hasNext()) {
            if (colonSplitter.next().equals(componentName.flattenToString(), ignoreCase = true)) {
                return true
            }
        }
        return false
    }


    /**
     * Try multiple intents until one succeeds
     */
    private fun tryIntents(context: Context, intents: List<Intent>): Boolean {
        for (intent in intents) {
            try {
                context.startActivity(intent)
                Log.d(TAG, "Successfully opened accessibility settings with intent: ${intent.component}")
                return true
            } catch (e: Exception) {
                Log.w(TAG, "Failed to open with intent: ${intent.component}, error: ${e.message}")
                continue
            }
        }
        return false
    }


    /**
     * Enhanced method to open app-specific accessibility settings
     * This tries to open the specific accessibility service page for the app
     */
    fun openAppSpecificAccessibilitySettings(context: Context) {
        try {
            val serviceName = "${context.packageName}/${NewGmailAccessibilityService::class.java.name}"
            Log.d(TAG, "Trying to open app-specific accessibility settings for: $serviceName")

            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
                putExtra(":settings:fragment_args_key", serviceName)
                putExtra(":settings:show_fragment_args", android.os.Bundle().apply {
                    putString("service", serviceName)
                })
            }

            context.startActivity(intent)
            Log.d(TAG, "Successfully opened app-specific accessibility settings")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open app-specific settings, falling back to general settings: ${e.message}")
            openAccessibilitySettings(context)
        }
    }

    /**
     * Get device information for debugging
     */
    fun getDeviceInfo(): String {
        return "Manufacturer: ${Build.MANUFACTURER}, " +
                "Brand: ${Build.BRAND}, " +
                "Model: ${Build.MODEL}, " +
                "SDK: ${Build.VERSION.SDK_INT}, " +
                "Release: ${Build.VERSION.RELEASE}"
    }
}

/**
 * Data class to hold permission states
 */
data class PermissionStates(
    val isAccessibilityPermissionGranted: Boolean,
    val isMailConfigured: Boolean,
    val receiverMail: String
)
