package com.tech.ekvayu.Models

data class OptionModel(
    val id: String,
    val title: String,
    val description: String = "",
    val iconRes: Int = 0,
    val showArrow: Boolean = false,
    val isEnabled: Boolean = true,
    val action: OptionAction = OptionAction.NONE
)

enum class OptionAction {
    NONE,
    NAVIGATE,
    SHARE,
    COPY,
    DELETE,
    EDIT,
    SETTINGS,
    EXPORT,
    IMPORT,
    REFRESH,
    CLEAR,
    BAC<PERSON>UP,
    RESTORE
}
