<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Activities.MainActivity">


    <androidx.appcompat.widget.AppCompatImageView
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginVertical="@dimen/_100sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:id="@+id/ivIcon"
        android:src="@drawable/icon_secure_phishing"
        android:layout_width="@dimen/_100sdp"
        android:layout_height="@dimen/_100sdp"/>


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvContent"
        app:layout_constraintTop_toBottomOf="@+id/ivIcon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/_10sdp"
        android:textAlignment="center"
        android:fontFamily="@font/roboto_semi_medium"
        android:textColor="@color/green"
        android:textSize="@dimen/_12sdp"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:layout_marginVertical="@dimen/_20sdp"
        android:text="@string/secure_my_app_from_phishing"/>


    <androidx.appcompat.widget.AppCompatButton
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/btPermission"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:layout_marginVertical="@dimen/_20sdp"
        android:elevation="@dimen/_10sdp"
        android:paddingStart="@dimen/_15sdp"
        android:paddingEnd="@dimen/_15sdp"
        android:textAllCaps="false"
        android:background="@drawable/bg_button_app_color"
        android:fontFamily="@font/roboto_semi_medium"
        android:textSize="@dimen/_12sdp"
        android:textColor="@color/white"
        android:text="@string/permission"
        />




</androidx.constraintlayout.widget.ConstraintLayout>
