package com.tech.ekvayu.Fragments

import android.os.Bundle
import android.text.Html
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.tech.ekvayu.databinding.FragmentCommonBinding


class CommonFragment : Fragment() {
    private lateinit var binding: FragmentCommonBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding= FragmentCommonBinding.inflate(inflater, container, false)

        val extraValue = arguments?.getString("pageName")

        if (extraValue=="about")
        {
            aboutContent(extraValue)
        }
        else
        {
            privacyPolicy()
        }


        return binding.root
    }

    private fun aboutContent(extraValue: String)
    {
        val termsHtml = """
    <h2>Terms of Service</h2>
    <h3>Interpretation and Definitions</h3>
    <p>The words of which the initial letter is capitalized have meanings defined under the following conditions. 
    These definitions shall have the same meaning regardless of whether they appear in singular or in plural.</p>

    <ul>
        <li><strong>Company:</strong> ekvayu, JSS Science & Tech Entrepreneurs Park (STEP) C-20/I, Sector 62, NOIDA - 201301, UP.</li>
        <li><strong>Country:</strong> Uttar Pradesh, India</li>
        <li><strong>Service:</strong> refers to the Website accessible from <a href="https://ekvayu.com/">https://ekvayu.com/</a></li>
        <li><strong>Device:</strong> any device that can access the Service such as a computer, cellphone or digital tablet.</li>
    </ul>

    <h3>Acknowledgment</h3>
    <p>By accessing or using the Service You agree to be bound by these Terms and Conditions. If You disagree with any part 
    of these Terms and Conditions then You may not access the Service. You represent that you are over the age of 18.</p>

    <h3>Links to Other Websites</h3>
    <p>Our Service may contain links to third-party websites or services that are not owned or controlled by the Company. 
    The Company assumes no responsibility for third-party content, privacy policies, or practices.</p>

    <h3>Limitation of Liability</h3>
    <p>The entire liability of the Company shall be limited to the amount actually paid by You through the Service 
    or 100 USD if You haven't purchased anything through the Service.</p>

    <h3>Governing Law</h3>
    <p>The laws of Uttar Pradesh, India, excluding its conflicts of law rules, shall govern these Terms and Your use of the Service.</p>

    <h3>Contact Information</h3>
    <p>For questions about these Terms and Conditions, contact us:</p>
    <ul>
        <li><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></li>
        <li><strong>Phone:</strong> +************</li>
    </ul>

    <p><em>Last updated: November 11, 2024</em></p>
""".trimIndent()

        binding.tvContent.text = Html.fromHtml(termsHtml, Html.FROM_HTML_MODE_LEGACY)
        binding.tvContent.linksClickable = true

    }

    private fun privacyPolicy()
    {

        val privacyPolicyHtml = """
    <h2>Privacy Policy</h2>

    <h3>Interpretation and Definitions</h3>
    <p>The words with capitalized initial letters have specific meanings under the following conditions.</p>
    <ul>
        <li><strong>Company:</strong> ekvayu (JSS Science & Tech Entrepreneurs Park, NOIDA)</li>
        <li><strong>Personal Data:</strong> Any information relating to an identified or identifiable individual</li>
        <li><strong>Service:</strong> Refers to the Website (<a href="https://ekvayu.com/">https://ekvayu.com/</a>)</li>
        <li><strong>Usage Data:</strong> Data collected automatically from Service use</li>
    </ul>

    <h3>Types of Data Collected</h3>
    <h4>Personal Data</h4>
    <ul>
        <li>Email address</li>
        <li>Phone number</li>
    </ul>

    <h4>Usage Data</h4>

    <h3>Tracking Technologies and Cookies</h3>
    <p>We use Cookies and similar tracking technologies including:</p>
    <ul>
        <li><strong>Session Cookies:</strong> Essential for Service functionality</li>
        <li><strong>Persistent Cookies:</strong> For user preferences and functionality</li>
        <li><strong>Web Beacons:</strong> For analytics and system verification</li>
    </ul>

    <h3>Use of Your Personal Data</h3>
    <ul>
        <li>To provide and maintain our Service</li>
        <li>To manage your Account</li>
        <li>To contact you about updates and communications</li>
        <li>To provide news and offers about similar services</li>
        <li>For business transfers and analysis</li>
    </ul>

    <h3>Security of Your Personal Data</h3>
    <p>While we implement commercially acceptable means to protect your Personal Data, 
    no method of transmission over the Internet or electronic storage is 100% secure.</p>

    <h3>Children's Privacy</h3>
    <p>Our Service does not address anyone under the age of 13. 
    We do not knowingly collect personal information from anyone under 13 years old.</p>

    <h3>Changes to this Privacy Policy</h3>
    <p>We may update our Privacy Policy periodically. 
    We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last updated" date.</p>

    <h3>Contact Us</h3>
    <p>For questions about this Privacy Policy, contact us:</p>
    <ul>
        <li><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></li>
        <li><strong>Phone:</strong> +************</li>
    </ul>

    <p><em>Last updated: November 11, 2024</em></p>
""".trimIndent()

        binding.tvContent.text = Html.fromHtml(privacyPolicyHtml, Html.FROM_HTML_MODE_LEGACY)
        binding.tvContent.linksClickable = true

    }


}