package com.tech.ekvayu.BaseClass

import android.util.Log
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import com.tech.ekvayu.R
import java.util.*

class FragmentNavigationManager(
    private val fragmentManager: FragmentManager,
    private val containerId: Int
) {
    
    companion object {
        private const val TAG = "FragmentNavManager"
        private const val HOME_FRAGMENT_TAG = "HOME_FRAGMENT"
    }
    
    // Stack to track fragment navigation
    private val fragmentStack = Stack<FragmentInfo>()
    
    // Current fragment reference
    private var currentFragment: Fragment? = null
    
    data class FragmentInfo(
        val fragment: Fragment,
        val tag: String,
        val addToBackStack: Boolean = true
    )
    
    /**
     * Initialize with home fragment
     */
    fun initializeWithHome(homeFragment: Fragment) {
        Log.d(TAG, "🏠 Initializing with home fragment")
        
        // Clear any existing fragments
        clearAllFragments()
        
        // Add home fragment
        val transaction = fragmentManager.beginTransaction()
        transaction.replace(containerId, homeFragment, HOME_FRAGMENT_TAG)
        transaction.commit()
        
        // Set as current and add to stack
        currentFragment = homeFragment
        fragmentStack.clear()
        fragmentStack.push(FragmentInfo(homeFragment, HOME_FRAGMENT_TAG, false))
        
        Log.d(TAG, "✅ Home fragment initialized. Stack size: ${fragmentStack.size}")
    }
    
    /**
     * Navigate to a new fragment
     */
    fun navigateToFragment(
        fragment: Fragment,
        tag: String? = null,
        addToBackStack: Boolean = true,
        clearStack: Boolean = false,
        useAnimation: Boolean = true
    ) {
        try {
            val fragmentTag = tag ?: fragment.javaClass.simpleName
            
            Log.d(TAG, "🧭 Navigating to fragment: $fragmentTag")
            Log.d(TAG, "📚 Current stack size: ${fragmentStack.size}")
            
            // Clear stack if requested (useful for main navigation)
            if (clearStack) {
                clearBackStack()
            }
            
            val transaction = fragmentManager.beginTransaction()
            
            // Add animations if requested
            if (useAnimation) {
                transaction.setCustomAnimations(
                    R.anim.slide_in_right,
                    R.anim.slide_out_left,
                    R.anim.slide_in_left,
                    R.anim.slide_out_right
                )
            }
            
            // Replace fragment
            transaction.replace(containerId, fragment, fragmentTag)
            
            // Add to back stack if requested
            if (addToBackStack) {
                transaction.addToBackStack(fragmentTag)
            }
            
            transaction.commit()
            
            // Update current fragment
            currentFragment = fragment
            
            // Add to our custom stack
            if (addToBackStack) {
                fragmentStack.push(FragmentInfo(fragment, fragmentTag, true))
            }
            
            Log.d(TAG, "✅ Navigation completed. New stack size: ${fragmentStack.size}")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error navigating to fragment: ${e.message}", e)
        }
    }
    
    /**
     * Navigate back to previous fragment
     */
    fun navigateBack(): Boolean {
        Log.d(TAG, "⬅️ Attempting to navigate back. Stack size: ${fragmentStack.size}")
        
        return try {
            // If we have more than just home fragment in stack
            if (fragmentStack.size > 1) {
                // Remove current fragment from stack
                val currentFragmentInfo = fragmentStack.pop()
                Log.d(TAG, "📤 Removing from stack: ${currentFragmentInfo.tag}")
                
                // Pop from fragment manager back stack
                if (fragmentManager.backStackEntryCount > 0) {
                    fragmentManager.popBackStack()
                }
                
                // Update current fragment reference
                if (fragmentStack.isNotEmpty()) {
                    currentFragment = fragmentStack.peek().fragment
                    Log.d(TAG, "📍 Current fragment now: ${fragmentStack.peek().tag}")
                }
                
                Log.d(TAG, "✅ Back navigation completed. New stack size: ${fragmentStack.size}")
                true
            } else {
                Log.d(TAG, "🏠 Already at home fragment, cannot go back further")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error during back navigation: ${e.message}", e)
            false
        }
    }
    
    /**
     * Navigate back to home fragment
     */
    fun navigateToHome() {
        Log.d(TAG, "🏠 Navigating back to home")
        
        try {
            // Clear all back stack
            clearBackStack()
            
            // If home fragment exists in stack, navigate to it
            if (fragmentStack.isNotEmpty()) {
                val homeFragmentInfo = fragmentStack.firstOrNull { it.tag == HOME_FRAGMENT_TAG }
                if (homeFragmentInfo != null) {
                    currentFragment = homeFragmentInfo.fragment
                    
                    val transaction = fragmentManager.beginTransaction()
                    transaction.replace(containerId, homeFragmentInfo.fragment, HOME_FRAGMENT_TAG)
                    transaction.commit()
                    
                    // Reset stack to only home
                    fragmentStack.clear()
                    fragmentStack.push(homeFragmentInfo)
                    
                    Log.d(TAG, "✅ Successfully navigated to home")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error navigating to home: ${e.message}", e)
        }
    }
    
    /**
     * Navigate to specific fragment by tag
     */
    fun navigateToFragmentByTag(tag: String): Boolean {
        Log.d(TAG, "🔍 Searching for fragment with tag: $tag")
        
        return try {
            val fragmentInfo = fragmentStack.find { it.tag == tag }
            if (fragmentInfo != null) {
                // Clear stack up to this fragment
                while (fragmentStack.isNotEmpty() && fragmentStack.peek().tag != tag) {
                    fragmentStack.pop()
                    if (fragmentManager.backStackEntryCount > 0) {
                        fragmentManager.popBackStack()
                    }
                }
                
                currentFragment = fragmentInfo.fragment
                Log.d(TAG, "✅ Found and navigated to fragment: $tag")
                true
            } else {
                Log.d(TAG, "❌ Fragment not found in stack: $tag")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error navigating to fragment by tag: ${e.message}", e)
            false
        }
    }
    
    /**
     * Clear all fragments except home
     */
    private fun clearBackStack() {
        try {
            // Clear fragment manager back stack
            while (fragmentManager.backStackEntryCount > 0) {
                fragmentManager.popBackStackImmediate()
            }
            
            Log.d(TAG, "🧹 Back stack cleared")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error clearing back stack: ${e.message}", e)
        }
    }
    
    /**
     * Clear all fragments
     */
    private fun clearAllFragments() {
        try {
            clearBackStack()
            fragmentStack.clear()
            currentFragment = null
            Log.d(TAG, "🧹 All fragments cleared")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error clearing all fragments: ${e.message}", e)
        }
    }
    
    /**
     * Get current fragment
     */
    fun getCurrentFragment(): Fragment? = currentFragment
    
    /**
     * Get current fragment tag
     */
    fun getCurrentFragmentTag(): String? {
        return if (fragmentStack.isNotEmpty()) {
            fragmentStack.peek().tag
        } else null
    }
    
    /**
     * Check if we can go back
     */
    fun canGoBack(): Boolean = fragmentStack.size > 1
    
    /**
     * Get fragment stack size
     */
    fun getStackSize(): Int = fragmentStack.size
    
    /**
     * Get fragment stack info for debugging
     */
    fun getStackInfo(): String {
        return buildString {
            appendLine("📚 Fragment Stack Info:")
            appendLine("Stack Size: ${fragmentStack.size}")
            appendLine("Can Go Back: ${canGoBack()}")
            appendLine("Current Fragment: ${getCurrentFragmentTag()}")
            appendLine("Stack Contents:")
            fragmentStack.forEachIndexed { index, info ->
                appendLine("  $index: ${info.tag} (backStack: ${info.addToBackStack})")
            }
        }
    }
}
