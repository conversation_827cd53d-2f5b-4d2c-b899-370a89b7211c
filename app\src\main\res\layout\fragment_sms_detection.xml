<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/swipeRefresh"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_sms_detection"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvEmptyState"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_semi_medium"
            android:gravity="center"
            android:visibility="gone"
            android:text="No spam sms found.\nYour sms message are safe! 🛡️"
            android:textColor="@color/text_secondary"
            android:textSize="@dimen/_14sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />



    </androidx.constraintlayout.widget.ConstraintLayout>



</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
