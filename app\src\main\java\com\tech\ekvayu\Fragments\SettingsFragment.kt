package com.tech.ekvayu.Fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment

import com.tech.ekvayu.databinding.FragmentSettingsBinding

class SettingsFragment : Fragment() {
    
    private var _binding: FragmentSettingsBinding? = null
    private val binding get() = _binding!!
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSettingsBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupClickListeners()
    }
    
    private fun setupClickListeners() {
        // Theme settings click listener
        binding.llThemeSettings.setOnClickListener {
            showThemeSettingsBottomSheet()
        }
    }
    
    private fun showThemeSettingsBottomSheet() {
        // Theme settings functionality removed
        // Use the theme toggle in the header instead
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
