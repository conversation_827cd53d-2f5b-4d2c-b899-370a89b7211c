package com.tech.ekvayu.BottomSheet

import android.accessibilityservice.AccessibilityService
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.PermissionHelper
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.EkService.NewGmailAccessibilityService
import com.tech.ekvayu.R
import com.tech.ekvayu.databinding.FragmentWarningBottomSheetBinding


class WarningBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentWarningBottomSheetBinding
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    private val TAG = "WarningBottomSheet"

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding= FragmentWarningBottomSheetBinding.inflate(inflater, container, false)

        // Log device information for debugging
        Log.d(TAG, "Device Info: ${PermissionHelper.getDeviceInfo()}")

        binding.btPermission.setOnClickListener {
            Log.d(TAG, "Permission button clicked - opening accessibility settings")
            PermissionHelper.openAccessibilitySettings(requireContext())
        }

        return binding.root
    }

    override fun onResume() {
        super.onResume()
        // Check if accessibility permission is granted when fragment resumes
        checkAndUpdatePermissionStatus()
    }

    private fun checkAndUpdatePermissionStatus() {
        Log.d(TAG, "Checking accessibility permission status")

        val isEnabled = PermissionHelper.checkAndUpdateAccessibilityPermission(requireContext())

        if (isEnabled) {
            Log.d(TAG, "Accessibility permission is granted - dismissing bottom sheet")
            // Permission is granted, dismiss bottom sheet
            dismiss()
        } else {
            Log.d(TAG, "Accessibility permission is not granted")
        }
    }
}