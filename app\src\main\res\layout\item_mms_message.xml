<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/card_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tvSender"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Sender"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:ellipsize="end"
                android:maxLines="1" />

            <ImageView
                android:id="@+id/ivAttachment"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_attachment"
                android:layout_marginEnd="8dp"
                android:visibility="gone"
                app:tint="@color/text_secondary" />

            <ImageView
                android:id="@+id/ivThreatIcon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_warning"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Subject Row -->
        <TextView
            android:id="@+id/tvSubject"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Subject"
            android:textSize="14sp"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="8dp"
            android:ellipsize="end"
            android:maxLines="2" />

        <!-- Info Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tvDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Date"
                android:textSize="12sp"
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/tvSize"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Size"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/tvPartsCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Parts"
                android:textSize="12sp"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

        <!-- Threat Info Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="8dp">

            <TextView
                android:id="@+id/tvThreatLevel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Threat Level"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/tvScore"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Score"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/warning"
                android:background="@drawable/rounded_background"
                android:backgroundTint="@color/warning_light"
                android:padding="4dp"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
