<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/swipeRefreshLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginBottom="24dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Analytics Dashboard"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:gravity="center" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Pull down to refresh"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:gravity="center"
                    android:layout_marginTop="4dp"
                    android:alpha="0.7" />

            </LinearLayout>

        <!-- Quick Stats Cards Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="@dimen/_10sdp">

            <!-- Total Emails Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:layout_margin="@dimen/_3sdp"
                android:elevation="0dp"
                app:cardMaxElevation="@dimen/_3sdp"
                app:cardCornerRadius="@dimen/analytics_card_corner_radius"
                app:cardElevation="@dimen/analytics_card_elevation_medium"
                app:cardBackgroundColor="@color/card_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <TextView
                        android:id="@+id/tvTotalEmails"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/blue" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Total Emails"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Spam Emails Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:layout_margin="@dimen/_3sdp"
                android:elevation="0dp"
                app:cardMaxElevation="@dimen/_3sdp"
                app:cardCornerRadius="@dimen/analytics_card_corner_radius"
                app:cardElevation="@dimen/analytics_card_elevation_medium"
                app:cardBackgroundColor="@color/card_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <TextView
                        android:id="@+id/tvSpamEmails"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/red" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Spam Emails"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- Second Row of Stats -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="24dp">

            <!-- SMS Threats Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:layout_margin="@dimen/_3sdp"
                android:elevation="0dp"
                app:cardMaxElevation="@dimen/_3sdp"
                app:cardCornerRadius="@dimen/analytics_card_corner_radius"
                app:cardElevation="@dimen/analytics_card_elevation_medium"
                app:cardBackgroundColor="@color/card_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <TextView
                        android:id="@+id/tvSmsThreats"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/orange" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="SMS Threats"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Disputes Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:layout_margin="@dimen/_3sdp"
                android:elevation="0dp"
                app:cardMaxElevation="@dimen/_3sdp"
                app:cardCornerRadius="@dimen/analytics_card_corner_radius"
                app:cardElevation="@dimen/analytics_card_elevation_medium"
                app:cardBackgroundColor="@color/card_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <TextView
                        android:id="@+id/tvDisputes"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/purple" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Disputes"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- Email Detection Chart Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="300dp"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="@dimen/analytics_card_corner_radius"
            app:cardElevation="@dimen/analytics_card_elevation_large"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Email Detection Overview"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp" />

                <com.github.mikephil.charting.charts.PieChart
                    android:id="@+id/pieChartEmails"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Device Security Status -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="@dimen/analytics_card_corner_radius"
            app:cardElevation="@dimen/analytics_card_elevation_large"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Device Security Status"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp" />

                <!-- Accessibility Services Status -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="12dp">

                    <!-- Gmail Service -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="📧 Gmail Detection"
                            android:textSize="14sp"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:id="@+id/tvGmailStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="❌ Disabled"
                            android:textSize="12sp"
                            android:textColor="@color/red"
                            android:background="@drawable/status_badge_background"
                            android:padding="6dp" />

                    </LinearLayout>

                    <!-- SMS Service -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="📱 SMS Detection"
                            android:textSize="14sp"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:id="@+id/tvSmsStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="❌ Disabled"
                            android:textSize="12sp"
                            android:textColor="@color/red"
                            android:background="@drawable/status_badge_background"
                            android:padding="6dp" />

                    </LinearLayout>

                    <!-- Yahoo Service -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="🟡 Yahoo Detection"
                            android:textSize="14sp"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:id="@+id/tvYahooStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="❌ Disabled"
                            android:textSize="12sp"
                            android:textColor="@color/red"
                            android:background="@drawable/status_badge_background"
                            android:padding="6dp" />

                    </LinearLayout>

                    <!-- Outlook Service -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="🔵 Outlook Detection"
                            android:textSize="14sp"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:id="@+id/tvOutlookStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="❌ Disabled"
                            android:textSize="12sp"
                            android:textColor="@color/red"
                            android:background="@drawable/status_badge_background"
                            android:padding="6dp" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Device Information Summary -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="@dimen/analytics_card_corner_radius"
            app:cardElevation="@dimen/analytics_card_elevation_large"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Device Information"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Device Model:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvDeviceModel"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Loading..."
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Android Version:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvAndroidVersion"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Loading..."
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Available Memory:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvAvailableMemory"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Loading..."
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Storage Used:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvStorageUsed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Loading..."
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Recent Activity Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="@dimen/analytics_card_corner_radius"
            app:cardElevation="@dimen/analytics_card_elevation_large"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Recent Activity"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/tvLastUpdated"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Last updated: Never"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvRecentActivity"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    tools:listitem="@layout/item_recent_activity" />

                <TextView
                    android:id="@+id/tvNoActivity"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="No recent activity to display"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:gravity="center"
                    android:padding="20dp"
                    android:visibility="gone" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
