package com.tech.ekvayu.Fragments

import android.Manifest
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.provider.Telephony
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.tech.ekvayu.BaseClass.BaseFragment
import com.tech.ekvayu.databinding.FragmentMmsDetectionBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class MmsDetectionFragment : BaseFragment() {
    
    private var _binding: FragmentMmsDetectionBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var mmsAdapter: MmsAdapter
    private val mmsList = mutableListOf<MmsMessage>()
    
    private val PERMISSION_REQUEST_CODE = 123
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMmsDetectionBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupUI()
        checkPermissionsAndLoadMms()
    }
    
    private fun setupUI() {
        // Setup RecyclerView
        mmsAdapter = MmsAdapter(mmsList) { mms ->
            // Handle MMS item click
            showMmsDetails(mms)
        }
        
        binding.recyclerViewMms.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = mmsAdapter
        }
        
        // Setup refresh button
        binding.btnRefresh.setOnClickListener {
            checkPermissionsAndLoadMms()
        }
        
        // Setup scan button
        binding.btnScanMms.setOnClickListener {
            scanForSuspiciousMms()
        }
        
        // Setup clear button
        binding.btnClearResults.setOnClickListener {
            clearResults()
        }
    }
    
    private fun checkPermissionsAndLoadMms() {
        if (ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.READ_SMS
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            requestPermissions(
                arrayOf(Manifest.permission.READ_SMS),
                PERMISSION_REQUEST_CODE
            )
        } else {
            loadMmsMessages()
        }
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                loadMmsMessages()
            } else {
                Toast.makeText(
                    requireContext(),
                    "SMS permission is required to detect MMS messages",
                    Toast.LENGTH_LONG
                ).show()
            }
        }
    }
    
    private fun loadMmsMessages() {
        binding.progressBar.visibility = View.VISIBLE
        binding.tvStatus.text = "Loading MMS messages..."
        
        lifecycleScope.launch {
            try {
                val messages = withContext(Dispatchers.IO) {
                    getMmsMessages()
                }
                
                withContext(Dispatchers.Main) {
                    mmsList.clear()
                    mmsList.addAll(messages)
                    mmsAdapter.notifyDataSetChanged()
                    
                    updateUI()
                    binding.progressBar.visibility = View.GONE
                    binding.tvStatus.text = "Loaded ${messages.size} MMS messages"
                }
                
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    binding.tvStatus.text = "Error loading MMS messages: ${e.message}"
                    Log.e(TAG, "Error loading MMS", e)
                }
            }
        }
    }
    
    private fun getMmsMessages(): List<MmsMessage> {
        val messages = mutableListOf<MmsMessage>()
        
        try {
            val uri = Telephony.Mms.CONTENT_URI
            val projection = arrayOf(
                Telephony.Mms._ID,
                Telephony.Mms.DATE,
                Telephony.Mms.MESSAGE_BOX,
                Telephony.Mms.READ,
                Telephony.Mms.SUBJECT,
                Telephony.Mms.MESSAGE_SIZE
            )
            
            val cursor: Cursor? = requireContext().contentResolver.query(
                uri,
                projection,
                null,
                null,
                "${Telephony.Mms.DATE} DESC LIMIT 100"
            )
            
            cursor?.use {
                while (it.moveToNext()) {
                    val id = it.getLong(it.getColumnIndexOrThrow(Telephony.Mms._ID))
                    val date = it.getLong(it.getColumnIndexOrThrow(Telephony.Mms.DATE)) * 1000
                    val messageBox = it.getInt(it.getColumnIndexOrThrow(Telephony.Mms.MESSAGE_BOX))
                    val isRead = it.getInt(it.getColumnIndexOrThrow(Telephony.Mms.READ)) == 1
                    val subject = it.getString(it.getColumnIndexOrThrow(Telephony.Mms.SUBJECT)) ?: ""
                    val messageSize = it.getInt(it.getColumnIndexOrThrow(Telephony.Mms.MESSAGE_SIZE))
                    
                    // Get sender/recipient address
                    val address = getMmsAddress(id, messageBox)
                    
                    // Get message parts (text, images, etc.)
                    val parts = getMmsParts(id)
                    
                    val mms = MmsMessage(
                        id = id,
                        address = address,
                        subject = subject,
                        date = date,
                        isRead = isRead,
                        messageBox = messageBox,
                        messageSize = messageSize,
                        parts = parts,
                        suspiciousScore = 0,
                        suspiciousReasons = emptyList()
                    )
                    
                    messages.add(mms)
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error reading MMS messages", e)
        }
        
        return messages
    }
    
    private fun getMmsAddress(mmsId: Long, messageBox: Int): String {
        try {
            val uri = Uri.parse("content://mms/$mmsId/addr")
            val projection = arrayOf("address", "type")
            
            val cursor = requireContext().contentResolver.query(uri, projection, null, null, null)
            cursor?.use {
                while (it.moveToNext()) {
                    val address = it.getString(0) ?: ""
                    val type = it.getInt(1)
                    
                    // Type 137 = FROM, Type 151 = TO
                    if ((messageBox == Telephony.Mms.MESSAGE_BOX_INBOX && type == 137) ||
                        (messageBox == Telephony.Mms.MESSAGE_BOX_SENT && type == 151)) {
                        return address
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting MMS address", e)
        }
        return "Unknown"
    }
    
    private fun getMmsParts(mmsId: Long): List<MmsPart> {
        val parts = mutableListOf<MmsPart>()
        
        try {
            val uri = Uri.parse("content://mms/part")
            val projection = arrayOf("_id", "ct", "text", "_data", "name")
            val selection = "mid=?"
            val selectionArgs = arrayOf(mmsId.toString())
            
            val cursor = requireContext().contentResolver.query(
                uri, projection, selection, selectionArgs, null
            )
            
            cursor?.use {
                while (it.moveToNext()) {
                    val partId = it.getLong(0)
                    val contentType = it.getString(1) ?: ""
                    val text = it.getString(2) ?: ""
                    val data = it.getString(3) ?: ""
                    val name = it.getString(4) ?: ""
                    
                    parts.add(MmsPart(partId, contentType, text, data, name))
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error getting MMS parts", e)
        }
        
        return parts
    }

    private fun scanForSuspiciousMms() {
        binding.progressBar.visibility = View.VISIBLE
        binding.tvStatus.text = "Scanning for suspicious MMS..."

        lifecycleScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    mmsList.forEach { mms ->
                        val (score, reasons) = analyzeMmsForThreats(mms)
                        mms.suspiciousScore = score
                        mms.suspiciousReasons = reasons
                    }
                }

                withContext(Dispatchers.Main) {
                    // Sort by suspicious score (highest first)
                    mmsList.sortByDescending { it.suspiciousScore }
                    mmsAdapter.notifyDataSetChanged()

                    val suspiciousCount = mmsList.count { it.suspiciousScore > 50 }
                    binding.progressBar.visibility = View.GONE
                    binding.tvStatus.text = "Scan complete. Found $suspiciousCount suspicious MMS messages"

                    updateUI()
                }

            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    binding.tvStatus.text = "Error during scan: ${e.message}"
                    Log.e(TAG, "Error scanning MMS", e)
                }
            }
        }
    }

    private fun analyzeMmsForThreats(mms: MmsMessage): Pair<Int, List<String>> {
        var score = 0
        val reasons = mutableListOf<String>()

        // Check for suspicious sender patterns
        if (mms.address.matches(Regex("\\+?\\d{10,15}"))) {
            if (mms.address.startsWith("+1") && mms.address.length > 12) {
                score += 20
                reasons.add("International number format")
            }
        } else if (mms.address.contains("@")) {
            score += 15
            reasons.add("Email-like sender address")
        }

        // Check subject for suspicious content
        val suspiciousSubjects = listOf(
            "urgent", "winner", "congratulations", "prize", "free", "click",
            "limited time", "act now", "verify", "suspended", "blocked"
        )

        suspiciousSubjects.forEach { keyword ->
            if (mms.subject.contains(keyword, ignoreCase = true)) {
                score += 25
                reasons.add("Suspicious subject: contains '$keyword'")
            }
        }

        // Check message parts for threats
        mms.parts.forEach { part ->
            // Check for executable attachments
            if (part.contentType.contains("application/") &&
                (part.name.endsWith(".apk") || part.name.endsWith(".exe") ||
                 part.name.endsWith(".bat") || part.name.endsWith(".scr"))) {
                score += 50
                reasons.add("Suspicious attachment: ${part.name}")
            }

            // Check text content for phishing patterns
            val phishingPatterns = listOf(
                "click.*link", "verify.*account", "update.*payment", "suspended.*account",
                "winner.*lottery", "claim.*prize", "urgent.*action", "limited.*offer"
            )

            phishingPatterns.forEach { pattern ->
                if (part.text.contains(Regex(pattern, RegexOption.IGNORE_CASE))) {
                    score += 30
                    reasons.add("Phishing pattern detected: $pattern")
                }
            }

            // Check for suspicious URLs
            val urlPattern = Regex("https?://[\\w.-]+")
            val urls = urlPattern.findAll(part.text).map { it.value }
            urls.forEach { url ->
                if (isSuspiciousUrl(url)) {
                    score += 35
                    reasons.add("Suspicious URL: $url")
                }
            }
        }

        // Check message size (unusually large MMS might contain malware)
        if (mms.messageSize > 5 * 1024 * 1024) { // 5MB
            score += 20
            reasons.add("Unusually large message size")
        }

        return Pair(score.coerceAtMost(100), reasons)
    }

    private fun isSuspiciousUrl(url: String): Boolean {
        val suspiciousDomains = listOf(
            "bit.ly", "tinyurl.com", "t.co", "goo.gl", "ow.ly",
            "short.link", "tiny.cc", "is.gd", "buff.ly"
        )

        val suspiciousPatterns = listOf(
            "secure.*update", "verify.*account", "login.*here", "click.*now",
            "free.*download", "winner.*claim", "urgent.*action"
        )

        // Check for suspicious domains
        suspiciousDomains.forEach { domain ->
            if (url.contains(domain, ignoreCase = true)) {
                return true
            }
        }

        // Check for suspicious patterns in URL
        suspiciousPatterns.forEach { pattern ->
            if (url.contains(Regex(pattern, RegexOption.IGNORE_CASE))) {
                return true
            }
        }

        return false
    }

    private fun showMmsDetails(mms: MmsMessage) {
        val details = buildString {
            appendLine("MMS Details:")
            appendLine("From: ${mms.address}")
            appendLine("Subject: ${mms.subject}")
            appendLine("Date: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(mms.date))}")
            appendLine("Size: ${mms.messageSize} bytes")
            appendLine("Parts: ${mms.parts.size}")

            if (mms.suspiciousScore > 0) {
                appendLine("\nSuspicious Score: ${mms.suspiciousScore}/100")
                appendLine("Reasons:")
                mms.suspiciousReasons.forEach { reason ->
                    appendLine("• $reason")
                }
            }

            appendLine("\nMessage Parts:")
            mms.parts.forEach { part ->
                appendLine("• Type: ${part.contentType}")
                if (part.name.isNotEmpty()) appendLine("  Name: ${part.name}")
                if (part.text.isNotEmpty()) appendLine("  Text: ${part.text.take(100)}...")
            }
        }

        android.app.AlertDialog.Builder(requireContext())
            .setTitle("MMS Details")
            .setMessage(details)
            .setPositiveButton("OK", null)
            .show()
    }

    private fun clearResults() {
        mmsList.forEach { mms ->
            mms.suspiciousScore = 0
            mms.suspiciousReasons = emptyList()
        }
        mmsAdapter.notifyDataSetChanged()
        updateUI()
        binding.tvStatus.text = "Results cleared"
    }

    private fun updateUI() {
        val totalMms = mmsList.size
        val suspiciousMms = mmsList.count { it.suspiciousScore > 50 }
        val moderateMms = mmsList.count { it.suspiciousScore in 25..50 }

        binding.tvTotalMms.text = totalMms.toString()
        binding.tvSuspiciousMms.text = suspiciousMms.toString()
        binding.tvModerateMms.text = moderateMms.toString()

        // Show/hide empty state
        if (totalMms == 0) {
            binding.layoutEmptyState.visibility = View.VISIBLE
            binding.recyclerViewMms.visibility = View.GONE
        } else {
            binding.layoutEmptyState.visibility = View.GONE
            binding.recyclerViewMms.visibility = View.VISIBLE
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
