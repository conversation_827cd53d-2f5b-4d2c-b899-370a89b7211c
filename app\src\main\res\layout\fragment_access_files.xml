<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:fillViewport="true"
    tools:context=".Fragments.AccessFilesFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Malicious File Scanner"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <!-- Statistics Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Scan Results"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="4">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tvTotalFiles"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/app_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Total"
                            android:textSize="10sp"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tvHighThreat"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/holo_red_dark" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="High"
                            android:textSize="10sp"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tvMediumThreat"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/holo_orange_dark" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Medium"
                            android:textSize="10sp"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tvLowThreat"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/holo_orange_light" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Low"
                            android:textSize="10sp"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Control Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/btnScanFiles"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Scan Files"
                android:layout_marginEnd="8dp"
                style="@style/Widget.Material3.Button" />

            <Button
                android:id="@+id/btnRefresh"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Refresh"
                android:layout_marginStart="8dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/btnClearResults"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Clear Results"
                android:layout_marginEnd="8dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <Button
                android:id="@+id/btnDeleteSelected"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Delete Selected"
                android:layout_marginStart="8dp"
                android:backgroundTint="@color/error"
                style="@style/Widget.Material3.Button" />

        </LinearLayout>

        <!-- Status and Progress -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="16dp">

            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                style="?android:attr/progressBarStyleHorizontal"
                android:indeterminate="true" />

            <TextView
                android:id="@+id/tvStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Ready to scan device for malicious files"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:gravity="center"
                android:layout_marginTop="8dp" />

        </LinearLayout>

        <!-- Malicious Files List -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Detected Malicious Files"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:padding="16dp"
                    android:paddingBottom="8dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewMaliciousFiles"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="8dp"
                    tools:listitem="@layout/item_malicious_file" />

                <!-- Empty State -->
                <LinearLayout
                    android:id="@+id/layoutEmptyState"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="32dp"
                    android:visibility="visible">

                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:src="@drawable/ic_folder_scan"
                        android:alpha="0.5"
                        app:tint="@color/text_secondary"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="No Malicious Files Found"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Tap 'Scan Files' to start scanning your device for potentially malicious files"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:gravity="center" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
