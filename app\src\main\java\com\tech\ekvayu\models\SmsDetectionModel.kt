package com.tech.ekvayu.models

import com.tech.ekvayu.BaseClass.SmsDetectionHelper

data class SmsDetectionModel(
    val sender: String,
    val body: String,
    val timestamp: Long,
    val isSuspicious: Boolean,
    val riskLevel: SmsDetectionHelper.RiskLevel,
    val riskScore: Int,
    val detectedPatterns: List<String>,
    val suspiciousKeywords: List<String>,
    val recommendations: List<String>,
    val components: SmsDetectionHelper.SmsComponents
)
