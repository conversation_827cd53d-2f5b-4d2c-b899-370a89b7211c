package com.tech.ekvayu.Fragments

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.tech.ekvayu.R

class MmsAdapter(
    private val mmsList: List<MmsMessage>,
    private val onItemClick: (MmsMessage) -> Unit
) : RecyclerView.Adapter<MmsAdapter.MmsViewHolder>() {

    class MmsViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvSender: TextView = itemView.findViewById(R.id.tvSender)
        val tvSubject: TextView = itemView.findViewById(R.id.tvSubject)
        val tvDate: TextView = itemView.findViewById(R.id.tvDate)
        val tvSize: TextView = itemView.findViewById(R.id.tvSize)
        val tvThreatLevel: TextView = itemView.findViewById(R.id.tvThreatLevel)
        val tvScore: TextView = itemView.findViewById(R.id.tvScore)
        val ivThreatIcon: ImageView = itemView.findViewById(R.id.ivThreatIcon)
        val ivAttachment: ImageView = itemView.findViewById(R.id.ivAttachment)
        val tvPartsCount: TextView = itemView.findViewById(R.id.tvPartsCount)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MmsViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_mms_message, parent, false)
        return MmsViewHolder(view)
    }

    override fun onBindViewHolder(holder: MmsViewHolder, position: Int) {
        val mms = mmsList[position]
        
        holder.tvSender.text = if (mms.address.isNotEmpty()) mms.address else "Unknown"
        holder.tvSubject.text = if (mms.subject.isNotEmpty()) mms.subject else "No Subject"
        holder.tvDate.text = mms.getFormattedDate()
        holder.tvSize.text = mms.getFormattedSize()
        holder.tvPartsCount.text = "${mms.parts.size} parts"
        
        // Show attachment icon if MMS has attachments
        if (mms.hasAttachments()) {
            holder.ivAttachment.visibility = View.VISIBLE
        } else {
            holder.ivAttachment.visibility = View.GONE
        }
        
        // Set threat level and score
        val threatLevel = mms.getThreatLevel()
        holder.tvThreatLevel.text = threatLevel.displayName
        holder.tvThreatLevel.setTextColor(
            ContextCompat.getColor(holder.itemView.context, threatLevel.colorRes)
        )
        
        if (mms.suspiciousScore > 0) {
            holder.tvScore.text = "${mms.suspiciousScore}/100"
            holder.tvScore.visibility = View.VISIBLE
            holder.ivThreatIcon.visibility = View.VISIBLE
            
            // Set threat icon based on level
            when (threatLevel) {
                ThreatLevel.HIGH -> {
                    holder.ivThreatIcon.setImageResource(R.drawable.ic_warning)
                    holder.ivThreatIcon.setColorFilter(
                        ContextCompat.getColor(holder.itemView.context, android.R.color.holo_red_dark)
                    )
                }
                ThreatLevel.MEDIUM -> {
                    holder.ivThreatIcon.setImageResource(R.drawable.ic_warning)
                    holder.ivThreatIcon.setColorFilter(
                        ContextCompat.getColor(holder.itemView.context, android.R.color.holo_orange_dark)
                    )
                }
                ThreatLevel.LOW -> {
                    holder.ivThreatIcon.setImageResource(R.drawable.ic_info)
                    holder.ivThreatIcon.setColorFilter(
                        ContextCompat.getColor(holder.itemView.context, android.R.color.holo_orange_light)
                    )
                }
                ThreatLevel.SAFE -> {
                    holder.ivThreatIcon.setImageResource(R.drawable.ic_check)
                    holder.ivThreatIcon.setColorFilter(
                        ContextCompat.getColor(holder.itemView.context, android.R.color.holo_green_light)
                    )
                }
            }
        } else {
            holder.tvScore.visibility = View.GONE
            holder.ivThreatIcon.visibility = View.GONE
            holder.tvThreatLevel.text = "Not Scanned"
            holder.tvThreatLevel.setTextColor(
                ContextCompat.getColor(holder.itemView.context, android.R.color.darker_gray)
            )
        }
        
        // Set click listener
        holder.itemView.setOnClickListener {
            onItemClick(mms)
        }
        
        // Highlight unread messages
        if (!mms.isRead) {
            holder.itemView.alpha = 1.0f
            holder.tvSender.setTypeface(null, android.graphics.Typeface.BOLD)
            holder.tvSubject.setTypeface(null, android.graphics.Typeface.BOLD)
        } else {
            holder.itemView.alpha = 0.8f
            holder.tvSender.setTypeface(null, android.graphics.Typeface.NORMAL)
            holder.tvSubject.setTypeface(null, android.graphics.Typeface.NORMAL)
        }
    }

    override fun getItemCount(): Int = mmsList.size
}
