<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/_16sdp">

        <!-- Settings Header -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Settings"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="@dimen/_24sdp"
            android:gravity="center" />

        <!-- Appearance Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Appearance"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="@dimen/_12sdp" />

        <!-- Theme Settings Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_16sdp"
            app:cardCornerRadius="@dimen/_12sdp"
            app:cardElevation="@dimen/_4sdp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:id="@+id/llThemeSettings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="@dimen/_16sdp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground">

                <!-- Theme Icon -->
                <ImageView
                    android:layout_width="@dimen/_24sdp"
                    android:layout_height="@dimen/_24sdp"
                    android:src="@drawable/theme_icon"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="@dimen/_16sdp"
                    app:tint="@color/icon_color" />

                <!-- Theme Text Content -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Theme"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Choose between light, dark, or system theme"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginTop="@dimen/_4sdp" />

                </LinearLayout>

                <!-- Arrow Icon -->
                <ImageView
                    android:layout_width="@dimen/_16sdp"
                    android:layout_height="@dimen/_16sdp"
                    android:src="@drawable/right_arrow"
                    android:layout_gravity="center_vertical"
                    app:tint="@color/icon_color" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Additional Settings Section (for future use) -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="General"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginTop="@dimen/_16sdp"
            android:layout_marginBottom="@dimen/_12sdp" />

        <!-- Placeholder for future settings -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_16sdp"
            app:cardCornerRadius="@dimen/_12sdp"
            app:cardElevation="@dimen/_4sdp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_16sdp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="More settings coming soon..."
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:gravity="center"
                    android:padding="@dimen/_16sdp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
