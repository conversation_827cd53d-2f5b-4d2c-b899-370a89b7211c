<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:fillViewport="true">


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvHeading"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Security Center"
        android:fontFamily="@font/roboto_semi_bold"
        android:textSize="@dimen/_12sdp"
        android:layout_margin="@dimen/_10sdp"
        android:textColor="@color/text_primary"
        android:layout_marginBottom="24dp" />

    <androidx.recyclerview.widget.RecyclerView
        app:layout_constraintTop_toBottomOf="@+id/tvHeading"
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/rvSecurityType"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_marginHorizontal="@dimen/_10sdp"

        />

</androidx.constraintlayout.widget.ConstraintLayout>
