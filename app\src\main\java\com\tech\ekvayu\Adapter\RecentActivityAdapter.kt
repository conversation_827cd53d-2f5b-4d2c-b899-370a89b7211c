package com.tech.ekvayu.Adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.tech.ekvayu.R
import com.tech.ekvayu.databinding.ItemRecentActivityBinding
import com.tech.ekvayu.models.RecentActivity

class RecentActivityAdapter(
    private var activities: MutableList<RecentActivity> = mutableListOf(),
    private val onItemClick: ((RecentActivity) -> Unit)? = null
) : RecyclerView.Adapter<RecentActivityAdapter.ActivityViewHolder>() {

    inner class ActivityViewHolder(val binding: ItemRecentActivityBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ActivityViewHolder {
        val binding = ItemRecentActivityBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ActivityViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ActivityViewHolder, position: Int) {
        val activity = activities[position]

        with(holder.binding) {
            // Set activity icon
            tvActivityIcon.text = activity.icon

            // Set activity details
            tvActivityTitle.text = activity.title
            tvActivityDescription.text = activity.description
            tvActivityTime.text = activity.getTimeAgo()
            tvActivityStatus.text = activity.status.displayName

            // Set status color
            val statusColor = when (activity.status) {
                com.tech.ekvayu.models.ActivityStatus.SUCCESS -> ContextCompat.getColor(holder.itemView.context, R.color.green)
                com.tech.ekvayu.models.ActivityStatus.WARNING -> ContextCompat.getColor(holder.itemView.context, R.color.orange)
                com.tech.ekvayu.models.ActivityStatus.ERROR -> ContextCompat.getColor(holder.itemView.context, R.color.red)
                com.tech.ekvayu.models.ActivityStatus.INFO -> ContextCompat.getColor(holder.itemView.context, R.color.blue)
                com.tech.ekvayu.models.ActivityStatus.BLOCKED -> ContextCompat.getColor(holder.itemView.context, R.color.red)
                com.tech.ekvayu.models.ActivityStatus.ALLOWED -> ContextCompat.getColor(holder.itemView.context, R.color.green)
                com.tech.ekvayu.models.ActivityStatus.PENDING -> ContextCompat.getColor(holder.itemView.context, R.color.orange)
            }

            // Set status background color
            tvActivityStatus.setBackgroundColor(statusColor)

            // Set icon background color based on activity type
            val iconBackgroundColor = when (activity.type) {
                com.tech.ekvayu.models.ActivityType.EMAIL_DETECTED -> ContextCompat.getColor(holder.itemView.context, R.color.blue)
                com.tech.ekvayu.models.ActivityType.SMS_DETECTED -> ContextCompat.getColor(holder.itemView.context, R.color.purple)
                com.tech.ekvayu.models.ActivityType.SPAM_BLOCKED -> ContextCompat.getColor(holder.itemView.context, R.color.red)
                com.tech.ekvayu.models.ActivityType.DISPUTE_RAISED -> ContextCompat.getColor(holder.itemView.context, R.color.orange)
                com.tech.ekvayu.models.ActivityType.SERVICE_ENABLED -> ContextCompat.getColor(holder.itemView.context, R.color.green)
                com.tech.ekvayu.models.ActivityType.SERVICE_DISABLED -> ContextCompat.getColor(holder.itemView.context, R.color.red)
                com.tech.ekvayu.models.ActivityType.PERMISSION_GRANTED -> ContextCompat.getColor(holder.itemView.context, R.color.green)
                com.tech.ekvayu.models.ActivityType.PERMISSION_DENIED -> ContextCompat.getColor(holder.itemView.context, R.color.red)
            }

            // Apply icon background (you might need to create a drawable for this)
            tvActivityIcon.setBackgroundColor(iconBackgroundColor)

            // Set click listener
            root.setOnClickListener {
                onItemClick?.invoke(activity)
            }
        }
    }

    override fun getItemCount() = activities.size

    fun updateActivities(newActivities: List<RecentActivity>) {
        activities.clear()
        activities.addAll(newActivities)
        notifyDataSetChanged()
    }
}
