package com.tech.ekvayu.Adapter

import android.R
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.tech.ekvayu.Sms.SmsSpamList.Results
import com.tech.ekvayu.databinding.ItemSpamSmsBinding


class SmsSpamAdapter(private val items: List<Results>, val listener: OnSpamSmsClickListener) : RecyclerView.Adapter<SmsSpamAdapter.MenuViewHolder>() {

    inner class MenuViewHolder(val binding: ItemSpamSmsBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuViewHolder {
        val binding = ItemSpamSmsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MenuViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MenuViewHolder, position: Int) {
        val item = items[position]

        holder.binding.tvtype.text = item.type
        holder.binding.tvMessage.text = "To: ${item.message}"
        holder.binding.tvDateTime.text = item.timestamp

        when (item.status) {
            "unsafe" -> {
                holder.binding.tvStatus.text = "Unsafe"
                holder.binding.cvStatusBadge.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, com.tech.ekvayu.R.color.red))
                holder.binding.tvStatus.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
            }
            "safe" -> {
                holder.binding.tvStatus.text = "Safe"
                holder.binding.cvStatusBadge.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, com.tech.ekvayu.R.color.green))
                holder.binding.tvStatus.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
            }
            else -> {
                holder.binding.tvStatus.text = "Pending"
                holder.binding.cvStatusBadge.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, com.tech.ekvayu.R.color.orange))
                holder.binding.tvStatus.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
            }
        }

        holder.binding.root.setOnClickListener {
            listener.onSmsClicked(item)
        }
    }

    override fun getItemCount() = items.size

    interface  OnSpamSmsClickListener{
        fun onSmsClicked(item: Results)
    }
}
