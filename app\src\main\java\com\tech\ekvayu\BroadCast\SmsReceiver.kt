package com.tech.ekvayu.BroadCast

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.provider.Telephony
import android.util.Log
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.BaseClass.SmsDetectionHelper
import java.text.SimpleDateFormat
import java.util.*

class SmsReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "SmsReceiver"
        private val processedSms = mutableSetOf<String>()
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null || intent == null) return

        when (intent.action) {
            Telephony.Sms.Intents.SMS_RECEIVED_ACTION -> {
                handleIncomingSms(context, intent)
            }
            Telephony.Sms.Intents.SMS_DELIVER_ACTION -> {
                handleDeliveredSms(context, intent)
            }
        }
    }

    private fun handleIncomingSms(context: Context, intent: Intent) {
        try {
            val messages = Telephony.Sms.Intents.getMessagesFromIntent(intent)

            for (sms in messages) {
                val sender = sms.displayOriginatingAddress ?: "Unknown"
                val body = sms.messageBody ?: ""
                val timestamp = sms.timestampMillis

                // Create unique identifier for this SMS
                val smsId = "${sender}_${body.hashCode()}_$timestamp"

                // Skip if already processed
                if (processedSms.contains(smsId)) {
                    Log.d(TAG, "📱 Skipping duplicate SMS processing")
                    continue
                }

                // Add to processed set (keep only last 20 to prevent memory issues)
                processedSms.add(smsId)
                if (processedSms.size > 20) {
                    val iterator = processedSms.iterator()
                    repeat(5) { // Remove oldest 5 entries
                        if (iterator.hasNext()) {
                            iterator.next()
                            iterator.remove()
                        }
                    }
                }

                val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                val formattedDate = dateFormat.format(Date(timestamp))

                Log.d(TAG, "📱 SMS Received - From: $sender, Body: ${body.take(50)}..., Time: $formattedDate")

                // Analyze SMS for suspicious content (single processing)
                val analysisResult = SmsDetectionHelper.analyzeSmsContent(
                    sender = sender,
                    body = body,
                    timestamp = timestamp,
                    context = context
                )

                // Store SMS data for demo UI
                SmsDetectionHelper.storeSmsData(
                    sender = sender,
                    body = body,
                    timestamp = timestamp,
                    analysisResult = analysisResult,
                    context = context
                )

                // Show notification if suspicious
                if (analysisResult.isSuspicious) {
                    SmsDetectionHelper.showSuspiciousMessageNotification(
                        context = context,
                        sender = sender,
                        body = body,
                        riskLevel = analysisResult.riskLevel
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing SMS: ${e.message}", e)
        }
    }

    private fun handleDeliveredSms(context: Context, intent: Intent) {
        Log.d(TAG, "📤 SMS Delivered")
        // Handle SMS delivery confirmation if needed
    }
}