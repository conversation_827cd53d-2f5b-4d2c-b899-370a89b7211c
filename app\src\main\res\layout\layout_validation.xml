<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent_black_60"
    android:padding="@dimen/_24sdp">

    <!-- Main popup card with modern design -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cardPopup"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16sdp"
        app:cardBackgroundColor="@color/card_background"
        app:cardCornerRadius="@dimen/_20sdp"
        app:cardElevation="@dimen/_16sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:strokeColor="@color/border"
        app:strokeWidth="1dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivClose"
            android:layout_gravity="end"
            android:layout_margin="@dimen/_10sdp"
            android:layout_width="@dimen/_20sdp"
            android:layout_height="@dimen/_20sdp"
            android:src="@drawable/ic_close"
            />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/_24sdp">

            <!-- Status indicator background -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardStatusIndicator"
                android:layout_width="@dimen/_80sdp"
                android:layout_height="@dimen/_80sdp"
                android:layout_marginBottom="@dimen/_16sdp"
                app:cardBackgroundColor="@color/surface"
                app:cardCornerRadius="@dimen/_40sdp"
                app:cardElevation="@dimen/_4sdp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <!-- Lottie animation for status -->
                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/lvWarning"
                    android:layout_width="@dimen/_60sdp"
                    android:layout_height="@dimen/_60sdp"
                    android:layout_gravity="center"
                    android:scaleType="centerCrop"
                    app:lottie_autoPlay="true"
                    app:lottie_loop="true"
                    app:lottie_rawRes="@raw/wait"
                    tools:ignore="ContentDescription" />

            </androidx.cardview.widget.CardView>

            <!-- Status title -->
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvStatusTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8sdp"
                android:fontFamily="@font/roboto_semi_bold"
                android:gravity="center"
                android:text="Email Security Check"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/_16sdp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/cardStatusIndicator" />

            <!-- Status subtitle -->
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvStatusSubtitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4sdp"
                android:fontFamily="@font/roboto_semi_regular"
                android:gravity="center"
                android:text="Analyzing email content for threats..."
                android:textColor="@color/text_secondary"
                android:textSize="@dimen/_12sdp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvStatusTitle" />

            <!-- Message container -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardMessage"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_20sdp"
                app:cardBackgroundColor="@color/surface"
                app:cardCornerRadius="@dimen/_12sdp"
                app:cardElevation="@dimen/_2sdp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvStatusSubtitle">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvMessage"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_semi_regular"
                    android:gravity="start"
                    android:lineSpacingExtra="@dimen/_2sdp"
                    android:maxLines="5"
                    android:padding="@dimen/_16sdp"
                    android:text="@string/warning_this_email_is_unsafe_proceed_with_caution"
                    android:textColor="@color/text_primary"
                    android:textSize="@dimen/_12sdp"
                    tools:text="This email contains suspicious content that may be harmful. Please review carefully before proceeding." />

            </androidx.cardview.widget.CardView>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btClose"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/cardMessage"
                style="@style/Widget.Material3.Button"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_44sdp"
                android:layout_marginStart="@dimen/_8sdp"
                android:layout_weight="1"
                android:backgroundTint="@color/app_color"
                android:fontFamily="@font/roboto_semi_medium"
                android:text="@string/close"
                android:textAllCaps="false"
                android:textColor="@color/black"
                android:textSize="@dimen/_12sdp"
                app:cornerRadius="@dimen/_12sdp" />

            <!-- Progress indicator (hidden by default) -->
            <ProgressBar
                android:id="@+id/progressIndicator"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16sdp"
                android:indeterminate="true"
                android:visibility="gone"
                app:indicatorColor="@color/app_color"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutButtons"
                app:trackColor="@color/surface"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>
