package com.tech.ekvayu.BaseClass

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.tech.ekvayu.Activities.DashboardActivity
import com.tech.ekvayu.R
import org.json.JSONArray
import org.json.JSONObject
import java.util.regex.Pattern

object SmsDetectionHelper {
    
    private const val TAG = "SmsDetectionHelper"
    private const val CHANNEL_ID = "sms_security_channel"
    private const val NOTIFICATION_ID = 1001
    
    // Suspicious patterns for SMS analysis
    private val suspiciousPatterns = listOf(
        // Phishing patterns
        Pattern.compile("(?i).*click.*link.*urgent.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(?i).*verify.*account.*immediately.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(?i).*suspended.*account.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(?i).*winner.*prize.*claim.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(?i).*free.*money.*", Pattern.CASE_INSENSITIVE),
        
        // Financial scam patterns
        Pattern.compile("(?i).*bank.*security.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(?i).*credit.*card.*expired.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(?i).*loan.*approved.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(?i).*tax.*refund.*", Pattern.CASE_INSENSITIVE),
        
        // Malware/virus patterns
        Pattern.compile("(?i).*download.*app.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(?i).*install.*software.*", Pattern.CASE_INSENSITIVE),
        
        // Social engineering
        Pattern.compile("(?i).*help.*family.*emergency.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(?i).*police.*arrest.*", Pattern.CASE_INSENSITIVE)
    )
    
    // Suspicious keywords
    private val suspiciousKeywords = listOf(
        "urgent", "immediate", "verify", "suspended", "expired", "winner", "prize",
        "free", "congratulations", "claim", "click", "download", "install",
        "security", "alert", "warning", "emergency", "police", "arrest",
        "bank", "credit", "loan", "refund", "tax", "irs", "government"
    )
    
    data class SmsAnalysisResult(
        val isSuspicious: Boolean,
        val riskLevel: RiskLevel,
        val detectedPatterns: List<String>,
        val suspiciousKeywords: List<String>,
        val riskScore: Int,
        val recommendations: List<String>,
        val extractedComponents: SmsComponents
    )

    data class SmsComponents(
        val timestamp: SmsTimestamp,
        val sender: SmsSender,
        val content: SmsContent,
        val urls: List<SmsUrl>,
        val phoneNumbers: List<String>,
        val emailAddresses: List<String>,
        val attachments: SmsAttachments,
        val metadata: SmsMetadata
    )

    data class SmsTimestamp(
        val receivedTime: Long,
        val formattedDate: String,
        val formattedTime: String,
        val dayOfWeek: String,
        val timeZone: String,
        val isBusinessHours: Boolean,
        val isWeekend: Boolean
    )

    data class SmsSender(
        val rawSender: String,
        val displayName: String?,
        val phoneNumber: String?,
        val countryCode: String?,
        val carrierInfo: String?,
        val senderType: SenderType,
        val isContact: Boolean,
        val contactName: String?
    )

    enum class SenderType {
        PERSONAL_CONTACT,
        BUSINESS_NUMBER,
        SHORT_CODE,
        ALPHANUMERIC,
        INTERNATIONAL,
        UNKNOWN
    }

    data class SmsContent(
        val originalText: String,
        val cleanedText: String,
        val language: String?,
        val encoding: String,
        val messageLength: Int,
        val wordCount: Int,
        val hasEmojis: Boolean,
        val emojis: List<String>,
        val hasSpecialCharacters: Boolean,
        val contentType: ContentType,
        val urgencyLevel: UrgencyLevel
    )

    enum class ContentType {
        PLAIN_TEXT,
        PROMOTIONAL,
        TRANSACTIONAL,
        OTP_VERIFICATION,
        ALERT_NOTIFICATION,
        SPAM_LIKELY,
        PHISHING_ATTEMPT
    }

    enum class UrgencyLevel {
        LOW,
        NORMAL,
        HIGH,
        URGENT,
        EMERGENCY
    }

    data class SmsUrl(
        val originalUrl: String,
        val expandedUrl: String?,
        val domain: String,
        val isShortened: Boolean,
        val isSuspicious: Boolean,
        val riskScore: Int,
        val urlType: UrlType
    )

    enum class UrlType {
        LEGITIMATE_WEBSITE,
        SOCIAL_MEDIA,
        SHOPPING,
        BANKING,
        GOVERNMENT,
        SHORTENED_URL,
        SUSPICIOUS_DOMAIN,
        PHISHING_ATTEMPT,
        MALWARE_LINK
    }

    data class SmsAttachments(
        val hasAttachments: Boolean,
        val attachmentCount: Int,
        val attachmentTypes: List<String>,
        val voiceMessage: VoiceMessage?,
        val images: List<ImageAttachment>,
        val documents: List<DocumentAttachment>
    )

    data class VoiceMessage(
        val duration: Int?, // in seconds
        val fileSize: Long?, // in bytes
        val format: String?,
        val isTranscribed: Boolean,
        val transcription: String?
    )

    data class ImageAttachment(
        val fileName: String?,
        val fileSize: Long?,
        val format: String?,
        val dimensions: String?
    )

    data class DocumentAttachment(
        val fileName: String?,
        val fileSize: Long?,
        val fileType: String?,
        val isPasswordProtected: Boolean?
    )

    data class SmsMetadata(
        val messageId: String?,
        val threadId: String?,
        val messageType: MessageType,
        val deliveryStatus: DeliveryStatus,
        val readStatus: Boolean,
        val isGroupMessage: Boolean,
        val groupParticipants: List<String>,
        val replyToMessageId: String?,
        val forwardedFrom: String?
    )

    enum class MessageType {
        INCOMING,
        OUTGOING,
        DRAFT,
        FAILED,
        QUEUED
    }

    enum class DeliveryStatus {
        DELIVERED,
        PENDING,
        FAILED,
        READ,
        UNKNOWN
    }
    
    enum class RiskLevel(val displayName: String, val color: String) {
        LOW("Low Risk", "#4CAF50"),
        MEDIUM("Medium Risk", "#FF9800"),
        HIGH("High Risk", "#F44336"),
        CRITICAL("Critical Risk", "#D32F2F")
    }
    
    /**
     * Analyze SMS content for suspicious patterns with detailed component extraction
     */
    fun analyzeSmsContent(
        sender: String,
        body: String,
        timestamp: Long,
        context: Context
    ): SmsAnalysisResult {
        
        val detectedPatterns = mutableListOf<String>()
        val foundKeywords = mutableListOf<String>()
        var riskScore = 0
        
        // Check for suspicious patterns
        suspiciousPatterns.forEach { pattern ->
            if (pattern.matcher(body).find()) {
                detectedPatterns.add(pattern.pattern())
                riskScore += 20
            }
        }
        
        // Check for suspicious keywords
        val bodyLower = body.lowercase()
        suspiciousKeywords.forEach { keyword ->
            if (bodyLower.contains(keyword)) {
                foundKeywords.add(keyword)
                riskScore += 5
            }
        }
        
        // Check sender patterns
        if (isNumericSender(sender)) {
            riskScore += 10
        }
        
        if (isShortCode(sender)) {
            riskScore += 5
        }
        
        // Check for URLs
        if (containsUrls(body)) {
            riskScore += 15
        }
        
        // Determine risk level
        val riskLevel = when {
            riskScore >= 50 -> RiskLevel.CRITICAL
            riskScore >= 30 -> RiskLevel.HIGH
            riskScore >= 15 -> RiskLevel.MEDIUM
            else -> RiskLevel.LOW
        }
        
        val isSuspicious = riskScore >= 15
        
        val recommendations = generateRecommendations(riskLevel, detectedPatterns, foundKeywords)

        // Extract detailed components
        val extractedComponents = extractSmsComponents(sender, body, timestamp, context)

        Log.d(TAG, "📊 SMS Analysis - Sender: $sender, Risk: ${riskLevel.displayName}, Score: $riskScore")
        Log.d(TAG, "📋 Components - URLs: ${extractedComponents.urls.size}, Phone: ${extractedComponents.phoneNumbers.size}")

        return SmsAnalysisResult(
            isSuspicious = isSuspicious,
            riskLevel = riskLevel,
            detectedPatterns = detectedPatterns,
            suspiciousKeywords = foundKeywords,
            riskScore = riskScore,
            recommendations = recommendations,
            extractedComponents = extractedComponents
        )
    }
    
    /**
     * Store SMS data for demo UI
     */
    fun storeSmsData(
        sender: String,
        body: String,
        timestamp: Long,
        analysisResult: SmsAnalysisResult,
        context: Context
    ) {
        try {
            val sharedPrefManager = SharedPrefManager.getInstance()
            val existingData = sharedPrefManager.getString("sms_detection_data", "[]")
            val jsonArray = JSONArray(existingData)
            
            val smsObject = JSONObject().apply {
                put("sender", sender)
                put("body", body)
                put("timestamp", timestamp)
                put("isSuspicious", analysisResult.isSuspicious)
                put("riskLevel", analysisResult.riskLevel.name)
                put("riskScore", analysisResult.riskScore)
                put("detectedPatterns", JSONArray(analysisResult.detectedPatterns))
                put("suspiciousKeywords", JSONArray(analysisResult.suspiciousKeywords))
                put("recommendations", JSONArray(analysisResult.recommendations))

                // Store detailed components
                val components = analysisResult.extractedComponents
                put("components", JSONObject().apply {
                    // Timestamp
                    put("timestamp", JSONObject().apply {
                        put("receivedTime", components.timestamp.receivedTime)
                        put("formattedDate", components.timestamp.formattedDate)
                        put("formattedTime", components.timestamp.formattedTime)
                        put("dayOfWeek", components.timestamp.dayOfWeek)
                        put("timeZone", components.timestamp.timeZone)
                        put("isBusinessHours", components.timestamp.isBusinessHours)
                        put("isWeekend", components.timestamp.isWeekend)
                    })

                    // Sender
                    put("sender", JSONObject().apply {
                        put("rawSender", components.sender.rawSender)
                        put("displayName", components.sender.displayName)
                        put("phoneNumber", components.sender.phoneNumber)
                        put("countryCode", components.sender.countryCode)
                        put("senderType", components.sender.senderType.name)
                        put("isContact", components.sender.isContact)
                        put("contactName", components.sender.contactName)
                    })

                    // Content
                    put("content", JSONObject().apply {
                        put("originalText", components.content.originalText)
                        put("cleanedText", components.content.cleanedText)
                        put("language", components.content.language)
                        put("messageLength", components.content.messageLength)
                        put("wordCount", components.content.wordCount)
                        put("hasEmojis", components.content.hasEmojis)
                        put("emojis", JSONArray(components.content.emojis))
                        put("contentType", components.content.contentType.name)
                        put("urgencyLevel", components.content.urgencyLevel.name)
                    })

                    // URLs
                    put("urls", JSONArray().apply {
                        components.urls.forEach { url ->
                            put(JSONObject().apply {
                                put("originalUrl", url.originalUrl)
                                put("domain", url.domain)
                                put("isShortened", url.isShortened)
                                put("isSuspicious", url.isSuspicious)
                                put("riskScore", url.riskScore)
                                put("urlType", url.urlType.name)
                            })
                        }
                    })

                    // Phone Numbers and Emails
                    put("phoneNumbers", JSONArray(components.phoneNumbers))
                    put("emailAddresses", JSONArray(components.emailAddresses))

                    // Attachments
                    put("attachments", JSONObject().apply {
                        put("hasAttachments", components.attachments.hasAttachments)
                        put("attachmentCount", components.attachments.attachmentCount)
                        put("attachmentTypes", JSONArray(components.attachments.attachmentTypes))
                        put("hasVoiceMessage", components.attachments.voiceMessage != null)
                    })

                    // Metadata
                    put("metadata", JSONObject().apply {
                        put("messageId", components.metadata.messageId)
                        put("messageType", components.metadata.messageType.name)
                        put("deliveryStatus", components.metadata.deliveryStatus.name)
                        put("isGroupMessage", components.metadata.isGroupMessage)
                    })
                })
            }
            
            jsonArray.put(smsObject)
            
            // Keep only last 100 SMS records
            if (jsonArray.length() > 100) {
                val trimmedArray = JSONArray()
                for (i in (jsonArray.length() - 100) until jsonArray.length()) {
                    trimmedArray.put(jsonArray.get(i))
                }
                sharedPrefManager.putString("sms_detection_data", trimmedArray.toString())
            } else {
                sharedPrefManager.putString("sms_detection_data", jsonArray.toString())
            }
            
            Log.d(TAG, "💾 SMS data stored successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error storing SMS data: ${e.message}", e)
        }
    }
    
    /**
     * Show notification for suspicious messages
     */
    fun showSuspiciousMessageNotification(
        context: Context,
        sender: String,
        body: String,
        riskLevel: RiskLevel
    ) {
        createNotificationChannel(context)
        
        val intent = Intent(context, DashboardActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("open_sms_detection", true)
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("⚠️ Suspicious SMS Detected")
            .setContentText("${riskLevel.displayName} message from $sender")
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("Suspicious message detected from $sender. Tap to view details and recommendations."))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build()
        
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
        
        Log.d(TAG, "🔔 Suspicious SMS notification shown")
    }
    
    private fun createNotificationChannel(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "SMS Security Alerts",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for suspicious SMS messages"
            }
            
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun isNumericSender(sender: String): Boolean {
        return sender.all { it.isDigit() || it == '+' }
    }
    
    private fun isShortCode(sender: String): Boolean {
        return sender.length <= 6 && sender.all { it.isDigit() }
    }
    
    private fun containsUrls(text: String): Boolean {
        val urlPattern = Pattern.compile(
            "(?i)\\b(?:https?://|www\\.|[a-z0-9.-]+\\.[a-z]{2,})\\S*",
            Pattern.CASE_INSENSITIVE
        )
        return urlPattern.matcher(text).find()
    }
    
    private fun generateRecommendations(
        riskLevel: RiskLevel,
        detectedPatterns: List<String>,
        suspiciousKeywords: List<String>
    ): List<String> {
        val recommendations = mutableListOf<String>()
        
        when (riskLevel) {
            RiskLevel.CRITICAL, RiskLevel.HIGH -> {
                recommendations.add("🚫 DO NOT click any links in this message")
                recommendations.add("🚫 DO NOT provide personal information")
                recommendations.add("📞 Contact the sender through official channels to verify")
                recommendations.add("🗑️ Consider deleting this message")
                recommendations.add("🚨 Report this message as spam")
            }
            RiskLevel.MEDIUM -> {
                recommendations.add("⚠️ Be cautious with this message")
                recommendations.add("🔍 Verify sender identity before taking action")
                recommendations.add("🚫 Avoid clicking suspicious links")
            }
            RiskLevel.LOW -> {
                recommendations.add("✅ Message appears relatively safe")
                recommendations.add("🔍 Still verify sender if requesting sensitive information")
            }
        }
        
        return recommendations
    }

    /**
     * Extract detailed SMS components for analysis
     */
    private fun extractSmsComponents(
        sender: String,
        body: String,
        timestamp: Long,
        context: Context
    ): SmsComponents {

        return SmsComponents(
            timestamp = extractTimestamp(timestamp),
            sender = extractSenderInfo(sender, context),
            content = extractContentInfo(body),
            urls = extractUrls(body),
            phoneNumbers = extractPhoneNumbers(body),
            emailAddresses = extractEmailAddresses(body),
            attachments = extractAttachments(body), // For now, basic implementation
            metadata = extractMetadata(sender, body, timestamp)
        )
    }

    private fun extractTimestamp(timestamp: Long): SmsTimestamp {
        val calendar = java.util.Calendar.getInstance()
        calendar.timeInMillis = timestamp

        val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
        val timeFormat = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault())
        val dayFormat = java.text.SimpleDateFormat("EEEE", java.util.Locale.getDefault())

        val hour = calendar.get(java.util.Calendar.HOUR_OF_DAY)
        val dayOfWeek = calendar.get(java.util.Calendar.DAY_OF_WEEK)

        return SmsTimestamp(
            receivedTime = timestamp,
            formattedDate = dateFormat.format(calendar.time),
            formattedTime = timeFormat.format(calendar.time),
            dayOfWeek = dayFormat.format(calendar.time),
            timeZone = java.util.TimeZone.getDefault().displayName,
            isBusinessHours = hour in 9..17,
            isWeekend = dayOfWeek == java.util.Calendar.SATURDAY || dayOfWeek == java.util.Calendar.SUNDAY
        )
    }

    private fun extractSenderInfo(sender: String, context: Context): SmsSender {
        val phonePattern = Pattern.compile("\\+?[1-9]\\d{1,14}")
        val isPhoneNumber = phonePattern.matcher(sender).matches()

        val senderType = when {
            sender.length <= 6 && sender.all { it.isDigit() } -> SenderType.SHORT_CODE
            isPhoneNumber -> {
                when {
                    sender.startsWith("+") -> SenderType.INTERNATIONAL
                    sender.length >= 10 -> SenderType.BUSINESS_NUMBER
                    else -> SenderType.PERSONAL_CONTACT
                }
            }
            sender.any { it.isLetter() } -> SenderType.ALPHANUMERIC
            else -> SenderType.UNKNOWN
        }

        val countryCode = if (sender.startsWith("+")) {
            sender.substring(1, minOf(4, sender.length))
        } else null

        return SmsSender(
            rawSender = sender,
            displayName = if (senderType == SenderType.ALPHANUMERIC) sender else null,
            phoneNumber = if (isPhoneNumber) sender else null,
            countryCode = countryCode,
            carrierInfo = null, // Would need carrier detection API
            senderType = senderType,
            isContact = false, // Would need contact lookup
            contactName = null // Would need contact lookup
        )
    }

    private fun extractContentInfo(body: String): SmsContent {
        val emojiPattern = Pattern.compile("[\\p{So}\\p{Cn}]")
        val emojis = mutableListOf<String>()
        val emojiMatcher = emojiPattern.matcher(body)

        while (emojiMatcher.find()) {
            emojis.add(emojiMatcher.group())
        }

        val urgencyKeywords = listOf("urgent", "immediate", "asap", "emergency", "now", "quickly")
        val urgencyLevel = when {
            body.lowercase().contains("emergency") -> UrgencyLevel.EMERGENCY
            urgencyKeywords.any { body.lowercase().contains(it) } -> UrgencyLevel.URGENT
            body.contains("!") && body.count { it == '!' } > 2 -> UrgencyLevel.HIGH
            else -> UrgencyLevel.NORMAL
        }

        val contentType = determineContentType(body)

        return SmsContent(
            originalText = body,
            cleanedText = body.replace(Regex("[^\\p{L}\\p{N}\\s]"), ""),
            language = detectLanguage(body),
            encoding = "UTF-8", // Default assumption
            messageLength = body.length,
            wordCount = body.split("\\s+".toRegex()).size,
            hasEmojis = emojis.isNotEmpty(),
            emojis = emojis,
            hasSpecialCharacters = body.any { !it.isLetterOrDigit() && !it.isWhitespace() },
            contentType = contentType,
            urgencyLevel = urgencyLevel
        )
    }

    private fun extractUrls(body: String): List<SmsUrl> {
        val urlPattern = Pattern.compile(
            "(?i)\\b(?:https?://|www\\.|[a-z0-9.-]+\\.[a-z]{2,})\\S*",
            Pattern.CASE_INSENSITIVE
        )

        val urls = mutableListOf<SmsUrl>()
        val matcher = urlPattern.matcher(body)

        while (matcher.find()) {
            val url = matcher.group()
            val domain = extractDomain(url)
            val isShortened = isUrlShortened(domain)
            val isSuspicious = isSuspiciousDomain(domain)

            urls.add(SmsUrl(
                originalUrl = url,
                expandedUrl = null, // Would need URL expansion service
                domain = domain,
                isShortened = isShortened,
                isSuspicious = isSuspicious,
                riskScore = if (isSuspicious) 80 else if (isShortened) 40 else 10,
                urlType = determineUrlType(domain, isSuspicious)
            ))
        }

        return urls
    }

    private fun extractPhoneNumbers(body: String): List<String> {
        val phonePattern = Pattern.compile(
            "(?:\\+?1[-. ]?)?\\(?([0-9]{3})\\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})|" +
            "\\+?[1-9]\\d{1,14}"
        )

        val phoneNumbers = mutableListOf<String>()
        val matcher = phonePattern.matcher(body)

        while (matcher.find()) {
            phoneNumbers.add(matcher.group())
        }

        return phoneNumbers
    }

    private fun extractEmailAddresses(body: String): List<String> {
        val emailPattern = Pattern.compile(
            "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"
        )

        val emailAddresses = mutableListOf<String>()
        val matcher = emailPattern.matcher(body)

        while (matcher.find()) {
            emailAddresses.add(matcher.group())
        }

        return emailAddresses
    }

    private fun extractAttachments(body: String): SmsAttachments {
        // Basic implementation - in real scenario, this would analyze MMS data
        val hasVoiceIndicators = body.lowercase().contains("voice message") ||
                                body.lowercase().contains("audio") ||
                                body.lowercase().contains("recording")

        val hasImageIndicators = body.lowercase().contains("image") ||
                                body.lowercase().contains("photo") ||
                                body.lowercase().contains("picture")

        val voiceMessage = if (hasVoiceIndicators) {
            VoiceMessage(
                duration = null,
                fileSize = null,
                format = null,
                isTranscribed = false,
                transcription = null
            )
        } else null

        return SmsAttachments(
            hasAttachments = hasVoiceIndicators || hasImageIndicators,
            attachmentCount = if (hasVoiceIndicators || hasImageIndicators) 1 else 0,
            attachmentTypes = mutableListOf<String>().apply {
                if (hasVoiceIndicators) add("voice")
                if (hasImageIndicators) add("image")
            },
            voiceMessage = voiceMessage,
            images = emptyList(),
            documents = emptyList()
        )
    }

    private fun extractMetadata(sender: String, body: String, timestamp: Long): SmsMetadata {
        return SmsMetadata(
            messageId = generateMessageId(sender, body, timestamp),
            threadId = null,
            messageType = MessageType.INCOMING,
            deliveryStatus = DeliveryStatus.DELIVERED,
            readStatus = true,
            isGroupMessage = false,
            groupParticipants = emptyList(),
            replyToMessageId = null,
            forwardedFrom = null
        )
    }

    // Helper methods
    private fun determineContentType(body: String): ContentType {
        val lowerBody = body.lowercase()

        return when {
            lowerBody.contains("otp") || lowerBody.contains("verification code") -> ContentType.OTP_VERIFICATION
            lowerBody.contains("offer") || lowerBody.contains("discount") -> ContentType.PROMOTIONAL
            lowerBody.contains("alert") || lowerBody.contains("notification") -> ContentType.ALERT_NOTIFICATION
            lowerBody.contains("click") && lowerBody.contains("link") -> ContentType.PHISHING_ATTEMPT
            suspiciousKeywords.any { lowerBody.contains(it) } -> ContentType.SPAM_LIKELY
            else -> ContentType.PLAIN_TEXT
        }
    }

    private fun detectLanguage(text: String): String {
        // Basic language detection - in production, use proper language detection library
        return when {
            text.any { it in 'a'..'z' || it in 'A'..'Z' } -> "en"
            else -> "unknown"
        }
    }

    private fun extractDomain(url: String): String {
        return try {
            val cleanUrl = if (!url.startsWith("http")) "http://$url" else url
            java.net.URL(cleanUrl).host
        } catch (e: Exception) {
            url.substringBefore("/").substringAfter("://")
        }
    }

    private fun isUrlShortened(domain: String): Boolean {
        val shorteners = listOf("bit.ly", "tinyurl.com", "t.co", "goo.gl", "ow.ly", "short.link")
        return shorteners.any { domain.contains(it, ignoreCase = true) }
    }

    private fun isSuspiciousDomain(domain: String): Boolean {
        val suspiciousTlds = listOf(".tk", ".ml", ".ga", ".cf")
        val suspiciousPatterns = listOf("secure-", "verify-", "update-", "confirm-")

        return suspiciousTlds.any { domain.endsWith(it) } ||
               suspiciousPatterns.any { domain.contains(it) } ||
               domain.count { it == '-' } > 3
    }

    private fun determineUrlType(domain: String, isSuspicious: Boolean): UrlType {
        return when {
            isSuspicious -> UrlType.PHISHING_ATTEMPT
            domain.contains("bank") || domain.contains("paypal") -> UrlType.BANKING
            domain.contains("gov") -> UrlType.GOVERNMENT
            domain.contains("facebook") || domain.contains("twitter") -> UrlType.SOCIAL_MEDIA
            domain.contains("amazon") || domain.contains("shop") -> UrlType.SHOPPING
            isUrlShortened(domain) -> UrlType.SHORTENED_URL
            else -> UrlType.LEGITIMATE_WEBSITE
        }
    }

    private fun generateMessageId(sender: String, body: String, timestamp: Long): String {
        return "${sender.hashCode()}_${body.hashCode()}_$timestamp"
    }
}
