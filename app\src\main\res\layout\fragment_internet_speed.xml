<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:fillViewport="true"
    tools:context=".Fragments.InternetSpeedFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Internet Speed Test"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- Network Status Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Network Status"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <ImageView
                        android:id="@+id/ivNetworkIcon"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@android:drawable/ic_dialog_info"
                        android:layout_marginEnd="12dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvNetworkType"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="WiFi"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:id="@+id/tvConnectionStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Connected"
                            android:textSize="14sp"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                    <Button
                        android:id="@+id/btnRefresh"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Refresh"
                        android:textSize="12sp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Speed Test Results Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Speed Test Results"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp" />

                <!-- Download Speed -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Download Speed:"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/tvDownloadSpeed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="-- Mbps"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/app_color" />

                </LinearLayout>

                <!-- Upload Speed -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Upload Speed:"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/tvUploadSpeed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="-- Mbps"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/app_color" />

                </LinearLayout>

                <!-- Ping -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Ping:"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/tvPing"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="-- ms"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/app_color" />

                </LinearLayout>

                <!-- Speed Rating -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Speed Rating:"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/tvSpeedRating"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Not tested"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/darker_gray" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Test Controls Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Speed Test Control"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp" />

                <!-- Progress Bar -->
                <ProgressBar
                    android:id="@+id/progressBar"
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:layout_marginBottom="16dp"
                    android:visibility="gone"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:indeterminate="false"
                    android:max="100"
                    android:progress="0"
                    android:progressTint="@color/app_color" />

                <!-- Status Text -->
                <TextView
                    android:id="@+id/tvStatus"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Ready to test"
                    android:textSize="16sp"
                    android:textColor="@color/text_secondary"
                    android:gravity="center"
                    android:layout_marginBottom="16dp" />

                <!-- Start Test Button -->
                <Button
                    android:id="@+id/btnStartTest"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Start Professional Speed Test"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:padding="16dp"
                    style="@style/Widget.Material3.Button" />


            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Speed Test Information Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Speed Test Information"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="• Download Speed: Measures how fast data is downloaded from the internet\n• Upload Speed: Measures how fast data is uploaded to the internet\n• Ping: Measures the response time to a server (lower is better)\n• Speed Rating: Overall assessment of your internet connection quality"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:lineSpacingExtra="4dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="\nSpeed Ratings:\n• Excellent: 25+ Mbps\n• Good: 10-25 Mbps\n• Fair: 5-10 Mbps\n• Poor: 1-5 Mbps\n• Very Poor: &lt;1 Mbps"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:lineSpacingExtra="4dp"
                    android:layout_marginTop="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="\nProfessional Speed Testing:\n• Uses time-based measurement like speedtest.net\n• Multiple server testing for accuracy\n• Automatic calibration for network type\n• Real-time speed monitoring during test\n• Results should closely match web-based tests"
                    android:textSize="12sp"
                    android:textColor="@color/success"
                    android:lineSpacingExtra="2dp"
                    android:layout_marginTop="8dp"
                    android:background="@color/success_light"
                    android:padding="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="\nCompare with Web Tests:\n• fast.com (Netflix)\n• speedtest.net (Ookla)\n• speed.cloudflare.com\n• Results should be within ±15% of web tests"
                    android:textSize="12sp"
                    android:textColor="@color/info"
                    android:lineSpacingExtra="2dp"
                    android:layout_marginTop="8dp"
                    android:background="@color/info_light"
                    android:padding="8dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>