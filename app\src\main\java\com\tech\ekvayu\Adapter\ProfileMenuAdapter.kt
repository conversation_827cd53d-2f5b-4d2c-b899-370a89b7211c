package com.tech.ekvayu.Adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.tech.ekvayu.databinding.ItemProfileBinding
import com.tech.ekvayu.models.ProfileMenuModel


class ProfileMenuAdapter(private val items: List<ProfileMenuModel>, val listener: onMenuClickListner) : RecyclerView.Adapter<ProfileMenuAdapter.MenuViewHolder>() {

    inner class MenuViewHolder(val binding: ItemProfileBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuViewHolder {
        val binding = ItemProfileBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MenuViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON><PERSON><PERSON>Holder, position: Int) {
        val item = items[position]

        holder.binding.tvTitle.text = item.title
        holder.binding.tvDescription.text = item.description
        holder.binding.ivIcon.setImageResource(item.icon)

        holder.binding.root.setOnClickListener {
            listener.onMenuClicked(item)
        }
    }

    override fun getItemCount() = items.size

    interface  onMenuClickListner{
        fun onMenuClicked(item: ProfileMenuModel)
    }
}
