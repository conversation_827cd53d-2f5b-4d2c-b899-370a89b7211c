package com.tech.ekvayu.models

/**
 * Data models for Analytics Dashboard
 */

data class AnalyticsDashboardData(
    val emailStats: EmailStats,
    val smsStats: SmsStats,
    val deviceInfo: DeviceAnalytics,
    val securityStatus: SecurityStatus,
    val recentActivity: List<RecentActivity>
)

data class EmailStats(
    val totalEmails: Int = 0,
    val spamEmails: Int = 0,
    val safeEmails: Int = 0,
    val pendingEmails: Int = 0,
    val disputes: Int = 0,
    val lastProcessedTime: String = "Never"
) {
    fun getSpamPercentage(): Float {
        return if (totalEmails > 0) (spamEmails.toFloat() / totalEmails.toFloat()) * 100 else 0f
    }
    
    fun getSafePercentage(): Float {
        return if (totalEmails > 0) (safeEmails.toFloat() / totalEmails.toFloat()) * 100 else 0f
    }
}

data class SmsStats(
    val totalSms: Int = 0,
    val suspiciousSms: Int = 0,
    val safeSms: Int = 0,
    val highRiskSms: Int = 0,
    val mediumRiskSms: Int = 0,
    val lowRiskSms: Int = 0,
    val lastScanTime: String = "Never"
) {
    fun getThreatPercentage(): Float {
        return if (totalSms > 0) (suspiciousSms.toFloat() / totalSms.toFloat()) * 100 else 0f
    }
}

data class DeviceAnalytics(
    val deviceModel: String = "Unknown",
    val androidVersion: String = "Unknown",
    val availableMemoryGB: String = "0 GB",
    val totalMemoryGB: String = "0 GB",
    val storageUsedPercentage: Int = 0,
    val freeStorageGB: String = "0 GB",
    val batteryLevel: Int = 0,
    val isCharging: Boolean = false,
    val networkType: String = "Unknown"
)

data class SecurityStatus(
    val gmailServiceEnabled: Boolean = false,
    val smsServiceEnabled: Boolean = false,
    val yahooServiceEnabled: Boolean = false,
    val outlookServiceEnabled: Boolean = false,
    val overlayPermissionGranted: Boolean = false,
    val accessibilityPermissionGranted: Boolean = false
) {
    fun getEnabledServicesCount(): Int {
        var count = 0
        if (gmailServiceEnabled) count++
        if (smsServiceEnabled) count++
        if (yahooServiceEnabled) count++
        if (outlookServiceEnabled) count++
        return count
    }
    
    fun getSecurityScore(): Int {
        val totalServices = 4
        val enabledServices = getEnabledServicesCount()
        val permissionScore = if (accessibilityPermissionGranted) 20 else 0
        val overlayScore = if (overlayPermissionGranted) 10 else 0
        
        return ((enabledServices.toFloat() / totalServices.toFloat()) * 70).toInt() + permissionScore + overlayScore
    }
}

data class RecentActivity(
    val id: String,
    val type: ActivityType,
    val title: String,
    val description: String,
    val timestamp: Long,
    val status: ActivityStatus,
    val icon: String
) {
    fun getTimeAgo(): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        
        return when {
            diff < 60000 -> "Just now"
            diff < 3600000 -> "${diff / 60000}m ago"
            diff < 86400000 -> "${diff / 3600000}h ago"
            else -> "${diff / 86400000}d ago"
        }
    }
}

enum class ActivityType(val displayName: String, val icon: String) {
    EMAIL_DETECTED("Email Detected", "📧"),
    SMS_DETECTED("SMS Detected", "📱"),
    SPAM_BLOCKED("Spam Blocked", "🚫"),
    DISPUTE_RAISED("Dispute Raised", "⚖️"),
    SERVICE_ENABLED("Service Enabled", "✅"),
    SERVICE_DISABLED("Service Disabled", "❌"),
    PERMISSION_GRANTED("Permission Granted", "🔓"),
    PERMISSION_DENIED("Permission Denied", "🔒")
}

enum class ActivityStatus(val displayName: String, val colorRes: String) {
    SUCCESS("SUCCESS", "@color/green"),
    WARNING("WARNING", "@color/orange"),
    ERROR("ERROR", "@color/red"),
    INFO("INFO", "@color/blue"),
    BLOCKED("BLOCKED", "@color/red"),
    ALLOWED("ALLOWED", "@color/green"),
    PENDING("PENDING", "@color/orange")
}

/**
 * Chart data models
 */
data class ChartData(
    val labels: List<String>,
    val values: List<Float>,
    val colors: List<Int>
)

data class EmailChartData(
    val safeEmails: Float,
    val spamEmails: Float,
    val pendingEmails: Float
) {
    fun toChartData(): ChartData {
        return ChartData(
            labels = listOf("Safe", "Spam", "Pending"),
            values = listOf(safeEmails, spamEmails, pendingEmails),
            colors = listOf(
                android.graphics.Color.GREEN,
                android.graphics.Color.RED,
                android.graphics.Color.YELLOW
            )
        )
    }
}

data class SmsChartData(
    val safeSms: Float,
    val suspiciousSms: Float,
    val highRiskSms: Float
) {
    fun toChartData(): ChartData {
        return ChartData(
            labels = listOf("Safe", "Suspicious", "High Risk"),
            values = listOf(safeSms, suspiciousSms, highRiskSms),
            colors = listOf(
                android.graphics.Color.GREEN,
                android.graphics.Color.YELLOW,
                android.graphics.Color.RED
            )
        )
    }
}
