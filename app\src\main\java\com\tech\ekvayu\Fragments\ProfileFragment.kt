package com.tech.ekvayu.Fragments

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import com.tech.ekvayu.Activities.DashboardActivity
import com.tech.ekvayu.Activities.MainActivity
import com.tech.ekvayu.Adapter.ProfileMenuAdapter
import com.tech.ekvayu.Adapter.SecurityTypeAdapter
import com.tech.ekvayu.BaseClass.BaseFragment
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.BaseClass.ThemeManager
import com.tech.ekvayu.R
import com.tech.ekvayu.databinding.FragmentProfileBinding
import com.tech.ekvayu.models.ProfileMenuModel
import com.tech.ekvayu.models.SecurityTypeModel

class ProfileFragment : BaseFragment() , ProfileMenuAdapter.onMenuClickListner{
    
    private var _binding: FragmentProfileBinding? = null
    private val binding get() = _binding!!
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentProfileBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setProfileMenu()
    }

    private fun setProfileMenu() {
        val menuItems = listOf(
            ProfileMenuModel("About","App Information and version", R.drawable.ic_info),
            ProfileMenuModel("Privacy Policy","Get App Privacy Policy", R.drawable.ic_info),
            ProfileMenuModel("Help", "Get App Help",R.drawable.ic_info),
            ProfileMenuModel("Logout", "Logout From App",R.drawable.ic_info),
            )
        val adapter = ProfileMenuAdapter(menuItems, this)
        binding.rvProfileMenu.adapter = adapter
        binding.rvProfileMenu.layoutManager = GridLayoutManager(requireActivity(), 1)
    }

    override fun onMenuClicked(item: ProfileMenuModel) {
       when(item.title)
       {
           "About"->{
               val fragment = CommonFragment()
               val bundle = Bundle().apply { putString("tag", "COMMON")
                   putString("pageName", "about")
               }
               fragment.arguments = bundle
               navigateToFragment(fragment = fragment, tag = "COMMON")
           }
           "Privacy Policy"->{
               val fragment = CommonFragment()
               val bundle = Bundle().apply {
                   putString("tag", "COMMON")
                   putString("pageName", "privacy")
               }
               fragment.arguments = bundle
               navigateToFragment(fragment = fragment, tag = "COMMON")
           }
           "Help"->{
               Toast.makeText(requireContext(), "Help section coming soon", Toast.LENGTH_SHORT).show()
           }
           "Logout"->{
               showLogoutConfirmationDialog(requireContext())
           }
       }
    }

    private fun showLogoutConfirmationDialog(context: Context) {
        AlertDialog.Builder(context).apply {
            setTitle("Logout")
            setMessage("Are you sure you want to logout?")
            setPositiveButton("Yes") { dialog, _ ->
                dialog.dismiss()
                sharedPrefManager.clear()
                val intent = Intent(requireContext(), DashboardActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                startActivity(intent)
                requireActivity().finish()
            }
            setNegativeButton("Cancel") { dialog, _ ->
                dialog.dismiss()
            }
            setCancelable(false)
            show()
        }
    }

    private fun toggleTheme() {
        try {
            val currentTheme = ThemeManager.getCurrentTheme()
            val nextTheme = when (currentTheme) {
                ThemeManager.ThemeMode.LIGHT -> ThemeManager.ThemeMode.DARK
                ThemeManager.ThemeMode.DARK -> ThemeManager.ThemeMode.LIGHT
                ThemeManager.ThemeMode.SYSTEM -> ThemeManager.ThemeMode.DARK
            }

            ThemeManager.applyTheme(nextTheme)
            val themeName = ThemeManager.getThemeDisplayName(nextTheme)
            Toast.makeText(requireContext(), "Switched to $themeName", Toast.LENGTH_SHORT).show()
            
            updateThemeDisplay()
        } catch (e: Exception) {
            Toast.makeText(requireContext(), "Failed to change theme", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun updateThemeDisplay() {
        val currentTheme = ThemeManager.getCurrentTheme()
        val themeName = ThemeManager.getThemeDisplayName(currentTheme)
      //  binding.tvThemeStatus.text = "Current: $themeName"
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }


}
