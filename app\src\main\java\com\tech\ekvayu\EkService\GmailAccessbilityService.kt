package com.tech.ekvayu.EkService

import android.accessibilityservice.AccessibilityService
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo

class GmailAccessbilityService:AccessibilityService() {

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null) return

        if (event.eventType == AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED ||
            event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED) {

            val rootNode = rootInActiveWindow ?: return
            val mailData = extractMailData(rootNode)

            if (mailData.isNotEmpty()) {
                Log.d("MailService", "Detected Mail: $mailData")
            }
        }
    }


    private fun extractMailData(root: AccessibilityNodeInfo): Map<String, Any?> {
        val result = mutableMapOf<String, Any?>()

        val allTextNodes = getAllTextNodes(root)

        // Sender (usually first line, contains '@' or looks like name)
        result["sender"] = allTextNodes.find { it.contains("@") || it.contains("From:") }

        // Receiver
        result["receiver"] = allTextNodes.find { it.contains("To:") || it.contains("Cc:") }

        // Subject
        result["subject"] = allTextNodes.find { it.length in 5..80 && !it.contains("@") && !it.contains("http") }

        // Body (biggest block of text)
        result["body"] = allTextNodes.maxByOrNull { it.length }

        // Extract URLs
        result["urls"] = extractUrls(result["body"]?.toString())

        // Extract Attachments (look for pdf/doc/jpg/png)
        result["attachments"] = allTextNodes.filter { it.matches(".*\\.(pdf|docx?|jpg|png)".toRegex(RegexOption.IGNORE_CASE)) }

        return result
    }


    private fun getAllTextNodes(node: AccessibilityNodeInfo?): List<String> {
        val list = mutableListOf<String>()
        if (node == null) return list

        if (!node.text.isNullOrEmpty()) {
            list.add(node.text.toString())
        }
        for (i in 0 until node.childCount) {
            list.addAll(getAllTextNodes(node.getChild(i)))
        }
        return list
    }

    private fun extractUrls(text: String?): List<String> {
        if (text.isNullOrEmpty()) return emptyList()
        val urlRegex = "(https?://[\\w\\-\\.\\?=/#&%]+)".toRegex()
        return urlRegex.findAll(text).map { it.value }.toList()
    }


    override fun onInterrupt() {}

}

