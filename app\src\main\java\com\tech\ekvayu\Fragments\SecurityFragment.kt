package com.tech.ekvayu.Fragments

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.recyclerview.widget.GridLayoutManager
import com.tech.ekvayu.Adapter.SecurityTypeAdapter
import com.tech.ekvayu.BaseClass.BaseFragment
import com.tech.ekvayu.R
import com.tech.ekvayu.databinding.FragmentSecurityBinding
import com.tech.ekvayu.models.SecurityTypeModel

class SecurityFragment : BaseFragment() , SecurityTypeAdapter.onClickEventListner{
    
    private var _binding: FragmentSecurityBinding? = null
    private val binding get() = _binding!!
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSecurityBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
     //   setupUI()

        setSecurityType()
    }


    private fun setSecurityType() {
        val menuItems = listOf(
            SecurityTypeModel("SMS Detection","Detect suspicious SMS messages", R.drawable.ic_sms),
            SecurityTypeModel("MMS Detection","Detect suspicious multimedia messages", R.drawable.ic_mms),
            SecurityTypeModel("Access Files","Scan device storage for malicious files", R.drawable.ic_folder_scan),
            SecurityTypeModel("Spam Mails", "View detected spam emails",R.drawable.spam_mail),
            SecurityTypeModel("Dispute Mails", "Manage email disputes",R.drawable.dispute_mails),
            SecurityTypeModel("Voice Detection", "Manage Voice Phishing Attack",R.drawable.spam_mail),

        )

        val adapter = SecurityTypeAdapter(menuItems, this)
        binding.rvSecurityType.adapter = adapter
        binding.rvSecurityType.layoutManager = GridLayoutManager(requireActivity(), 1)


    }


    
/*    private fun setupUI() {
        // Setup security options
        binding.cardSmsDetection.setOnClickListener {
            navigateToFragment(
                fragment = SmsDetectionFragment(),
                tag = "SMS_DETECTION"
            )
        }

        binding.cardMmsDetection.setOnClickListener {
            navigateToFragment(
                fragment = MmsDetectionFragment(),
                tag = "MMS_DETECTION"
            )
        }

        binding.cardAccessFiles.setOnClickListener {
            navigateToFragment(
                fragment = AccessFilesFragment(),
                tag = "ACCESS_FILES"
            )
        }

        binding.cardSpamMails.setOnClickListener {
            navigateToFragment(
                fragment = SpamMailFragment(),
                tag = "SPAM_MAILS"
            )
        }
        
        binding.cardDisputeMails.setOnClickListener {
            navigateToFragment(
                fragment = DisputeMaillistFragment(),
                tag = "DISPUTE_MAILS"
            )
        }
    }*/


    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onTypeClick(item: SecurityTypeModel) {
        Toast.makeText(requireContext(), "Under Development", Toast.LENGTH_SHORT).show()
    }
}
