<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <androidx.cardview.widget.CardView
        android:layout_width="@dimen/_100sdp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_8sdp"
        app:cardCornerRadius="@dimen/_12sdp"
        app:cardElevation="@dimen/_6sdp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_12sdp"
            android:gravity="center">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/lvWarning"
                android:layout_width="@dimen/_40sdp"
                android:layout_height="@dimen/_40sdp"
                app:lottie_rawRes="@raw/ai_progress"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"
                android:scaleType="fitCenter" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8sdp"
                android:fontFamily="@font/roboto_semi_medium"
                android:textSize="@dimen/_10sdp"
                android:gravity="center"
                android:text="Loading..." />

        </LinearLayout>
    </androidx.cardview.widget.CardView>


</androidx.constraintlayout.widget.ConstraintLayout>
