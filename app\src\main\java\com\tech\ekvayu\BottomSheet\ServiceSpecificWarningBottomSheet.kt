package com.tech.ekvayu.BottomSheet

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.tech.ekvayu.BaseClass.PermissionHelper
import com.tech.ekvayu.databinding.FragmentWarningBottomSheetBinding

/**
 * Service-specific warning bottom sheet for different accessibility services
 */
class ServiceSpecificWarningBottomSheet : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentWarningBottomSheetBinding
    private var serviceType: PermissionHelper.ServiceType = PermissionHelper.ServiceType.GMAIL
    private val TAG = "ServiceWarningBottomSheet"

    companion object {
        private const val ARG_SERVICE_TYPE = "service_type"
        
        fun newInstance(serviceType: PermissionHelper.ServiceType): ServiceSpecificWarningBottomSheet {
            val fragment = ServiceSpecificWarningBottomSheet()
            val args = Bundle()
            args.putString(ARG_SERVICE_TYPE, serviceType.name)
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            val serviceTypeName = it.getString(ARG_SERVICE_TYPE, PermissionHelper.ServiceType.GMAIL.name)
            serviceType = PermissionHelper.ServiceType.valueOf(serviceTypeName)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentWarningBottomSheetBinding.inflate(inflater, container, false)

        // Customize the message based on service type
        setupServiceSpecificContent()

        // Log device information for debugging
        Log.d(TAG, "Device Info: ${PermissionHelper.getDeviceInfo()}")
        Log.d(TAG, "Service Type: ${serviceType.displayName}")

        binding.btPermission.setOnClickListener {
            Log.d(TAG, "Permission button clicked for ${serviceType.displayName}")
            PermissionHelper.openAccessibilitySettings(requireContext())
        }

        return binding.root
    }

    private fun setupServiceSpecificContent() {
        // You can customize the title and message based on service type
        when (serviceType) {
            PermissionHelper.ServiceType.GMAIL -> {
                // Keep default content for Gmail
            }
            PermissionHelper.ServiceType.YAHOO -> {
                // Customize for Yahoo if needed
                Toast.makeText(requireContext(), "Yahoo accessibility service required", Toast.LENGTH_SHORT).show()
            }
            PermissionHelper.ServiceType.OUTLOOK -> {
                // Customize for Outlook if needed
                Toast.makeText(requireContext(), "Outlook accessibility service required", Toast.LENGTH_SHORT).show()
            }
            PermissionHelper.ServiceType.SMS -> {
                // Customize for SMS if needed
                Toast.makeText(requireContext(), "SMS accessibility service required", Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // Check if specific accessibility permission is granted when fragment resumes
        checkAndUpdatePermissionStatus()
    }

    private fun checkAndUpdatePermissionStatus() {
        Log.d(TAG, "Checking ${serviceType.displayName} accessibility permission status")

        val isEnabled = PermissionHelper.checkAndUpdateSpecificAccessibilityPermission(requireContext(), serviceType)

        if (isEnabled) {
            Log.d(TAG, "${serviceType.displayName} accessibility permission is granted - dismissing bottom sheet")
            // Permission is granted, dismiss bottom sheet
            dismiss()
        } else {
            Log.d(TAG, "${serviceType.displayName} accessibility permission is not granted")
        }
    }
}
