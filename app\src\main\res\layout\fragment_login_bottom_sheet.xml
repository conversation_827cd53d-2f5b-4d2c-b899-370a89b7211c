<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/clUserId"
    android:padding="24dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/securityIcon"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:src="@drawable/icon_secure_phishing"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/appTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/roboto_semi_bold"
        android:text="SecureAccess"
        android:textColor="@color/black"
        android:textSize="@dimen/_14sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/securityIcon" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/emailLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:hint="User Id"
        app:boxBackgroundMode="outline"
        app:boxStrokeColor="@color/app_color"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appTitle">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/userId"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textEmailAddress"
            android:maxLength="6"
            android:fontFamily="@font/roboto_semi_bold"
            android:textColor="@color/dark_grey" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/passwordLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:hint="Password"
        app:boxBackgroundMode="outline"
        app:boxStrokeColor="@color/app_color"
        app:endIconMode="password_toggle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/emailLayout">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/userPass"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPassword"
            android:fontFamily="@font/roboto_semi_bold"
            android:textColor="@color/dark_grey" />
    </com.google.android.material.textfield.TextInputLayout>


    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btSubmit"
        app:layout_constraintTop_toBottomOf="@+id/passwordLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:elevation="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_20sdp"
        android:paddingStart="@dimen/_15sdp"
        android:paddingEnd="@dimen/_15sdp"
        android:textAllCaps="false"
        android:visibility="visible"
        android:background="@drawable/bg_button_app_color"
        android:fontFamily="@font/roboto_semi_medium"
        android:textSize="@dimen/_12sdp"
        android:textColor="@color/white"
        android:text="@string/submit"
        android:layout_marginBottom="@dimen/_20sdp"
        />


</androidx.constraintlayout.widget.ConstraintLayout>
