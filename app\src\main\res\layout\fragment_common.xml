<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".Fragments.CommonFragment">


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvContent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_semi_regular"
        android:layout_margin="@dimen/_8sdp"
        android:padding="@dimen/_8sdp"
        android:textSize="@dimen/_10sdp"
        android:text="About"/>


</ScrollView>