<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/card_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <CheckBox
                android:id="@+id/cbSelectFile"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp" />

            <ImageView
                android:id="@+id/ivFileType"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_file"
                android:layout_marginEnd="8dp"
                app:tint="@color/text_secondary" />

            <TextView
                android:id="@+id/tvFileName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="File Name"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:ellipsize="end"
                android:maxLines="1" />

            <TextView
                android:id="@+id/tvFileExtension"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="EXT"
                android:textSize="10sp"
                android:textStyle="bold"
                android:textColor="@color/text_secondary"
                android:background="@drawable/rounded_background"
                android:backgroundTint="@color/info_light"
                android:padding="4dp"
                android:layout_marginEnd="8dp" />

            <ImageView
                android:id="@+id/ivThreatIcon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_warning" />

        </LinearLayout>

        <!-- File Path Row -->
        <TextView
            android:id="@+id/tvFilePath"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="File Path"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:layout_marginBottom="8dp"
            android:ellipsize="middle"
            android:maxLines="2" />

        <!-- Threat Summary Row -->
        <TextView
            android:id="@+id/tvThreatSummary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Threat Summary"
            android:textSize="13sp"
            android:textColor="@color/warning"
            android:layout_marginBottom="8dp"
            android:ellipsize="end"
            android:maxLines="2" />

        <!-- Info Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tvFileSize"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Size"
                android:textSize="12sp"
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/tvFileDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Date"
                android:textSize="12sp"
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/tvThreatLevel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Threat Level"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/text_secondary"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/tvThreatScore"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Score"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:background="@drawable/rounded_background"
                android:backgroundTint="@color/warning"
                android:padding="4dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
