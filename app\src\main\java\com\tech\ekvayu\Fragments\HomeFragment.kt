package com.tech.ekvayu.Fragments


import android.graphics.Color
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.recyclerview.widget.GridLayoutManager
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.github.mikephil.charting.utils.ColorTemplate
import com.tech.ekvayu.Activities.DashboardActivity
import com.tech.ekvayu.ActivityGraph.ActivityGraphResponse
import com.tech.ekvayu.Adapter.DashboardMenuAdapter
import com.tech.ekvayu.Adapter.DashboardMenuAdapter.onClickEventListner
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.BaseFragment
import com.tech.ekvayu.BaseClass.CommonUtil
import com.tech.ekvayu.BaseClass.PermissionHelper
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.BottomSheet.LoginBottomSheetFragment
import com.tech.ekvayu.BottomSheet.ServiceSpecificWarningBottomSheet
import com.tech.ekvayu.BottomSheet.SuggetionBottomFragment
import com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment
import com.tech.ekvayu.R
import com.tech.ekvayu.Request.CommonRequest
import com.tech.ekvayu.databinding.FragmentHomeBinding
import com.tech.ekvayu.models.DashboardMenuModel
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class HomeFragment : BaseFragment(),onClickEventListner {
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    private lateinit var binding: FragmentHomeBinding
    private var receiverMail=""
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding=FragmentHomeBinding.inflate(inflater,container,false)
        receiverMail=  sharedPrefManager.getString(AppConstant.receiverMail,"")

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        menus()
        getAcityGraph(receiverMail)
    }

    private fun getAcityGraph(email: String) {
        // Check network connectivity first
        if (!CommonUtil.isNetworkAvailable(requireContext())) {
            Log.d("HomeFragment", "❌ No internet connection available")
            return
        }

        val retrofit = ApiClient.getRetrofitInstance(requireContext())
        if (retrofit == null) {
            Log.e("HomeFragment", "❌ Failed to get Retrofit instance")
            return
        }

        val apiService = retrofit.create(ApiService::class.java)
        val request = CommonRequest(emailId = email)

        CommonUtil.showProgressDialog(requireContext(), "Please wait...")
        apiService.getActivtyGraph(request)
            .enqueue(object : Callback<ActivityGraphResponse> {
                override fun onResponse(
                    call: Call<ActivityGraphResponse>,
                    response: Response<ActivityGraphResponse>
                ) {
                    CommonUtil.hideProgressDialog()
                    if (response.isSuccessful && response.body() != null) {
                        try {
                            Log.d("getBody", "onResponse: "+response.body())

                            val responseData = response.body()?.data
                            if (responseData != null) {
                                val total_disputes = responseData.totalDisputes?.toFloat() ?: 0f
                                val total_processed_emails = responseData.totalProcessedEmails?.toFloat() ?: 0f
                                val total_spam_emails = responseData.totalSpamEmails?.toFloat() ?: 0f

                                Log.d("gettotalalldata", "onResponse: $total_disputes = $total_spam_emails = $total_processed_emails")

                                // Create data map with safe values
                                val dataMap = mapOf(
                                    "Dispute" to total_disputes,
                                    "Spam" to total_spam_emails,
                                    "Process" to total_processed_emails
                                )
                                val entries = dataMap.map { PieEntry(it.value, it.key) }

                                // Set up dataset
                                val dataSet = PieDataSet(entries, "Issue Types")
                                dataSet.colors = ColorTemplate.MATERIAL_COLORS.toList()
                                dataSet.valueTextSize = 5f
                                dataSet.valueTextColor = Color.WHITE

                                // Set data to PieChart
                                val pieData = PieData(dataSet)
                                binding.pieChart.data = pieData

                                // Customize chart
                                binding.pieChart.description.isEnabled = false
                                binding.pieChart.centerText = "Activity"
                                binding.pieChart.setEntryLabelColor(Color.WHITE)
                                binding.pieChart.animateY(1000)
                                binding.pieChart.invalidate()
                                binding.pieChart.setDrawEntryLabels(false)
                                binding.pieChart.legend.textColor = Color.WHITE

                                // Update text views
                                binding.tvDispute.text = "🛡️ $total_disputes Dispute Mails"
                                binding.tvSpam.text = "❗ $total_spam_emails Spam Mails"
                                binding.tvProcess.text = "⚖️ $total_processed_emails Process Mails"
                            } else {
                                Log.e("HomeFragment", "❌ Response data is null")
                                Toast.makeText(requireContext(), "No data available", Toast.LENGTH_SHORT).show()
                            }
                        } catch (e: Exception) {
                            Log.e("HomeFragment", "❌ Error processing response data: ${e.message}", e)
                            Toast.makeText(requireContext(), "Error displaying data", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                      //  Toast.makeText(requireActivity(), response.message(), Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<ActivityGraphResponse?>, t: Throwable) {
                    CommonUtil.hideProgressDialog()
                    Log.e("HomeFragment", "❌ API call failed: ${t.message}", t)

                    // Show user-friendly error message
                    val errorMessage = when {
                        t.message?.contains("timeout", ignoreCase = true) == true -> "Request timeout. Please try again."
                        t.message?.contains("network", ignoreCase = true) == true -> "Network error. Please check your connection."
                        else -> "Unable to load activity data. Please try again later."
                    }

                    try {
                        if (isAdded && context != null) {
                            Toast.makeText(requireContext(), errorMessage, Toast.LENGTH_SHORT).show()
                        }
                    } catch (e: Exception) {
                        Log.e("HomeFragment", "❌ Error showing toast: ${e.message}")
                    }
                }

            })
    }

    private fun menus() {
        val menuItems = listOf(
            DashboardMenuModel("Details","View your device-specific information", R.drawable.details),
            DashboardMenuModel("Spam Mails","Review and handle detected spam emails", R.drawable.spam_mail),
            DashboardMenuModel("Dispute Mails","See emails you've raised disputes against", R.drawable.argument),
          //  DashboardMenuModel("Activity", "Track all recent actions taken on emails",R.drawable.process),
            DashboardMenuModel("SMS Detection", "Monitor and analyze SMS messages for threats",R.drawable.ic_email),
            DashboardMenuModel("Check Speed", "Check your internet speed",R.drawable.settings),
            DashboardMenuModel("Yahoo Detection", "Monitor and analyze yahoo spam mails",R.drawable.settings),
            DashboardMenuModel("Outlook Detection", "Monitor and analyze outlook spam mails ",R.drawable.settings),

           // DashboardMenuModel("Dispute", "Manually raise a new dispute on suspicious emails",R.drawable.dispute)
        )

        val adapter = DashboardMenuAdapter(menuItems, this)
        binding.rvMenu.adapter = adapter
        binding.rvMenu.layoutManager = GridLayoutManager(requireActivity(), 2)

        // Add long press listener to show menu options
        binding.rvMenu.setOnLongClickListener {
            showMenuOptionsBottomSheet()
            true
        }
    }

    override fun onResume() {
        super.onResume()
       // checkPermissionsAndShowBottomSheets()
    }

   /* private fun checkPermissionsAndShowBottomSheets() {
        // Use PermissionHelper to check and update all permission states
        val isServiceEnabled = PermissionHelper.checkAndUpdateAccessibilityPermission(requireContext())
        val isMailConfigured = PermissionHelper.checkAndUpdateMailConfiguration()
        val permissionStates = PermissionHelper.getPermissionStates()

        Log.d("getPermissionState", "checkPermissionsAndShowBottomSheets: "+permissionStates)

        // Update current mail status
        receiverMail = permissionStates.receiverMail

        // Show bottom sheets based on conditions
        when {
            // Case 1: Accessibility permission not granted
            !isServiceEnabled -> {
                val bottomSheet_warning = WarningBottomSheetFragment()
                bottomSheet_warning.setCancelable(false)
                bottomSheet_warning.show(requireActivity().supportFragmentManager, bottomSheet_warning.tag)
            }
            // Case 2: Accessibility permission granted but mail not configured
            isServiceEnabled && !isMailConfigured -> {
                val bottomSheet_suggetion = SuggetionBottomFragment()
                bottomSheet_suggetion.setCancelable(false)
                bottomSheet_suggetion.show(requireActivity().supportFragmentManager, bottomSheet_suggetion.tag)
            }
            // Case 3: Both conditions met - no bottom sheet needed
            isServiceEnabled && isMailConfigured -> {
                // Both accessibility permission and mail are configured
                // No bottom sheet needed - user can use the app normally
            }
        }
    }
*/

   override fun onMenuClick(item: DashboardMenuModel) {
        when(item.title){
            "Details"->{
                (activity as DashboardActivity).showFragment(fragment = DeviceDetailsFragment(), tag = "DEVICE_DETAILS")
            }
            "Spam Mails"->{
                Log.d("getUserId", "onMenuClick: "+sharedPrefManager.getString(AppConstant.userId,""))
             /*   if ( sharedPrefManager.getString(AppConstant.userId,"")==""){
                    callLoginBottomSheet()
                }
                else
                {
                    checkServicePermissionAndNavigate(serviceType = PermissionHelper.ServiceType.GMAIL, onPermissionGranted = {
                            (activity as DashboardActivity).showFragment(fragment = SpamMailFragment(), tag = "SPAM_MAILS")
                        }
                    )
                }*/

                checkServicePermissionAndNavigate(serviceType = PermissionHelper.ServiceType.GMAIL, onPermissionGranted = {
                    (activity as DashboardActivity).showFragment(fragment = SpamMailFragment(), tag = "SPAM_MAILS")
                  //  Toast.makeText(requireContext(), "Under Development", Toast.LENGTH_SHORT).show()
                }
                )

            }
            "Dispute Mails"->{
                (activity as DashboardActivity).showFragment(
                    fragment = DisputeMaillistFragment(),
                    tag = "DISPUTE_MAILS"
                )
            }
            "Activity"-> {
                (activity as DashboardActivity).showFragment(
                    fragment = ActivityGraphFragment(),
                    tag = "ACTIVITY_GRAPH"
                )
            }
            "SMS Detection"-> {

                Toast.makeText(requireContext(), "Under Development", Toast.LENGTH_SHORT).show()
                if ( sharedPrefManager.getString(AppConstant.userId,"")==""){
                    callLoginBottomSheet()
                }
                else
                {
                    checkServicePermissionAndNavigate(
                        serviceType = PermissionHelper.ServiceType.SMS,
                        onPermissionGranted = {
                            (activity as DashboardActivity).showFragment(
                                fragment = SmsDetectionFragment(),
                                tag = "SMS_DETECTION"
                            )
                        }
                    )
                }
                /*checkServicePermissionAndNavigate(
                    serviceType = PermissionHelper.ServiceType.SMS,
                    onPermissionGranted = {
                        (activity as DashboardActivity).showFragment(
                            fragment = SmsDetectionFragment(),
                            tag = "SMS_DETECTION"
                        )
                    }
                )*/

            }

            "Yahoo Detection"-> {

              /*  if ( sharedPrefManager.getString(AppConstant.userId,"")==""){
                    callLoginBottomSheet()
                }
                else
                {
                    checkServicePermissionAndNavigate(
                        serviceType = PermissionHelper.ServiceType.YAHOO,
                        onPermissionGranted = {
                            Toast.makeText(requireActivity(), "Yahoo Detection feature coming soon", Toast.LENGTH_SHORT).show()
                            // TODO: Navigate to Yahoo detection fragment when implemented
                        }
                    )
                }*/

                checkServicePermissionAndNavigate(
                    serviceType = PermissionHelper.ServiceType.YAHOO,
                    onPermissionGranted = {
                        Toast.makeText(requireActivity(), "Yahoo Detection feature coming soon", Toast.LENGTH_SHORT).show()
                        // TODO: Navigate to Yahoo detection fragment when implemented
                    }
                )

            }
            "Outlook Detection"-> {
              /*  if ( sharedPrefManager.getString(AppConstant.userId,"")==""){
                    callLoginBottomSheet()
                }
                else
                {
                    checkServicePermissionAndNavigate(
                        serviceType = PermissionHelper.ServiceType.OUTLOOK,
                        onPermissionGranted = {
                            Toast.makeText(requireActivity(), "Outlook Detection feature coming soon", Toast.LENGTH_SHORT).show()
                            // TODO: Navigate to Outlook detection fragment when implemented
                        }
                    )
                }
*/
                checkServicePermissionAndNavigate(
                    serviceType = PermissionHelper.ServiceType.OUTLOOK,
                    onPermissionGranted = {
                        Toast.makeText(requireActivity(), "Outlook Detection feature coming soon", Toast.LENGTH_SHORT).show()
                        // TODO: Navigate to Outlook detection fragment when implemented
                    }
                )

            }

            "Check Speed"-> {
                (activity as DashboardActivity).showFragment(
                    fragment = InternetSpeedFragment(),
                    tag = "InternetSpeed"
                )
            }

            "Settings"-> {
                Toast.makeText(requireActivity(),"Settings feature coming soon",Toast.LENGTH_SHORT).show()
            }

            else->{
                Toast.makeText(requireActivity(),"Something Went Wrong",Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun showMenuOptionsBottomSheet() {
        // Menu options functionality removed
        Toast.makeText(requireActivity(), "Menu options feature coming soon", Toast.LENGTH_SHORT).show()
    }


    private fun checkServicePermissionAndNavigate(
        serviceType: PermissionHelper.ServiceType,
        onPermissionGranted: () -> Unit
    ) {
        Log.d("HomeFragment", "Checking ${serviceType.displayName} accessibility permission")

        val isServiceEnabled = PermissionHelper.checkAndUpdateSpecificAccessibilityPermission(requireContext(), serviceType)

        if (isServiceEnabled) {
            Log.d("HomeFragment", "${serviceType.displayName} accessibility permission granted - proceeding")
            onPermissionGranted()
        } else {
            Log.d("HomeFragment", "${serviceType.displayName} accessibility permission not granted - showing warning")

            // Show service-specific warning bottom sheet
            val bottomSheet = ServiceSpecificWarningBottomSheet.newInstance(serviceType)
            bottomSheet.setCancelable(true)
            bottomSheet.show(requireActivity().supportFragmentManager, bottomSheet.tag)

            Toast.makeText(
                requireContext(),
                "${serviceType.displayName} accessibility permission is required",
                Toast.LENGTH_LONG
            ).show()
        }
    }

/*
    private fun handleMenuOption(option: OptionModel) {
        when (option.id) {
            "export_data" -> {
                Toast.makeText(requireContext(), "Export Data selected", Toast.LENGTH_SHORT).show()
                // Implement export functionality
            }
            "clear_data" -> {
                Toast.makeText(requireContext(), "Clear Data selected", Toast.LENGTH_SHORT).show()
                // Implement clear data functionality
            }
            "backup" -> {
                Toast.makeText(requireContext(), "Backup Settings selected", Toast.LENGTH_SHORT).show()
                // Implement backup functionality
            }
            "about" -> {
                Toast.makeText(requireContext(), "About App selected", Toast.LENGTH_SHORT).show()
                // Navigate to about screen
            }
        }
    }
*/



}