package com.tech.ekvayu.Fragments

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.tech.ekvayu.BaseClass.BaseFragment
import com.tech.ekvayu.databinding.FragmentAccessFilesBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.util.*

class AccessFilesFragment : BaseFragment() {
    
    private var _binding: FragmentAccessFilesBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var maliciousFilesAdapter: MaliciousFilesAdapter
    private val maliciousFilesList = mutableListOf<MaliciousFile>()
    
    private val STORAGE_PERMISSION_REQUEST_CODE = 124
    
    // Known malicious file signatures and patterns
    private val maliciousExtensions = setOf(
        ".apk", ".exe", ".bat", ".cmd", ".scr", ".pif", ".com", ".vbs", 
        ".js", ".jar", ".dex", ".so", ".dll", ".sys", ".tmp"
    )
    
    private val suspiciousFileNames = setOf(
        "virus", "malware", "trojan", "backdoor", "keylogger", "spyware",
        "rootkit", "worm", "ransomware", "adware", "hack", "crack", "keygen"
    )
    
    private val maliciousHashes = setOf(
        // Add known malicious file hashes here
        "d41d8cd98f00b204e9800998ecf8427e", // Example MD5 hash
        "e3b0c44298fc1c149afbf4c8996fb924" // Example SHA256 hash
    )
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAccessFilesBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupUI()
        checkPermissionsAndScanFiles()
    }
    
    private fun setupUI() {
        // Setup RecyclerView
        maliciousFilesAdapter = MaliciousFilesAdapter(maliciousFilesList) { file ->
            showFileDetails(file)
        }
        
        binding.recyclerViewMaliciousFiles.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = maliciousFilesAdapter
        }
        
        // Setup buttons
        binding.btnScanFiles.setOnClickListener {
            checkPermissionsAndScanFiles()
        }
        
        binding.btnRefresh.setOnClickListener {
            refreshScan()
        }
        
        binding.btnClearResults.setOnClickListener {
            clearResults()
        }
        
        binding.btnDeleteSelected.setOnClickListener {
            deleteSelectedFiles()
        }
    }
    
    private fun checkPermissionsAndScanFiles() {
        val permissions = mutableListOf<String>()
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ permissions
            if (ContextCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.READ_MEDIA_IMAGES
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                permissions.add(Manifest.permission.READ_MEDIA_IMAGES)
            }
            if (ContextCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.READ_MEDIA_VIDEO
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                permissions.add(Manifest.permission.READ_MEDIA_VIDEO)
            }
            if (ContextCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.READ_MEDIA_AUDIO
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                permissions.add(Manifest.permission.READ_MEDIA_AUDIO)
            }
        } else {
            // Android 12 and below
            if (ContextCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.READ_EXTERNAL_STORAGE
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
        }
        
        if (permissions.isNotEmpty()) {
            requestPermissions(permissions.toTypedArray(), STORAGE_PERMISSION_REQUEST_CODE)
        } else {
            startFileScan()
        }
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == STORAGE_PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                startFileScan()
            } else {
                Toast.makeText(
                    requireContext(),
                    "Storage permission is required to scan files",
                    Toast.LENGTH_LONG
                ).show()
                updateEmptyState("Permission denied. Grant storage access to scan files.")
            }
        }
    }
    
    private fun startFileScan() {
        binding.progressBar.visibility = View.VISIBLE
        binding.tvStatus.text = "Scanning device for malicious files..."
        binding.btnScanFiles.isEnabled = false
        
        lifecycleScope.launch {
            try {
                val scannedFiles = withContext(Dispatchers.IO) {
                    scanDeviceForMaliciousFiles()
                }
                
                withContext(Dispatchers.Main) {
                    maliciousFilesList.clear()
                    maliciousFilesList.addAll(scannedFiles)
                    maliciousFilesAdapter.notifyDataSetChanged()
                    
                    updateUI()
                    binding.progressBar.visibility = View.GONE
                    binding.btnScanFiles.isEnabled = true
                    
                    val maliciousCount = scannedFiles.size
                    binding.tvStatus.text = if (maliciousCount > 0) {
                        "Scan complete. Found $maliciousCount potentially malicious files"
                    } else {
                        "Scan complete. No malicious files detected"
                    }
                }
                
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    binding.btnScanFiles.isEnabled = true
                    binding.tvStatus.text = "Error during scan: ${e.message}"
                    Log.e(TAG, "Error scanning files", e)
                }
            }
        }
    }

    private suspend fun scanDeviceForMaliciousFiles(): List<MaliciousFile> {
        val maliciousFiles = mutableListOf<MaliciousFile>()
        var scannedCount = 0

        try {
            // Get all accessible storage directories
            val scanDirectories = mutableListOf<File>()

            // Add external storage directories
            if (Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED) {
                scanDirectories.add(Environment.getExternalStorageDirectory())
            }

            // Add additional storage directories
            val externalFilesDirs = requireContext().getExternalFilesDirs(null)
            externalFilesDirs.filterNotNull().forEach { dir ->
                scanDirectories.add(dir.parentFile?.parentFile ?: dir)
            }

            // Scan each directory
            for (directory in scanDirectories) {
                if (directory.exists() && directory.canRead()) {
                    scanDirectoryRecursively(directory, maliciousFiles) { count ->
                        scannedCount = count
                        // Update progress on main thread
                        lifecycleScope.launch(Dispatchers.Main) {
                            binding.tvStatus.text = "Scanning... ($scannedCount files checked)"
                        }
                    }
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error scanning directories", e)
        }

        return maliciousFiles
    }

    private fun scanDirectoryRecursively(
        directory: File,
        maliciousFiles: MutableList<MaliciousFile>,
        onProgress: (Int) -> Unit
    ) {
        try {
            val files = directory.listFiles() ?: return

            for (file in files) {
                try {
                    if (file.isDirectory) {
                        // Skip system and hidden directories
                        if (!file.name.startsWith(".") &&
                            !file.name.equals("Android", ignoreCase = true) &&
                            !file.name.equals("system", ignoreCase = true)) {
                            scanDirectoryRecursively(file, maliciousFiles, onProgress)
                        }
                    } else {
                        // Scan individual file
                        val maliciousFile = analyzeFile(file)
                        if (maliciousFile != null) {
                            maliciousFiles.add(maliciousFile)
                        }

                        // Update progress every 100 files
                        if (maliciousFiles.size % 100 == 0) {
                            onProgress(maliciousFiles.size)
                        }
                    }
                } catch (e: SecurityException) {
                    // Skip files we can't access
                    Log.d(TAG, "Cannot access file: ${file.absolutePath}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error scanning directory: ${directory.absolutePath}", e)
        }
    }

    private fun analyzeFile(file: File): MaliciousFile? {
        try {
            if (!file.exists() || !file.canRead() || file.length() == 0L) {
                return null
            }

            var threatScore = 0
            val threats = mutableListOf<String>()

            // Check file extension
            val extension = file.extension.lowercase()
            if (maliciousExtensions.contains(".$extension")) {
                threatScore += 40
                threats.add("Suspicious file extension: .$extension")
            }

            // Check file name for suspicious patterns
            val fileName = file.name.lowercase()
            suspiciousFileNames.forEach { suspiciousName ->
                if (fileName.contains(suspiciousName)) {
                    threatScore += 30
                    threats.add("Suspicious file name contains: $suspiciousName")
                }
            }

            // Check file size (unusually large or small files)
            val fileSize = file.length()
            if (fileSize > 100 * 1024 * 1024) { // > 100MB
                threatScore += 15
                threats.add("Unusually large file size: ${formatFileSize(fileSize)}")
            } else if (fileSize < 100 && extension in setOf("exe", "apk", "jar")) {
                threatScore += 25
                threats.add("Suspiciously small executable file")
            }

            // Check file hash (for known malicious files)
            if (extension in setOf("apk", "exe", "jar", "dex")) {
                val fileHash = calculateFileHash(file)
                if (maliciousHashes.contains(fileHash)) {
                    threatScore += 80
                    threats.add("Known malicious file hash: $fileHash")
                }
            }

            // Check file location
            val filePath = file.absolutePath.lowercase()
            if (filePath.contains("/download") || filePath.contains("/temp") ||
                filePath.contains("/cache") || filePath.contains("/tmp")) {
                threatScore += 10
                threats.add("File in suspicious location: ${file.parent}")
            }

            // Check for hidden files with executable extensions
            if (file.name.startsWith(".") && extension in setOf("apk", "exe", "sh", "bat")) {
                threatScore += 35
                threats.add("Hidden executable file")
            }

            // Only return files with significant threat score
            return if (threatScore >= 25) {
                MaliciousFile(
                    file = file,
                    threatScore = threatScore.coerceAtMost(100),
                    threats = threats,
                    fileSize = fileSize,
                    lastModified = file.lastModified(),
                    isSelected = false
                )
            } else null

        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing file: ${file.absolutePath}", e)
            return null
        }
    }

    private fun calculateFileHash(file: File): String {
        return try {
            val digest = MessageDigest.getInstance("MD5")
            file.inputStream().use { input ->
                val buffer = ByteArray(8192)
                var bytesRead: Int
                while (input.read(buffer).also { bytesRead = it } != -1) {
                    digest.update(buffer, 0, bytesRead)
                }
            }
            digest.digest().joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Log.e(TAG, "Error calculating file hash", e)
            ""
        }
    }

    private fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "$bytes B"
            bytes < 1024 * 1024 -> "${bytes / 1024} KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)} MB"
            else -> "${bytes / (1024 * 1024 * 1024)} GB"
        }
    }

    private fun showFileDetails(maliciousFile: MaliciousFile) {
        val file = maliciousFile.file
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

        val details = buildString {
            appendLine("File Details:")
            appendLine("Name: ${file.name}")
            appendLine("Path: ${file.absolutePath}")
            appendLine("Size: ${formatFileSize(maliciousFile.fileSize)}")
            appendLine("Last Modified: ${dateFormat.format(Date(maliciousFile.lastModified))}")
            appendLine("Threat Score: ${maliciousFile.threatScore}/100")
            appendLine()
            appendLine("Detected Threats:")
            maliciousFile.threats.forEach { threat ->
                appendLine("• $threat")
            }
        }

        android.app.AlertDialog.Builder(requireContext())
            .setTitle("Malicious File Details")
            .setMessage(details)
            .setPositiveButton("OK", null)
            .setNegativeButton("Delete File") { _, _ ->
                deleteFile(maliciousFile)
            }
            .show()
    }

    private fun deleteFile(maliciousFile: MaliciousFile) {
        try {
            if (maliciousFile.file.delete()) {
                maliciousFilesList.remove(maliciousFile)
                maliciousFilesAdapter.notifyDataSetChanged()
                updateUI()
                Toast.makeText(requireContext(), "File deleted successfully", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(requireContext(), "Failed to delete file", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Toast.makeText(requireContext(), "Error deleting file: ${e.message}", Toast.LENGTH_SHORT).show()
            Log.e(TAG, "Error deleting file", e)
        }
    }

    private fun refreshScan() {
        maliciousFilesList.clear()
        maliciousFilesAdapter.notifyDataSetChanged()
        updateUI()
        startFileScan()
    }

    private fun clearResults() {
        maliciousFilesList.clear()
        maliciousFilesAdapter.notifyDataSetChanged()
        updateUI()
        binding.tvStatus.text = "Results cleared. Ready to scan again."
    }

    private fun deleteSelectedFiles() {
        val selectedFiles = maliciousFilesList.filter { it.isSelected }
        if (selectedFiles.isEmpty()) {
            Toast.makeText(requireContext(), "No files selected", Toast.LENGTH_SHORT).show()
            return
        }

        android.app.AlertDialog.Builder(requireContext())
            .setTitle("Delete Selected Files")
            .setMessage("Are you sure you want to delete ${selectedFiles.size} selected files? This action cannot be undone.")
            .setPositiveButton("Delete") { _, _ ->
                var deletedCount = 0
                selectedFiles.forEach { maliciousFile ->
                    try {
                        if (maliciousFile.file.delete()) {
                            maliciousFilesList.remove(maliciousFile)
                            deletedCount++
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error deleting file: ${maliciousFile.file.name}", e)
                    }
                }
                maliciousFilesAdapter.notifyDataSetChanged()
                updateUI()
                Toast.makeText(requireContext(), "Deleted $deletedCount files", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun updateUI() {
        val totalFiles = maliciousFilesList.size
        val highThreatFiles = maliciousFilesList.count { it.threatScore >= 75 }
        val mediumThreatFiles = maliciousFilesList.count { it.threatScore in 50..74 }
        val lowThreatFiles = maliciousFilesList.count { it.threatScore in 25..49 }

        binding.tvTotalFiles.text = totalFiles.toString()
        binding.tvHighThreat.text = highThreatFiles.toString()
        binding.tvMediumThreat.text = mediumThreatFiles.toString()
        binding.tvLowThreat.text = lowThreatFiles.toString()

        // Show/hide empty state
        if (totalFiles == 0) {
            binding.layoutEmptyState.visibility = View.VISIBLE
            binding.recyclerViewMaliciousFiles.visibility = View.GONE
            binding.btnDeleteSelected.visibility = View.GONE
        } else {
            binding.layoutEmptyState.visibility = View.GONE
            binding.recyclerViewMaliciousFiles.visibility = View.VISIBLE
            binding.btnDeleteSelected.visibility = View.VISIBLE
        }
    }

    private fun updateEmptyState(message: String) {
        binding.layoutEmptyState.visibility = View.VISIBLE
        binding.recyclerViewMaliciousFiles.visibility = View.GONE
        binding.btnDeleteSelected.visibility = View.GONE
        binding.tvStatus.text = message
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
