<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:gravity="center_vertical"
    android:background="?attr/selectableItemBackground">

    <ImageView
        android:id="@+id/ivIcon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="16dp"
        app:tint="?attr/colorPrimary" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="?attr/colorOnSurface"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tvDescription"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:visibility="gone" />

    </LinearLayout>

    <ImageView
        android:id="@+id/ivArrow"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@drawable/ic_arrow_right"
        android:visibility="gone"
        app:tint="?attr/colorOnSurfaceVariant" />

</LinearLayout>
