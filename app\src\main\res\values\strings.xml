<resources>
    <string name="app_name">Ek<PERSON>yu</string>
    <string name="permission">Permission</string>
    <string name="accessibility_service_gmail">Monitors for mail in Gmail.</string>
    <string name="accessibility_service_yahoo">Monitors for mail in yahoo.</string>
    <string name="accessibility_service_outlook">Monitors for mail in outlook.</string>
    <string name="sms_accessibility_service_description">Monitors SMS messages for security threats and suspicious content to protect you from phishing and scam messages.</string>
    <string name="warning_this_email_is_unsafe_proceed_with_caution">Warning: This email is unsafe! Proceed with caution.</string>
    <string name="close">Close</string>
    <string name="continuee">Continue</string>
    <string name="google_client_id">408481275944-9clep0h2rardm0f07ugg6f765ik6bpnb.apps.googleusercontent.com</string>
    <string name="appAuthRedirectScheme">https://ekvayuy.firebaseapp.com/__/auth/handler</string>
    <string name="gmail_auth">Gmail Auth</string>
    <string name="yahoo_auth">Yahoo Auth</string>
    <string name="secure_my_app_from_phishing">Shield your inbox from phishing, malicious emails, and unauthorized access with robust security 🔒 🛡️ 🔐 🔑 measures to keep your personal information safe and secure.</string>
    <string name="default_web_client_id" translatable="false">206367517092-15gou6mdicehhh21r1f6ru1suolkglab.apps.googleusercontent.com</string>
    <string name="device_details">Device Details</string>

    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="safe">safe</string>
    <string name="unsafe">unsafe</string>
    <string name="submit">Submit</string>
    <string name="refresh">Refresh</string>
    <string name="raise_dispute">Raise Dispute</string>
    <string name="basic_information">Basic Information</string>
    <string name="manufacturer_name">Manufacturer Name</string>
    <string name="android_version">Android Version</string>
    <string name="device_name">Device Name</string>
    <string name="manufacturer">Manufacturer</string>
    <string name="model_name">Model Name</string>
    <string name="brand_name">Brand Name</string>
    <string name="product">Product</string>
    <string name="api_level">Api Level</string>
    <string name="build_number">Build Number</string>
    <string name="serial_number">Serial Number</string>
    <string name="android_id">Android Id</string>
    <string name="screen_info">Screen Info</string>
    <string name="android_info">Android Info</string>
    <string name="resolution">Resolution</string>
    <string name="density_factor">Density Factor</string>
    <string name="scaled_density">Scaled Density</string>
    <string name="app_info">App Info</string>
    <string name="version">Version</string>
    <string name="min_sdk">Min SDK</string>
    <string name="get_stared_raised_disputes_against">Get Started! Raised disputes against</string>
    <string name="message_id">Message Id</string>
    <string name="sender_email">Sender Email</string>
    <string name="status">Status</string>
    <string name="counter">Counter</string>
    <string name="reason_of_raising_dispute">Reason Of Raising Dispute</string>
    <string name="to_extract_email_details_securely">To extract email details securely, we need Accessibility Service permission. This helps us identify and analyze email content directly from your mail app to keep you safe. No personal data is stored or shared.</string>
    <string name="i_am_facing_difficulties_retrieving_the_recipient_s_email_id_could_you_kindly_enable_the_service_and_assist_in_verifying_the_mail_for_a_timely_resolution">I am facing difficulties retrieving the recipient\'s email ID. Could you kindly enable the service and assist in verifying the mail for a timely resolution?</string>


</resources>