package com.tech.ekvayu.BaseClass

import android.Manifest
import android.annotation.SuppressLint
import android.app.ActivityManager
import android.content.Context
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.wifi.WifiManager
import android.os.BatteryManager
import android.os.Build
import android.provider.Settings
import android.telephony.TelephonyManager
import android.util.DisplayMetrics
import android.view.WindowManager
import androidx.core.app.ActivityCompat
import com.tech.ekvayu.models.AppInfo
import com.tech.ekvayu.models.BatteryInfo
import com.tech.ekvayu.models.DeviceInfo
import com.tech.ekvayu.models.MemoryInfo
import com.tech.ekvayu.models.NetworkInfo
import com.tech.ekvayu.models.ScreenInfo
import com.tech.ekvayu.models.StorageInfo
import com.tech.ekvayu.models.SystemInfo
import com.tech.ekvayu.models.TelephonyInfo
import java.net.NetworkInterface
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlin.collections.iterator

class DeviceInfoHelper(private val context: Context) {

    fun getDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            deviceName = getDeviceName(),
            manufacturer = Build.MANUFACTURER,
            model = Build.MODEL,
            brand = Build.BRAND,
            product = Build.PRODUCT,
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            buildNumber = Build.DISPLAY,
            serialNumber = getSerialNumber(),
            androidId = getAndroidId(),
            screenInfo = getScreenInfo(),
            memoryInfo = getMemoryInfo(),
            storageInfo = getStorageInfo(),
            networkInfo = getNetworkInfo(),
            telephonyInfo = getTelephonyInfo(),
            batteryInfo = getBatteryInfo(),
            systemInfo = getSystemInfo(),
            appInfo = getAppInfo(),
            timestamp = getCurrentTimestamp()
        )
    }

    private fun getDeviceName(): String {
        return try {
            Settings.Global.getString(context.contentResolver, "device_name")
                ?: "${Build.MANUFACTURER} ${Build.MODEL}"
        } catch (e: Exception) {
            "${Build.MANUFACTURER} ${Build.MODEL}"
        }
    }

    @SuppressLint("HardwareIds", "MissingPermission")
    private fun getSerialNumber(): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                if (ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE)
                    == PackageManager.PERMISSION_GRANTED) {
                    try {
                        Build.getSerial()
                    } catch (e: SecurityException) {
                        "Permission Denied"
                    }
                } else {
                    "Permission Required"
                }
            } else {
                @Suppress("DEPRECATION")
                Build.SERIAL
            }
        } catch (e: Exception) {
            "Unknown"
        }
    }

    @SuppressLint("HardwareIds")
    private fun getAndroidId(): String {
        return try {
            Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        } catch (e: Exception) {
            "Unknown"
        }
    }

    private fun getScreenInfo(): ScreenInfo {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics = DisplayMetrics()

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Use WindowMetrics for Android R and above
            val windowMetrics = windowManager.currentWindowMetrics
            val bounds = windowMetrics.bounds
            context.resources.displayMetrics.let { metrics ->
                ScreenInfo(
                    width = bounds.width(),
                    height = bounds.height(),
                    density = metrics.density,
                    densityDpi = metrics.densityDpi,
                    scaledDensity = metrics.scaledDensity
                )
            }
        } else {
            @Suppress("DEPRECATION")
            val display = windowManager.defaultDisplay
            @Suppress("DEPRECATION")
            display.getRealMetrics(displayMetrics)
            ScreenInfo(
                width = displayMetrics.widthPixels,
                height = displayMetrics.heightPixels,
                density = displayMetrics.density,
                densityDpi = displayMetrics.densityDpi,
                scaledDensity = displayMetrics.scaledDensity
            )
        }
    }

    private fun getMemoryInfo(): MemoryInfo {
        val runtime = Runtime.getRuntime()
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memInfo)

        return MemoryInfo(
            totalRAM = memInfo.totalMem,
            availableRAM = memInfo.availMem,
            usedRAM = memInfo.totalMem - memInfo.availMem,
            lowMemory = memInfo.lowMemory,
            heapSize = runtime.totalMemory(),
            heapFree = runtime.freeMemory(),
            heapUsed = runtime.totalMemory() - runtime.freeMemory(),
            heapMax = runtime.maxMemory()
        )
    }

    private fun getStorageInfo(): StorageInfo {
        val internalDir = context.filesDir
        val totalSpace = internalDir.totalSpace
        val freeSpace = internalDir.freeSpace
        val usedSpace = totalSpace - freeSpace

        return StorageInfo(
            totalInternal = totalSpace,
            freeInternal = freeSpace,
            usedInternal = usedSpace
        )
    }

    private fun getNetworkInfo(): NetworkInfo {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager

        val isConnected = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities != null && (
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
            )
        } else {
            @Suppress("DEPRECATION")
            connectivityManager.activeNetworkInfo?.isConnected ?: false
        }

        val connectionType = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            when {
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true -> "WiFi"
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true -> "Mobile Data"
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) == true -> "Ethernet"
                else -> "Unknown"
            }
        } else {
            @Suppress("DEPRECATION")
            connectivityManager.activeNetworkInfo?.typeName ?: "Unknown"
        }

        return NetworkInfo(
            isConnected = isConnected,
            connectionType = connectionType,
            wifiEnabled = wifiManager.isWifiEnabled,
            ipAddress = getIPAddress()
        )
    }

    private fun getIPAddress(): String {
        return try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            for (networkInterface in interfaces) {
                val addresses = networkInterface.inetAddresses
                for (address in addresses) {
                    if (!address.isLoopbackAddress && address.hostAddress?.contains(':') == false) {
                        return address.hostAddress ?: "Unknown"
                    }
                }
            }
            "Unknown"
        } catch (e: Exception) {
            "Unknown"
        }
    }

    @SuppressLint("MissingPermission")
    private fun getTelephonyInfo(): TelephonyInfo {
        val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager

        return if (ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE)
            == PackageManager.PERMISSION_GRANTED) {
            TelephonyInfo(
                carrierName = telephonyManager.networkOperatorName,
                countryCode = telephonyManager.networkCountryIso,
                networkType = getNetworkType(telephonyManager.networkType),
                phoneType = getPhoneType(telephonyManager.phoneType),
                hasPermission = true
            )
        } else {
            TelephonyInfo(
                carrierName = "Permission Required",
                countryCode = "Permission Required",
                networkType = "Permission Required",
                phoneType = "Permission Required",
                hasPermission = false
            )
        }
    }

    private fun getNetworkType(networkType: Int): String {
        return when (networkType) {
            TelephonyManager.NETWORK_TYPE_GPRS -> "GPRS"
            TelephonyManager.NETWORK_TYPE_EDGE -> "EDGE"
            TelephonyManager.NETWORK_TYPE_UMTS -> "UMTS"
            TelephonyManager.NETWORK_TYPE_HSDPA -> "HSDPA"
            TelephonyManager.NETWORK_TYPE_HSUPA -> "HSUPA"
            TelephonyManager.NETWORK_TYPE_HSPA -> "HSPA"
            TelephonyManager.NETWORK_TYPE_LTE -> "LTE"
            TelephonyManager.NETWORK_TYPE_NR -> "5G"
            else -> "Unknown ($networkType)"
        }
    }

    private fun getPhoneType(phoneType: Int): String {
        return when (phoneType) {
            TelephonyManager.PHONE_TYPE_GSM -> "GSM"
            TelephonyManager.PHONE_TYPE_CDMA -> "CDMA"
            TelephonyManager.PHONE_TYPE_SIP -> "SIP"
            else -> "Unknown ($phoneType)"
        }
    }

    private fun getBatteryInfo(): BatteryInfo {
        val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager

        return BatteryInfo(
            level = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY),
            isCharging = batteryManager.isCharging,
            temperature = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW),
            voltage = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_AVERAGE)
        )
    }

    private fun getSystemInfo(): SystemInfo {
        return SystemInfo(
            bootloader = Build.BOOTLOADER,
            hardware = Build.HARDWARE,
            fingerprint = Build.FINGERPRINT,
            host = Build.HOST,
            id = Build.ID,
            tags = Build.TAGS,
            type = Build.TYPE,
            user = Build.USER,
            kernelVersion = System.getProperty("os.version") ?: "Unknown",
            javaVmVersion = System.getProperty("java.vm.version") ?: "Unknown"
        )
    }

    private fun getAppInfo(): AppInfo {
        val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)

        return AppInfo(
            packageName = context.packageName,
            versionName = packageInfo.versionName ?: "Unknown",
            versionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.longVersionCode
            } else {
                @Suppress("DEPRECATION")
                packageInfo.versionCode.toLong()
            },
            targetSdkVersion = packageInfo.applicationInfo?.targetSdkVersion ?: 0,
            minSdkVersion = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                packageInfo.applicationInfo?.minSdkVersion ?: "Unknown"
            } else {
                "Unknown"
            }
        )
    }

    private fun getCurrentTimestamp(): String {
        val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return formatter.format(Date())
    }
}