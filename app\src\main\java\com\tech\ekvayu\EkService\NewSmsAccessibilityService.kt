package com.tech.ekvayu.EkService

import android.accessibilityservice.AccessibilityService
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo


class NewSmsAccessibilityService : AccessibilityService() {
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        Log.d("eventType", "onAccessibilityEvent: "+event?.eventType)
      /*  when (event?.eventType) {
            AccessibilityEvent.TYPE_VIEW_CLICKED -> {
                val rootNode = rootInActiveWindow
                if (rootNode != null) {
                    Log.d("SmsService", "Dumping SMS screen tree...")
                    logNodeTree(rootNode)  // 👈 print the full UI hierarchy
                }
            }
        }*/
        if (event?.eventType==2048)
        {
            val rootNode = rootInActiveWindow
            if (rootNode != null) {
                Log.d("SmsService", "Dumping SMS screen tree...")
                logNodeTree(rootNode)
            }
        }
    }


    private fun logNodeTree(node: AccessibilityNodeInfo?, depth: Int = 0) {
        if (node == null) return

        val prefix = " ".repeat(depth * 2)
        Log.d("SmsServiceTree",
            "$prefix Class: ${node.className}, " +
                    "Text: ${node.text}, " +
                    "ContentDesc: ${node.contentDescription}, " +
                    "ViewId: ${node.viewIdResourceName}"
        )

        for (i in 0 until node.childCount) {
            logNodeTree(node.getChild(i), depth + 1)
        }

    }


    private fun extractSmsContent(node: AccessibilityNodeInfo?) {
        if (node == null) return

        // We only care about SMS text nodes
        if (node.className == "android.widget.TextView" &&
            node.viewIdResourceName?.contains("message_text") == true
        ) {
            val smsText = node.text?.toString() ?: ""
            val contentDesc = node.contentDescription?.toString() ?: ""

            // Extract date/time from contentDesc (after the last occurrence of message text)
            val dateTime = if (contentDesc.contains(smsText)) {
                contentDesc.substringAfter(smsText).trim()
            } else {
                "" // fallback
            }

            Log.d("SmsServiceExtract", "Message: $smsText")
            Log.d("SmsServiceExtract", "Time: $dateTime")
        }

        // Keep traversing children
        for (i in 0 until node.childCount) {
            extractSmsContent(node.getChild(i))
        }
    }

    private fun parseNodeLog(log: String) {
        // Extract "Text: ..." part
        val textRegex = Regex("Text: (.*?), ContentDesc")
        val smsContent = textRegex.find(log)?.groupValues?.get(1)?.trim()

        // Extract time from "ContentDesc"
        val timeRegex = Regex("(\\d{1,2} \\w+, \\d{1,2}:\\d{2}\\s?(?:am|pm))")
        val smsTime = timeRegex.find(log)?.groupValues?.get(1)?.trim()

        Log.d("SmsParsed", "SMS Content: $smsContent")
        Log.d("SmsParsed", "SMS Time: $smsTime")
    }


    private fun extractSmsData(rootNode: AccessibilityNodeInfo?) {
        if (rootNode == null) return

        val nodes = rootNode.findAccessibilityNodeInfosByViewId(
            "com.google.android.apps.messaging:id/message_text"
        )

        for (node in nodes) {
            val message = node.text?.toString() ?: ""
            val contentDesc = node.contentDescription?.toString() ?: ""

            // Extract timestamp from contentDesc (last part after message)
            val timeRegex = Regex("(\\d{1,2}\\s\\w+,\\s\\d{1,2}:\\d{2} (?:am|pm))")
            val time = timeRegex.find(contentDesc)?.value ?: ""

            Log.d("SmsParser", "Message: $message, Time: $time")
        }
    }


    override fun onInterrupt() {

    }




}